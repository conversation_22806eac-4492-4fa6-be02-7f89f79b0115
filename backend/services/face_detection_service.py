import cv2
import numpy as np


class FaceDetectionService:
    def __init__(
        self,
        face_cascade_path="services/face_models/haarcascade_frontalface_default.xml",
        eye_cascade_path="services/face_models/haarcascade_eye.xml",
    ):
        # Load Haar cascade classifiers
        self.face_cascade = cv2.CascadeClassifier(face_cascade_path)
        self.eye_cascade = cv2.CascadeClassifier(eye_cascade_path)

    def detect(self, img):
        """
        Detect face and eyes in the given image and return warnings if:
        - No face is detected
        - Face is not centered
        - Eyes are not detected or appear to be looking away
        """
        gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
        faces = self.face_cascade.detectMultiScale(
            gray, scaleFactor=1.1, minNeighbors=5, minSize=(80, 80)
        )

        if len(faces) == 0:
            return {
                "status": "warning",
                "message": "No face detected. Please position your face inside the frame.",
            }

        # Use the first detected face
        x, y, w, h = faces[0]
        face_center_x = x + w / 2
        image_width = img.shape[1]
        # Define acceptable horizontal bounds (30% - 70% of image width)
        left_bound = image_width * 0.30
        right_bound = image_width * 0.70
        movement_margin = image_width * 0.10

        adjusted_left = left_bound - movement_margin
        adjusted_right = right_bound + movement_margin

        if face_center_x < adjusted_left or face_center_x > adjusted_right:
            return {
                "status": "warning",
                "message": "Face detected but outside the allowed area. Please look directly at the screen.",
            }

        # Now, check the eyes within the face region
        face_roi_gray = gray[y : y + h, x : x + w]
        eyes = self.eye_cascade.detectMultiScale(
            face_roi_gray, scaleFactor=1.1, minNeighbors=5
        )

        if len(eyes) < 2:
            return {
                "status": "warning",
                "message": "Face detected but both eyes were not clearly detected. Please adjust your face.",
            }

        # Check each eye for gaze direction using simple pupil localization
        eye_warnings = []
        # for idx, (ex, ey, ew, eh) in enumerate(eyes):
        #     eye_roi = face_roi_gray[ey:ey+eh, ex:ex+ew]
        #     blurred = cv2.GaussianBlur(eye_roi, (7, 7), 0)
        #     _, thresh = cv2.threshold(blurred, 30, 255, cv2.THRESH_BINARY_INV)
        #     contours, _ = cv2.findContours(thresh, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

        #     if contours:
        #         pupil_contour = max(contours, key=cv2.contourArea)
        #         M = cv2.moments(pupil_contour)
        #         if M["m00"] != 0:
        #             cx = int(M["m10"] / M["m00"])
        #             # Heuristic: Check pupil's horizontal position in the eye region
        #             if cx < ew * 0.3:
        #                 eye_warnings.append(f"Eye {idx+1} appears to be looking left.")
        #             elif cx > ew * 0.7:
        #                 eye_warnings.append(f"Eye {idx+1} appears to be looking right.")
        #         else:
        #             eye_warnings.append(f"Eye {idx+1} pupil detection issue.")
        #     else:
        #         eye_warnings.append(f"Eye {idx+1} pupil not detected.")

        if eye_warnings:
            return {"status": "warning", "message": " ".join(eye_warnings)}

        return {
            "status": "ok",
            "message": "Face and eyes detected properly. Please continue.",
        }
