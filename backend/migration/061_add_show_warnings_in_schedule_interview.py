"""
Peewee migrations -- 061_add_show_warnings_in_schedule_interview.py.
"""

from contextlib import suppress
import peewee as pw
from peewee_migrate import Migrator

with suppress(ImportError):
    import playhouse.postgres_ext as pw_pext


def migrate(migrator: Migrator, database: pw.Database, *, fake=False):
    """Add 'show_warnings' column to schedule_interviews table."""
    sql_query = """
        ALTER TABLE schedule_interviews
        ADD COLUMN show_warnings BOOLEAN DEFAULT TRUE;
    """
    migrator.sql(sql_query)


def rollback(migrator: Migrator, database: pw.Database, *, fake=False):
    """Remove 'show_warnings' column from schedule_interviews table."""
    rollback_query = """
        ALTER TABLE schedule_interviews
        DROP COLUMN show_warnings;
    """
    migrator.sql(rollback_query)
