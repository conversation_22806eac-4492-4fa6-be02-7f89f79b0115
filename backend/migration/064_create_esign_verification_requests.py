"""
Peewee migrations -- 064_create_esign_verification_requests.py.
"""

from contextlib import suppress

import peewee as pw
from peewee_migrate import Migra<PERSON>


with suppress(ImportError):
    import playhouse.postgres_ext as pw_pext


def migrate(migrator: Migrator, database: pw.Database, *, fake=False):
    """Create esign_verification_requests table."""
    sql_query = """
        CREATE TABLE esign_verification_requests (
            id BIGINT NOT NULL AUTO_INCREMENT PRIMARY KEY,
            employee_id BIGINT NOT NULL,
            candidate_id BIGINT NOT NULL,
            reason VARCHAR(255),
            otp VARCHAR(10) NOT NULL,
            otp_expire_at DATETIME DEFAULT NULL,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            FOR<PERSON><PERSON><PERSON>Y (employee_id) REFERENCES employees(id),
            FOREI<PERSON><PERSON> KEY (candidate_id) REFERENCES candidates(id)
        );
    """
    migrator.sql(sql_query)


def rollback(migrator: Migrator, database: pw.Database, *, fake=False):
    """Drop esign_verification_requests table."""
    migrator.sql("DROP TABLE IF EXISTS `esign_verification_requests`;")
