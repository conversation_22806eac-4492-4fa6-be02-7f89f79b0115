from fastapi import APIRouter, Depends, HTTPException, status
from fastapi.openapi.docs import get_swagger_ui_html, get_redoc_html
from fastapi.openapi.utils import get_openapi
from fastapi.security import HTT<PERSON><PERSON>asic, HTTPBasicCredentials
import secrets
from app.config import SWAGGER_USERNAME, SWAGGER_PASSWORD

security = HTTPBasic()
docs_router = APIRouter()


def check_swagger_auth(credentials: HTTPBasicCredentials = Depends(security)):
    correct_username = secrets.compare_digest(credentials.username, SWAGGER_USERNAME)
    correct_password = secrets.compare_digest(credentials.password, SWAGGER_PASSWORD)
    if not (correct_username and correct_password):
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Unauthorized",
            headers={"WWW-Authenticate": "Basic"},
        )


@docs_router.get("/api/docs", include_in_schema=False)
def custom_swagger_ui(credentials: HTTPBasicCredentials = Depends(check_swagger_auth)):
    return get_swagger_ui_html(
        openapi_url="/openapi.json", title="RecruiteasePro API Docs"
    )


@docs_router.get("/openapi.json", include_in_schema=False)
def openapi_json(credentials: HTTPBasicCredentials = Depends(check_swagger_auth)):
    from main import app  # or wherever your FastAPI app is

    return get_openapi(title=app.title, version="1.0.0", routes=app.routes)


@docs_router.get("/api/redocs", include_in_schema=False)
def custom_redoc(credentials: HTTPBasicCredentials = Depends(check_swagger_auth)):
    return get_redoc_html(openapi_url="/openapi.json", title="RecruiteasePro API ReDoc")
