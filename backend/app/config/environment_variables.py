"""
File conatins database connection URL
"""

from dotenv import load_dotenv
import os

load_dotenv()

db_url = os.environ.get("DATABASE_URL", None)

SMTP_API_KEY = os.environ.get("SMTP_API_KEY", "api-key")
SMTP_PASSWORD = os.environ.get("SMTP_PASSWORD", "api-passwprd")
SMTP_FROM_EMAIL = os.environ.get("SMTP_FROM_EMAIL", "email-address")
SMTP_SERVER = os.environ.get("SMTP_SERVER", "smtp-server")
SMTP_PORT = os.environ.get("SMTP_PORT", "587")

ALLOWED_ORIGINS = (os.environ.get("ALLOWED_ORIGINS", "*")).split(",")

PYTHON_ENV = os.environ.get("PYTHON_ENV", "development")
HOST = os.environ.get("HOST", "127.0.0.1")
PORT = int(os.environ.get("PORT", "4000"))
PYTHON_WORKER = os.environ.get("PYTHON_WORKER", "1")

APP_URL = os.environ.get("APP_URL", "")
API_URL = os.environ.get("API_URL", f"http://{HOST}:{PORT}")
BASE_DOMAIN = os.environ.get("BASE_DOMAIN", f"{HOST}:{PORT}")
OPENAI_KEY = os.environ.get("OPENAI_KEY", "")

S3_ENDPOINT = os.environ.get("S3_ENDPOINT", "")
S3_BUCKET_NAME = os.environ.get("S3_BUCKET_NAME", "")
S3_ACCESS_KEY = os.environ.get("S3_ACCESS_KEY", "")
S3_SECRET_KEY = os.environ.get("S3_SECRET_KEY", "")
S3_REGION = os.environ.get("S3_STORAGE_REGION", "")
UPLOAD_TO_S3 = os.environ.get("UPLOAD_TO_S3", None)
SWAGGER_USERNAME = os.environ.get("SWAGGER_USERNAME", "ats")
SWAGGER_PASSWORD = os.environ.get("SWAGGER_PASSWORD", "ats123")
STRIPE_SECRET_KEY = os.environ.get("STRIPE_SECRET_KEY", "")
STRIPE_WEBHOOK_SECRET = os.environ.get("STRIPE_WEBHOOK_SECRET", "")

SENSITIVE_PARAMS = (
    os.environ.get("SENSITIVE_PARAMS", "password, confirm_password, otp, secret")
).split(",")
CC_EMAILS = os.environ.get("CC_EMAILS", "")
PRODUCTION_ENV = PYTHON_ENV == "production"
ENQUERY_EMAILS = os.environ.get("ENQUERY_EMAILS", None) or CC_EMAILS
REDIS_URL = os.environ.get("REDIS_URL", "redis://localhost:6379/1")

COMPILER_BASE_URL = os.environ.get(
    "COMPILER_BASE_URL", "http://ats-code-execution:4000"
)

CELERY_FLOWER_USER = os.environ.get("CELERY_FLOWER_PASSWORD", "admin")
CELERY_FLOWER_PASSWORD = os.environ.get("CELERY_FLOWER_PASSWORD", "ats")

FERNET_KEY = os.environ.get("FERNET_KEY", "12345678901234567890123456789012")

if db_url:
    DATABASE_URL = f"{db_url}"
else:
    # Construct DATABASE_URL using other environment variables
    db_user = os.environ.get("DATABASE_USER", "root")
    db_password = os.environ.get("DATABASE_PASSWORD", "password")
    db_name = os.environ.get("DATABASE_NAME", "ats")
    db_host = os.environ.get("DB_HOST", "localhost")
    db_port = os.environ.get("DB_PORT", "3306")
    DATABASE_URL = f"mysql://{db_user}:{db_password}@{db_host}:{db_port}/{db_name}"
