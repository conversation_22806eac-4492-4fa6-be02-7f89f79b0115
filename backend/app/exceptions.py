from fastapi import Request, HTTPException, status as HTTPStatus
from fastapi.encoders import jsonable_encoder
from fastapi.responses import JSONResponse, PlainTextResponse
from peewee import IntegrityError
from app.config import logger


class RecordNotFoundException(Exception):
    """Exception raised when a record is not found."""

    def __init__(self, message="Record not found"):
        self.message = message
        super().__init__(self.message)


class CustomValidationError(Exception):
    """Exception raised for custom validation errors."""

    def __init__(self, error="Record not found", status_code=404):
        self.error = error
        self.status_code = status_code
        super().__init__(self.error)


class UnauthorizedException(Exception):
    """Exception raised for unauthorized access attempts."""

    def __init__(self, error="Unauthorized"):
        self.error = error
        super().__init__(self.error)


class MethodNotAllowedException(Exception):
    """Exception raised for method not allowed access attempts."""

    def __init__(self, error="Method Not Allowed"):
        self.error = error
        super().__init__(self.error)


#  Custom exception handler for HTTPException
async def custom_http_exception_handler(request: Request, exc: HTTPException):
    if request.url.path.startswith(("/api/docs", "/openapi.json", "/api/redocs")):
        # Let FastAPI handle it normally (returns the browser's auth prompt)
        return PlainTextResponse(
            str(exc.detail),
            status_code=exc.status_code,
            headers=exc.headers,  # ensure WWW-Authenticate is preserved
        )

    return JSONResponse(
        status_code=exc.status_code,
        content=jsonable_encoder(
            {"success": False, "status_code": exc.status_code, "error": f"{exc.detail}"}
        ),
    )


async def custom_record_not_found_exception_handler(
    request: Request, e: RecordNotFoundException
):
    logger.error(f"RecordNotFoundException Exception: {e}")
    return JSONResponse(
        status_code=HTTPStatus.HTTP_404_NOT_FOUND,
        content=jsonable_encoder(
            {
                "success": False,
                "status_code": HTTPStatus.HTTP_404_NOT_FOUND,
                "error": str(e),
            }
        ),
    )


async def custom_validation_exception_handler(
    request: Request, e: CustomValidationError
):
    logger.error(f"CustomValidationError Exception: {e} - 422 Unprocessable Entity")
    return JSONResponse(
        status_code=HTTPStatus.HTTP_422_UNPROCESSABLE_ENTITY,
        content=jsonable_encoder(
            {
                "success": False,
                "status_code": HTTPStatus.HTTP_422_UNPROCESSABLE_ENTITY,
                "error": str(e),
            }
        ),
    )


async def custom_integrity_exception_handler(request: Request, e: IntegrityError):
    logger.error(f"IntegrityError Exception: {e} - 422 Unprocessable Entity")
    return JSONResponse(
        status_code=HTTPStatus.HTTP_422_UNPROCESSABLE_ENTITY,
        content=jsonable_encoder(
            {
                "success": False,
                "status_code": HTTPStatus.HTTP_422_UNPROCESSABLE_ENTITY,
                "error": str(e),
            }
        ),
    )


async def unauthorized_exception_handler(request: Request, e: UnauthorizedException):
    return JSONResponse(
        status_code=HTTPStatus.HTTP_401_UNAUTHORIZED,
        content=jsonable_encoder(
            {
                "success": False,
                "status_code": HTTPStatus.HTTP_401_UNAUTHORIZED,
                "error": str(e),
            }
        ),
    )


async def method_not_allowed_exception_handler(
    request: Request, e: MethodNotAllowedException
):
    logger.error(f"MethodNotAllowedException Exception: {e} 405 method not allowed")
    return JSONResponse(
        status_code=HTTPStatus.HTTP_405_METHOD_NOT_ALLOWED,
        content=jsonable_encoder(
            {
                "success": False,
                "status_code": HTTPStatus.HTTP_405_METHOD_NOT_ALLOWED,
                "error": str(e),
            }
        ),
    )
