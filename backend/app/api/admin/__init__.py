# Import all routers from the API directory
from app.api.admin.registration_controller import router as registration_router
from app.api.admin.auth_controller import router as auth_router
from app.api.admin.businesses_controller import router as businesses_router
from app.api.admin.dashboard_controller import router as dashboard_router
from app.api.admin.users_contoller import router as user_router
from app.api.admin.setting_controller import router as setting_router
from app.api.admin.payment_controller import router as payment_router
from fastapi import APIRouter


# Create a new APIRouter instance to combine all routers
admin_router = APIRouter(prefix="/admin")

# Include all routes from authentication
admin_router.include_router(auth_router)

# Include all routes from registration
admin_router.include_router(registration_router)

# Include all routes from businesses
admin_router.include_router(businesses_router)

# Include all routes from dashboard
admin_router.include_router(dashboard_router)

# Include all routes from payments
admin_router.include_router(payment_router)

# Include all routes from user
admin_router.include_router(user_router)

# Include all routes from dashboard
admin_router.include_router(setting_router)

# Include all routes from session
# api_router.include_router(session.router)
