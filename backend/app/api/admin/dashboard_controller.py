from fastapi import APIRouter, Depends, HTTPException, status, Query
from app.schema import SuccessResponse
from app.models import User, Business, Employee, EmployeeRole
from app.helper import AdminAuthHelper
from datetime import datetime, timedelta
from app.models import User, Candidate, Opportunity
from typing import Optional
from app.models.helper import CandidateHelper
from app.constants import Constant

# Initialize the APIRouter with a tag for categorizing endpoints in the documentation.
router = APIRouter(
    prefix="/dashboard",
    tags=["Admin Dashboard API"],
    dependencies=[Depends(AdminAuthHelper.get_current_auth_token)],
)

# ------------------------------ functions ------------------------------


@router.get(
    "",
    summary="List of Dashboard Stats",
    description="List of Dashboard Stats.",
    response_model=SuccessResponse,
)
async def get_dashboard_stats(user: User = Depends(AdminAuthHelper.get_admin_user)):
    """
    Retrieve dashboard statistics.

    Args:
    - user: The authenticated admin user, injected by dependency injection.

    Returns:
    - A response model containing a message and a list of stats.
    """
    try:
        # Fetch your dashboard statistics from your data source.
        # Here, dummy data is used for demonstration purposes.

        today = datetime.now()
        start_of_week = today - timedelta(days=today.weekday() + 7)
        end_of_week = start_of_week + timedelta(days=7)

        if user.is_super_admin:
            business_query = Business.select().where(Business.status == 1)
        else:
            business_query = Business.select().where(
                Business.status == 1, Business.user_id == user.id
            )

        business_ids = Business.pluck_from_query(business_query, "id")
        total_businesses = business_query.count()

        employee_roles = EmployeeRole.select().where(
            (EmployeeRole.name != Constant.SUPER_ADMIN)
            & (
                (EmployeeRole.business_id.in_(business_ids))
                | (EmployeeRole.business_id.is_null())
            )
        )

        employee_role_ids = EmployeeRole.pluck_from_query(employee_roles, "id")

        total_users = (
            Employee.select()
            .where(
                Employee.business_id.in_(business_ids),
                Employee.status == 1,
                Employee.employee_role_id.in_(employee_role_ids),
            )
            .count()
        )

        # Fetch your dashboard statistics from your data source.

        # Query to count active jobs
        jobs_active = (
            Opportunity.select()
            .where(Opportunity.status == 1, Opportunity.business_id.in_(business_ids))
            .count()
        )

        # Query to count total candidates
        total_candidates = (
            Candidate.select()
            .where(
                Candidate.business_id.in_(business_ids), Candidate.is_deleted == False
            )
            .count()
        )

        # Query to count total shortlisted candidates
        total_shortlisted = (
            Candidate.select()
            .where(Candidate.status == 2, Candidate.business_id.in_(business_ids))
            .count()
        )

        # Query to count total jobs added last week
        total_jobs_last_week = (
            Opportunity.select()
            .where(
                Opportunity.created_at.between(start_of_week, end_of_week),
                Opportunity.business_id.in_(business_ids),
            )
            .count()
        )

        # Query to count total candidates added last week
        total_candidates_last_week = (
            Candidate.select()
            .where(
                Candidate.created_at.between(start_of_week, end_of_week),
                Candidate.business_id.in_(business_ids),
                Candidate.is_deleted == False,
            )
            .count()
        )

        # Query to count total shortlisted candidates added last week
        total_shortlisted_last_week = (
            Candidate.select()
            .where(
                Candidate.status == 2,
                Candidate.created_at.between(start_of_week, end_of_week),
                Candidate.business_id.in_(business_ids),
                Candidate.is_deleted == False,
            )
            .count()
        )

        stats = {
            "jobs_active": jobs_active,
            "total_candidates": total_candidates,
            "total_shortlisted": total_shortlisted,
            "total_jobs_last_week": total_jobs_last_week,
            "total_candidates_last_week": total_candidates_last_week,
            "total_shortlisted_last_week": total_shortlisted_last_week,
            "total_users": total_users,
            "total_businesses": total_businesses,
        }
        # Return the data using the defined response model
        return SuccessResponse(
            message="Dashboard statistics fetched successfully.", data=stats
        )
    except Exception as e:
        # If any exception occurs, raise an HTTP 500 error with the exception details
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=str(e)
        )


@router.get(
    "/stats",
    summary="List of Dashboard Stats",
    description="List of Dashboard Stats.",
    response_model=SuccessResponse,
)
async def get_dashboard_stats(
    type: Optional[str] = Query(
        None, description="Filter by type (monthly, weekly, yearly)"
    ),
    date: Optional[str] = Query(None, description="Selected date in YYYY-MM-DD format"),
    user: User = Depends(AdminAuthHelper.get_admin_user),
):
    """
    Retrieve dashboard statistics.

    Args:
    - type: Type of statistics to fetch (monthly, weekly, yearly).
    - date: The date used for fetching statistics, in YYYY-MM-DD format.
    - user: The authenticated admin user, injected by dependency injection.

    Returns:
    - A response model containing a message and a list of stats.
    """
    try:
        if user.is_super_admin:
            business_query = Business.select().where(Business.status == 1)
        else:
            business_query = Business.select().where(
                Business.status == 1, Business.user_id == user.id
            )

        # Convert date from string to datetime object, if provided
        if date:
            try:
                current_date = datetime.strptime(date, "%Y-%m-%d")
            except ValueError:
                raise HTTPException(
                    status_code=400, detail="Invalid date format. Use YYYY-MM-DD."
                )
        else:
            current_date = datetime.today()  # Use today's date if no date is provided

        # Fetch business IDs
        business_ids = [business.id for business in business_query]

        # Validate type
        if type not in ["weekly", "monthly", "daily", "yearly"]:
            raise HTTPException(
                status_code=400,
                detail="Invalid type. Must be 'weekly', 'monthly', 'daily', or 'yearly'.",
            )

        # Get candidate counts
        labels, counts, title, xLabel, yLabel, has_data = (
            CandidateHelper.get_candidate_count_by_grouping(
                current_date, type, business_ids
            )
        )

        bar_stats = {
            "label": labels,
            "data": [counts.get(label, 0) for label in labels],
            "title": title,
            "xLabel": xLabel,
            "yLabel": yLabel,
            "has_data": has_data,
        }

        # Get candidate status counts
        pie_stats_data, title, has_data, colors = (
            CandidateHelper.get_candidate_status_counts(business_ids)
        )

        pie_stats = {
            "data": pie_stats_data,
            "title": title,
            "has_data": has_data,
            "colors": colors,
        }

        stats = {
            "bar_stats": bar_stats,
            "pie_stats": pie_stats,
            "stats_type": type,
        }

        # Return the data using the defined response model
        return SuccessResponse(message="Statistics retrieved successfully", data=stats)
    except Exception as e:
        print(e, "e as errrr")
        # If any exception occurs, raise an HTTP 500 error with the exception details
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=str(e)
        )
