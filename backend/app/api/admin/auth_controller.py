from fastapi import APIRouter, Body, Response, Depends, Request
from app.models import User, UserLoginResponse, AuthToken, Employee, Business
from app.schema import MessageR<PERSON>ponse, SuccessResponse
from app.exceptions import CustomValidationError, UnauthorizedException
from app.validations import PasswordValidate
from app.helper import Admin<PERSON><PERSON><PERSON><PERSON><PERSON>
from app.tasks import UserTask
from typing import Union
from app.constants import ConstantMessages
import logging


async def get_login_response(
    request: Request,
    response: Response,
    user: User,
    message: str,
    auth_token: Union[AuthToken, None] = None,
) -> UserLoginResponse:
    from app.models.auth_token import AuthToken
    from app.models.user import UserLoginResponse
    from app.models.node import Node

    # If auth_token is not provided, create a new one
    if auth_token is None:
        auth_token = AuthToken.create(
            object_type=user.__class__.__name__, object_id=user.id
        )
        access_token = (
            auth_token.id
        )  # Assign the id of the created auth token to access_token
        # Set the auth token cookie with the newly created access_token
        await AdminAuthHelper.set_auth_token_cookie(
            request=request, response=response, access_token=access_token
        )
    else:
        access_token = (
            auth_token.id
        )  # If auth_token is provided, use its id as the access_token

    # Set response message based on whether the user's email is verified
    response_message = (
        "Please verify your account first" if not user.email_verified else message
    )

    # Query the Node table for records of type "AdminNode" with no parent
    base_node_query = Node.where({"type": "AdminNode", "parent_id": None})
    if not user.is_super_admin:
        base_node_query = base_node_query.where(
            Node.unique_key.in_(["admin_dashboard", "admin_business"])
        )

    # Order the query results by the order_num field in ascending order
    base_node_query = base_node_query.order_by(Node.order_num.asc())
    # Execute the query and call the info method on each result
    nodes = [record.info() for record in base_node_query]

    # Return a UserLoginResponse object with the user's details and the queried nodes
    return UserLoginResponse(
        message=response_message,
        data={
            "id": user.id,
            "email": user.email,
            "first_name": user.first_name,
            "last_name": user.last_name,
            "user_type_id": user.user_type.id,
            "user_type": user.user_type.name,
            "email_verified": user.email_verified,
            "access_token": access_token,
            "nodes": nodes,
        },
    )


router = APIRouter(prefix="/auth", tags=["Admin Authentication"])


@router.post(
    "/login",
    summary="User Login",
    description="User Login with Email and Password.",
    response_model=UserLoginResponse,
)
async def login(
    request: Request,
    response: Response,
    body: dict = Body(
        ...,
        example={
            "email": "<EMAIL>",
            "password": "password",
        },
    ),
):
    """
    Endpoint for user login.

    Args:
        response (Response): The response object to set cookies.
        body (dict): The request body containing email and password.

    Returns:
        UserLoginResponse: The response containing user data and login status.
    """
    try:
        email = body.get("email")
        password = body.get("password")

        user = await AdminAuthHelper.authenticate_user(email, password)

        if not user:
            raise UnauthorizedException(error="Incorrect username or password.")

        login_res = await get_login_response(
            request=request,
            response=response,
            user=user,
            message="Logged in successfully",
        )
        return login_res
    except UnauthorizedException as e:
        raise e
    except Exception as e:
        logging.error(f"Login Exception: {str(e)}")
        raise CustomValidationError(error=ConstantMessages.SOMETHING_WENT_WRONG)


@router.post(
    "/forgot-password",
    summary="Forgot Password",
    description="Forgot Password.",
    response_model=MessageResponse,
)
async def forgot_password(
    request: Request,
    response: Response,
    body: dict = Body(
        ...,
        example={
            "email": "<EMAIL>",
        },
    ),
):
    """
    Endpoint to handle forgot password requests.

    Args:
        response (Response): The response object to set cookies.
        body (dict): The request body containing the email.

    Returns:
        MessageResponse: The response indicating the result of the forgot password request.
    """
    try:
        email = body.get("email")

        user = User.get_or_none(email=email, status=1)
        if user is None:
            raise CustomValidationError(error="User with this email does not exist.")

        user.generate_otp()
        UserTask.reset_password_email.delay(user_id=user.id)
        await AdminAuthHelper.set_otp_cookie(
            request=request, response=response, email=user.email
        )
        return MessageResponse(
            message="An email containing the OTP has been sent to your email address. Please use this OTP to verify your email address."
        )
    except ValueError as e:
        logging.error(f"Forgot Password Exception: {str(e)}")
        raise CustomValidationError(error=e)
    except CustomValidationError as e:
        logging.error(f"Forgot Password Exception: {str(e)}")
        raise CustomValidationError(error=e)
    except Exception as e:
        logging.error(f"Forgot Password Exception: {str(e)}")
        raise CustomValidationError(error=ConstantMessages.SOMETHING_WENT_WRONG)


@router.post(
    "/verify-forgot-password-otp",
    summary="Verify OTP",
    description="Verify OTP.",
    response_model=MessageResponse,
)
async def verify_forgot_password_otp(
    request: Request,
    response: Response,
    user: User = Depends(AdminAuthHelper.get_email_otp_admin),
    body: dict = Body(
        ...,
        example={
            "otp": "123456",
            "email": "<EMAIL>",
        },
    ),
):
    """
    Endpoint to verify the OTP for forgotten password.

    Args:
        response (Response): The response object to set cookies.
        user (User): The user instance, provided by dependency injection.
        body (dict): The request body containing the OTP and email.

    Returns:
        MessageResponse: The response indicating the result of the OTP verification.
    """
    try:
        otp = body.get("otp")

        if user.is_otp_expired():
            raise CustomValidationError(error="OTP has expired")

        if not otp or otp != user.otp:
            raise CustomValidationError(error="OTP is invalid")

        await AdminAuthHelper.set_otp_cookie(
            request=request, response=response, email=user.email
        )
        return MessageResponse(message="OTP verified successfully.")
    except ValueError as e:
        logging.error(f"Verify Forgot Password OTP Exception: {str(e)}")
        raise CustomValidationError(error=e)
    except CustomValidationError as e:
        logging.error(f"Verify Forgot Password OTP Exception: {str(e)}")
        raise CustomValidationError(error=e)
    except Exception as e:
        logging.error(f"Verify Forgot Password OTP Exception: {str(e)}")
        raise CustomValidationError(error=ConstantMessages.SOMETHING_WENT_WRONG)


@router.post(
    "/resend-forgot-password-otp",
    summary="Resend OTP",
    description="Resend OTP.",
    response_model=MessageResponse,
)
async def resend_verify_email(
    request: Request,
    response: Response,
    user: User = Depends(AdminAuthHelper.get_email_otp_admin),
    body: dict = Body(..., example={"otp": "123456"}),
):
    """
    Endpoint to resend the OTP for password reset.

    Args:
        response (Response): The response object to set cookies.
        user (User): The user instance, provided by dependency injection.
        body (dict): The request body containing the OTP.

    Returns:
        MessageResponse: The response indicating the result of the OTP resend operation.
    """
    try:
        user.generate_otp()
        UserTask.resend_otp_email.delay(
            user_id=user.id, resend_text="Please use this OTP to reset your password."
        )
        await AdminAuthHelper.set_otp_cookie(
            request=request, response=response, email=user.email
        )
        return MessageResponse(
            message="An email containing the OTP has been sent to your email address. Please use this OTP to reset your password."
        )
    except Exception as e:
        logging.error(f"Resend Forgot Password OTP Exception: {str(e)}")
        raise CustomValidationError(error=ConstantMessages.SOMETHING_WENT_WRONG)


@router.post(
    "/change-password",
    summary="Update Password",
    description="Update Password.",
    response_model=UserLoginResponse,
)
async def change_password(
    request: Request,
    response: Response,
    user: User = Depends(AdminAuthHelper.get_email_otp_admin),
    body: dict = Body(
        ...,
        example={
            "otp": "123456",
            "password": "123456",
            "confirm_password": "123456",
        },
    ),
):
    """
    Endpoint to update the user's password.

    Args:
        response (Response): The response object to set cookies.
        user (User): The user instance, provided by dependency injection.
        body (dict): The request body containing the OTP, new password, and confirm password.

    Returns:
        MessageResponse: The response indicating the result of the password update.
    """
    try:
        otp = body.get("otp")

        if user.is_otp_expired():
            raise CustomValidationError(error="OTP has expired")

        if not otp or otp != user.otp:
            raise CustomValidationError(error="OTP is invalid")

        password = body.get("password")
        confirm_password = body.get("confirm_password")

        password_hash = PasswordValidate(
            password, field="Password", min_length=8, max_length=20
        )
        PasswordValidate(
            confirm_password, field="Password", min_length=8, max_length=20
        )

        if password != confirm_password:
            raise CustomValidationError(error="Confirm password does not match.")

        user.password = password_hash
        user.save()
        # Assuming `set_otp_cookie` handles the logic for resetting OTP or related operations.
        await AdminAuthHelper.clear_otp_cookie(request=request, response=response)

        login_res = await get_login_response(
            request=request,
            response=response,
            user=user,
            message="Password Changed successfully",
        )
        return login_res
    except CustomValidationError as e:
        raise e
    except ValueError as e:
        raise e
    except Exception as e:
        logging.error(f"Change Password Exception: {str(e)}")
        raise CustomValidationError(error=ConstantMessages.SOMETHING_WENT_WRONG)


@router.post(
    "/me",
    summary="Verify Current User",
    description="Verify Current User",
    response_model=UserLoginResponse,
)
async def verify_admin(
    request: Request,
    response: Response,
    user: User = Depends(AdminAuthHelper.get_admin_user),
    auth_token: AuthToken = Depends(AdminAuthHelper.get_current_auth_token),
):
    """
    Endpoint to verify the current user.

    Args:
        response (Response): The response object to set cookies.
        user (User): The current user, provided by dependency injection.

    Returns:
        UserLoginResponse: The response containing user data and login status.
    """
    try:
        auth_token.update_expiration()
        await AdminAuthHelper.set_auth_token_cookie(
            request=request, response=response, access_token=auth_token.id
        )
        login_res = await get_login_response(
            request=request,
            response=response,
            user=user,
            message="Logged in successfully",
            auth_token=auth_token,
        )
        return login_res
    except Exception as e:
        logging.error(f"Verify Current Admin Exception: {str(e)}")
        raise CustomValidationError(error=ConstantMessages.SOMETHING_WENT_WRONG)


@router.put(
    "/update-password",
    summary="Update Current User Password",
    description="Update the password for the current employee",
    response_model=SuccessResponse,
)
async def update_password(
    user: User = Depends(AdminAuthHelper.get_admin_user),
    body: dict = Body(...),
):
    """
    Endpoint to update the current user's password.

    Args:
        user (User): The current user, provided by dependency injection.
        body (UpdatePasswordRequest): Request body containing old and new passwords.

    Returns:
        SuccessResponse: The response containing status and optional data.
    """
    try:
        old_password = body.get("old_password")
        new_password = body.get("new_password")
        confirm_password = body.get("confirm_password")

        # Check if the old password matches
        if not user.match_password(old_password):
            raise CustomValidationError(error="Old password does not match.")

        password_hash = PasswordValidate(
            new_password, field="New Password", min_length=8, max_length=20
        )
        PasswordValidate(
            confirm_password, field="Confirm Password", min_length=8, max_length=20
        )

        if new_password != confirm_password:
            raise CustomValidationError(error="Confirm password does not match.")

        user.password = password_hash
        user.save()

        return SuccessResponse(message="Password updated successfully")
    except CustomValidationError as e:
        logging.error(f"Verify Current Admin Exception: {str(e)}")
        raise e
    except ValueError as e:
        logging.error(f"Verify Current Admin Exception: {str(e)}")
        raise e
    except Exception as e:
        logging.error(f"Verify Current Admin Exception: {str(e)}")
        raise CustomValidationError(error=ConstantMessages.SOMETHING_WENT_WRONG)


@router.delete(
    "/logout",
    summary="Logout Current User",
    description="Logout the current user by deleting the authentication token.",
    response_model=MessageResponse,
)
async def logout(
    request: Request,
    response: Response,
    auth_token: AuthToken = Depends(AdminAuthHelper.get_current_auth_token),
):
    """
    Logout the current user by deleting the authentication token.

    - **auth_token**: The authentication token associated with the current user.
    """

    # Delete the authentication token instance from the database
    await AdminAuthHelper.clear_auth_cookie(request=request, response=response)
    try:
        auth_token.delete_instance()
    except Exception as e:
        logging.error(f"Exception in delete auth token: {str(e)}")

    # Return a success message
    return MessageResponse(message="Logged out successfully.")


@router.post(
    "/business_login/{subdomain}",
    summary="Verify Current User",
    description="Verify the current user and set authentication token.",
    response_model=MessageResponse,
)
async def business_login(
    subdomain: str,
    request: Request,
    response: Response,
    user: User = Depends(AdminAuthHelper.get_admin_user),
):
    """
    Endpoint to verify the current user and set the authentication token.

    Args:
        subdomain (str): The subdomain of the business.
        request (Request): The request object.
        response (Response): The response object to set cookies.
        user (User): The current user, provided by dependency injection.

    Returns:
        MessageResponse: A success message if the login is successful.
    """
    try:
        # Fetch the business using the subdomain
        business: Business = AdminAuthHelper.get_business(subdomain)

        # Fetch the employee associated with the business and user email
        employee = Employee.get_or_none(
            business_id=business.id, email=user.email, status=1
        )
        if not employee:
            raise UnauthorizedException(
                error="Employee does not exist with your email."
            )

        # Create an authentication token
        auth_token = AuthToken.create(
            object_type=employee.__class__.__name__, object_id=employee.id
        )
        access_token = auth_token.id

        # Set the authentication token cookie
        await AdminAuthHelper.set_auth_token_cookie(
            request=request,
            response=response,
            access_token=access_token,
            custom_subdomain=subdomain,
        )

        # Return a success response
        return MessageResponse(message="Success")

    except UnauthorizedException as e:
        # Handle unauthorized access
        raise e
    except Exception as e:
        # Log and handle any other exceptions
        logging.error(f"Verify Current Admin Exception: {str(e)}")
        raise CustomValidationError(error=ConstantMessages.SOMETHING_WENT_WRONG)
