from fastapi import (
    APIRouter,
    Depends,
    Query,
    HTTPException,
    status,
)

from app.schema import SuccessResponse, PaginationResponse, UpdateStatusPayload
from app.models import User, User, UserType
from app.exceptions import RecordNotFoundException
from app.helper import AdminAuthHelper
import logging

router = APIRouter(
    prefix="/users",
    tags=["Admin User API"],
    dependencies=[
        Depends(AdminAuthHelper.get_current_auth_token),
        Depends(AdminAuthHelper.get_admin_user),
        Depends(AdminAuthHelper.user_is_super_admin),
    ],
)

# ------------------------------ functions ------------------------------


def get_user(user_id: int) -> User:
    user = User.get_or_none(id=user_id)
    if not user:
        raise RecordNotFoundException(message="User not found or is inactive")
    return user


@router.get(
    "",
    summary="List of Users",
    description="Get a paginated list of users.",
    response_model=PaginationResponse,
)
def get_users(
    page: int = Query(1, gt=0),
    limit: int = Query(10, gt=0, le=100),
):
    """
    Retrieve a paginated list of users.

    Args:
        page (int): The page number for pagination. Defaults to 1.
        limit (int): The maximum number of users per page. Defaults to 10.

    Returns:
        PaginationResponse: Paginated list of users.
    """
    try:
        # Fetch total count
        user_query = User.select().join(UserType).where(UserType.name != "Super Admin")

        total_user = user_query.count()

        # Paginate results
        if total_user > 0:
            offset = (page - 1) * limit
            users = user_query.offset(offset).limit(limit).order_by(User.id.desc())
        else:
            users = []

        # Prepare user list
        user_list = [user.info() for user in users]

        return PaginationResponse(
            data={
                "page": page,
                "limit": limit,
                "count": total_user,
                "rows": user_list,
            },
            message="Data fetched successfully",
        )
    except Exception as e:
        logging.error(f"Exception in user list: {str(e)}")
        return PaginationResponse(
            data={"page": page, "limit": limit, "count": 0, "rows": []},
            message=f"Failed to fetch data: {str(e)}",
        )


@router.patch(
    "/{user_id}/status",
    response_model=SuccessResponse,
    summary="Update User Status",
    description="Update the status of a user.",
)
def update_user_status(
    payload: UpdateStatusPayload,
    user: User = Depends(get_user),
):
    """
    Update the status of a user.

    Args:
        id (int): The ID of the user.
        user (User): The user instance, provided by dependency injection.

    Returns:
        SuccessResponse: The response containing the result message and updated user information.
    """
    try:
        user.status = payload.status
        user.save()
        return SuccessResponse(
            message="User Status updated successfully.",
            data=user.info(),
        )
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_422_UNPROCESSABLE_ENTITY, detail=str(e)
        )
