from fastapi import APIRouter, HTTPException, status, Body
from app.schema import SuccessResponse
from app.validations import EmailValidate, StringValidate
from app.tasks import HomeTask
from app.models import ContactEnquery
import logging


# ------------------------------ router functions ------------------------------


router = APIRouter(prefix="/contact_enqueries", tags=["Contact Enquery"])


@router.post(
    "",
    summary="This end point api for the clients who visited the home page",
    description="This end point api sends an email to the company email.",
    response_model=SuccessResponse,
)
async def send_email(
    body: dict = Body(
        ...,
        example={
            "first_name": "<PERSON>",
            "last_name": "Doe",
            "email": "<EMAIL>",
            "subject": "ABC",
            "message": "message",
        },
    ),
):
    """This is the end-point api which is send the clients enquery to the company email."

    Args:
        - first_name: str - The first name of the user (required).
        - last_name: str - The last name of the user (required).
        - email: str - The email address of the user (required).
        - subject: str - The Subject of the email (required).
        - message: str = The Message or main body of the email (required).

    Returns:
        -Client Mail send Successful to the Company email
    """
    try:
        first_name = StringValidate(
            body.get("first_name"),
            field="First Name",
            required=True,
            max_length=100,
            strip=True,
        )
        last_name = StringValidate(
            body.get("last_name"),
            field="Last Name",
            required=True,
            max_length=100,
            strip=True,
        )
        email = EmailValidate(body.get("email"), field="Email")
        subject = StringValidate(
            body.get("subject"),
            field="Subject",
            required=True,
            max_length=250,
            strip=False,
        )
        message = StringValidate(
            body.get("message"),
            field="Message",
            required=True,
            max_length=500,
            strip=False,
        )

        contact_enquery = ContactEnquery.create(
            email=email,
            first_name=first_name,
            last_name=last_name,
            subject=subject,
            message=message,
        )

        # Add email sending task to background
        HomeTask.contact_us_email.delay(contact_enquery_id=contact_enquery.id)

        return SuccessResponse(
            message="Thanks for reaching out! We’ll get back to you shortly."
        )

    except Exception as e:
        logging.error(f"Error processing email request: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_422_UNPROCESSABLE_ENTITY, detail=str(e)
        )
