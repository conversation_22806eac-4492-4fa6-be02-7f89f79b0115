from fastapi import APIRouter, Depends, Query, status, Body, Request
from app.schema import PaginationResponse, SuccessResponse
from app.models import (
    Employee,
    Business,
    ScheduleInterview,
    CandidateInterview,
    Candidate,
    CandidateScreeningAnswer,
    InterviewWarning,
)
from app.validations import Number<PERSON>alidate, StringValidate
from app.exceptions import RecordNotFoundException, HTTPException
from app.models.concern.enum import Interview<PERSON><PERSON>, InterviewStatus
from app.helper import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>per
from app.constants import ConstantMessages, NodeName, PermissionName
from peewee import JOIN, fn
from typing import Optional, Union
import datetime
import logging


async def get_interview(
    id: int,
    business: Business = Depends(WildcardAuthHelper.validate_subdomain),
    current_employee: Employee = Depends(WildcardAuthHelper.get_current_employee),
) -> CandidateInterview:
    """
    Retrieve a CandidateInterview by its ID and business context.

    Args:
        id (int): The ID of the interview to retrieve.
        business (Business): The business context provided by dependency injection.
        current_employee (Employee): The current employee provided by dependency injection.

    Returns:
        CandidateInterview: The CandidateInterview object if found.

    Raises:
        RecordNotFoundException: If the CandidateInterview does not exist.
    """
    interview: CandidateInterview = CandidateInterview.get_or_none(
        id=id, business_id=business.id
    )
    if not interview:
        raise RecordNotFoundException(message="Interview does not exist")

    if not current_employee.is_admin and not (
        interview.created_by_id == current_employee.id
        or current_employee.id
        in ScheduleInterview.pluck_from_query(
            interview.schedule_interviews, "interviewer_id"
        )
    ):
        raise RecordNotFoundException(message="Not authorized to do this action")

    return interview


router = APIRouter(
    prefix="/candidate_interviews",
    tags=["Wildcard Interview API"],
    dependencies=[
        Depends(WildcardAuthHelper.get_current_employee),
        Depends(WildcardAuthHelper.get_current_auth_token),
    ],
)

# ------------------------------ router functions ------------------------------


# list of interviews
@router.get(
    "",
    summary="List of Interview",
    description="Retrieve a paginated list of interviews.",
    response_model=PaginationResponse,
)
def get_interviews(
    request: Request,
    page: int = Query(1, gt=0),
    limit: int = Query(10, gt=0, le=100),
    search: Optional[str] = Query(
        None, description="Search by Candidate Name or Email"
    ),
    interview_status: Optional[Union[int, str]] = Query(
        None, description="Filter by Interview Status"
    ),
    opportunity_id: int = Query(None, description="Filter by Opportunity ID"),
    date_filter: str = Query(
        None, description="Filter Interviews by 'today' or 'upcoming'"
    ),
    business: Business = Depends(WildcardAuthHelper.validate_subdomain),
    current_employee: Employee = Depends(WildcardAuthHelper.get_current_employee),
):
    """
    Retrieve a paginated list of interviews.

    Args:
        page (int): The page number for pagination. Defaults to 1.
        limit (int): The maximum number of interviews per page. Defaults to 10.

    Returns:
        PaginationResponse: Paginated list of interviews.
    """
    WildcardAuthHelper.has_route_permission(
        request=request,
        node_key=NodeName.EMPLOYEE_CANDIDATES,
        perm_names=PermissionName.READ_ONLY,
    )
    WildcardAuthHelper.has_route_permission(
        request=request,
        node_key=NodeName.EMPLOYEE_INTERVIEWS,
        perm_names=PermissionName.READ_ONLY,
    )
    try:
        # Fetch total count
        is_admin = current_employee.is_admin
        # Subquery to get unique CandidateInterview records
        subquery = (
            CandidateInterview.select(CandidateInterview.id)
            .distinct()
            .join(
                ScheduleInterview,
                JOIN.LEFT_OUTER,
                on=(ScheduleInterview.candidate_interview_id == CandidateInterview.id),
            )
            .where(CandidateInterview.business_id == business.id)
        )

        start, end = FilterHelper.get_date_range(date_filter)

        if search:
            search = search.strip().lower()

            # Join ScheduleInterview with Candidate to enable filtering by candidate fields
            subquery = subquery.join(
                Candidate, on=(ScheduleInterview.candidate_id == Candidate.id)
            )

            # Apply filter for case-insensitive search on name and email
            subquery = subquery.where(
                (fn.LOWER(Candidate.name).contains(search))
                | (fn.LOWER(Candidate.email).contains(search))
            )

        # Apply date filtering on ScheduleInterview.interview_date if start or end defined
        if start and end:
            subquery = subquery.where(
                (ScheduleInterview.interview_at >= start)
                & (ScheduleInterview.interview_at < end)
            )
        elif start and not end:
            subquery = subquery.where(ScheduleInterview.interview_at >= start)
        elif end and not start:
            subquery = subquery.where(ScheduleInterview.interview_at < end)

        if opportunity_id:
            subquery = subquery.where(
                (CandidateInterview.opportunity_id == opportunity_id)
            )

        if interview_status and interview_status != 0 and interview_status != "":
            subquery = subquery.where((CandidateInterview.status == interview_status))

        if not is_admin:
            subquery = subquery.where(
                (CandidateInterview.created_by_id == current_employee.id)
                | (ScheduleInterview.interviewer_id == current_employee.id)
            )

        base_query = (
            CandidateInterview.select(
                CandidateInterview,
                ScheduleInterview.id.alias("interview_id"),
                ScheduleInterview.interview_at,
                ScheduleInterview.interview_round,
                ScheduleInterview.interviewer_id,
                ScheduleInterview.interview_mode,
            )
            .join(
                ScheduleInterview,
                JOIN.LEFT_OUTER,
                on=(ScheduleInterview.candidate_interview_id == CandidateInterview.id),
            )
            .where(
                ScheduleInterview.id
                == (
                    ScheduleInterview.select(fn.MAX(ScheduleInterview.id)).where(
                        ScheduleInterview.candidate_interview_id
                        == CandidateInterview.id
                    )
                )
            )
            .where(CandidateInterview.id.in_(subquery))
            .order_by(ScheduleInterview.id.desc())
        )

        total_records = base_query.count()

        # Paginate results
        if total_records > 0:
            offset = (page - 1) * limit
            records = base_query.offset(offset).limit(limit)
        else:
            records = []

        # Prepare interview list
        rows = [record.custom_info() for record in records]

        return PaginationResponse(
            data={"page": page, "limit": limit, "count": total_records, "rows": rows},
            message=ConstantMessages.FETCHED_SUCCESSFULLY,
        )
    except Exception as e:
        logging.error(f"Exception in interview list: {str(e)}")
        return PaginationResponse(
            data={"page": page, "limit": limit, "count": 0, "rows": []},
            message=f"Failed to fetch data: {str(e)}",
        )


@router.get("/{id}", summary="Get Interview by ID", response_model=SuccessResponse)
async def get_interview_detail(
    request: Request,
    interview: CandidateInterview = Depends(get_interview),
    current_employee: Employee = Depends(WildcardAuthHelper.get_current_employee),
):
    """
    Endpoint to retrieve CandidateInterview details.

    Args:
        id (int): The ID of the interview to retrieve.
        interview (CandidateInterview): The CandidateInterview instance, provided by dependency injection.

    Returns:
        SuccessResponse: Success message along with CandidateInterview details.
    """
    WildcardAuthHelper.has_route_permission(
        request=request,
        node_key=NodeName.EMPLOYEE_CANDIDATES,
        perm_names=PermissionName.READ_ONLY,
    )
    WildcardAuthHelper.has_route_permission(
        request=request,
        node_key=NodeName.EMPLOYEE_INTERVIEWS,
        perm_names=PermissionName.READ_ONLY,
    )
    try:
        interview_scheduled = (
            ScheduleInterview.select()
            .where(ScheduleInterview.candidate_interview_id == interview.id)
            .order_by(ScheduleInterview.created_at.desc())
            .first()
        )
        if not interview_scheduled:
            raise RecordNotFoundException(message="Interview does not exist")
        if (
            current_employee.is_admin
            or (current_employee.employee_role.name == "HR")
            or (interview_scheduled.interviewer_id == current_employee.id)
        ):
            schedule_interviews = interview.schedule_interviews.order_by(
                ScheduleInterview.id.asc()
            )
            records = [record.info() for record in schedule_interviews]
            return SuccessResponse(
                message="Interview details fetched successfully.",
                data={
                    "interview": interview.info(),
                    "records": records,
                },
            )
        else:
            raise RecordNotFoundException(message="Not authorized to do this action")
    except RecordNotFoundException as e:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail=str(e))
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_422_UNPROCESSABLE_ENTITY, detail=str(e)
        )


@router.post(
    "/{id}/new_round", summary="Get Interview by ID", response_model=SuccessResponse
)
async def get_interview_detail(
    request: Request,
    interview: CandidateInterview = Depends(get_interview),
    business: Business = Depends(WildcardAuthHelper.validate_subdomain),
    current_employee: Employee = Depends(WildcardAuthHelper.get_current_employee),
    body: dict = Body(...),
):
    """
    Endpoint to retrieve CandidateInterview details.

    Args:
        id (int): The ID of the interview to retrieve.
        interview (CandidateInterview): The CandidateInterview instance, provided by dependency injection.

    Returns:
        SuccessResponse: Success message along with CandidateInterview details.
    """
    WildcardAuthHelper.has_route_permission(
        request=request,
        node_key=NodeName.EMPLOYEE_CANDIDATES,
        perm_names=PermissionName.READ_ONLY,
    )
    WildcardAuthHelper.has_route_permission(
        request=request,
        node_key=NodeName.EMPLOYEE_INTERVIEWS,
        perm_names=PermissionName.READ_WRITE,
    )
    try:
        interviewer_id = body.get("interviewer_id")
        if interviewer_id:
            interviewer = Employee.get_or_none(
                id=interviewer_id, business_id=business.id, status=1
            )
            if not interviewer:
                raise RecordNotFoundException(message="Interviewer does not exist")
        else:
            raise RecordNotFoundException(message="Interviewer must exist")

        interview_mode_id = NumberValidate(
            body.get("interview_mode_id"),
            field="interview_mode",
            allow_zero=True,
            min_value=0,
            max_value=3,
        )
        interview_mode = InterviewMode(interview_mode_id)
        is_video_call_interview = interview_mode.name == "Video_Call"

        # validating interview data
        interview_at = StringValidate(
            body.get("interview_at"), field="Interview Time", required=True, strip=False
        )
        meeting_link = StringValidate(
            body.get("meeting_link"),
            field="Meeting Link",
            required=is_video_call_interview,
            strip=True,
        )
        comment = StringValidate(
            body.get("comment"), field="Comment", required=False, strip=True
        )
        reason = StringValidate(
            body.get("reason"), field="Reason", required=False, strip=True
        )

        # always first round created
        # interview_round = interview.sch

        candidate = interview.candidate
        candidate_id = interview.candidate_id
        opportunity_id = interview.opportunity_id
        last_interview = interview.schedule_interviews.order_by(
            ScheduleInterview.id.desc()
        ).get()
        interview_round = last_interview.interview_round

        interview_status_key = body.get("status") or 1
        interview_status = InterviewStatus(interview_status_key)
        is_reschedule_interview = interview_status.name == "Rescheduled"

        if is_reschedule_interview:
            # if last_interview:
            # interview mark as reschedule
            last_interview.status = InterviewStatus(2)
            last_interview.comment = reason
            last_interview.save()
        else:
            interview_round = interview_round + 1

        passing_percentage = NumberValidate(
            (body.get("passing_percentage") or 0),
            field="Passing Percentage",
            allow_zero=True,
            min_value=0,
            max_value=100,
        )

        time_duration = NumberValidate(
            (body.get("time_duration") or 0),
            field="Time Duration",
            allow_zero=True,
            min_value=0,
            max_value=500,
        )
        show_marks = body.get("show_marks") or False
        phone_number = StringValidate(
            body.get("phone_number"),
            field="Phone Number",
            required=True,
            max_length=20,
            strip=True,
        )

        schedule_interview = ScheduleInterview.create(
            interview_at=interview_at,
            meeting_link=meeting_link,
            comment=comment,
            candidate_id=candidate_id,
            created_by_id=current_employee.id,
            interviewer_id=interviewer_id,
            interview_mode=interview_mode.value,
            interview_round=interview_round,
            business_id=business.id,
            opportunity_id=opportunity_id,
            candidate_interview_id=interview.id,
            passing_percentage=passing_percentage,
            time_duration=time_duration,
            show_marks=show_marks,
            phone_number=phone_number,
        )

        # Send email to candidate
        InterviewHelper.schedule_interview_email(schedule_interview=schedule_interview)

        # Return success response with schedule interview details
        if is_reschedule_interview:
            message = "Interview Rescheduled Successfully."
        else:
            message = "Interview Scheduled Successfully."

        return SuccessResponse(message=message, data=interview.info())
    except RecordNotFoundException as e:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail=str(e))
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_422_UNPROCESSABLE_ENTITY, detail=str(e)
        )


@router.put(
    "/{id}/status", summary="Update Interview status", response_model=SuccessResponse
)
async def update_interview_status(
    request: Request,
    interview: CandidateInterview = Depends(get_interview),
    body: dict = Body(...),
):
    """
    Endpoint to retrieve CandidateInterview details.

    Args:
        id (int): The ID of the interview to retrieve.
        interview (CandidateInterview): The CandidateInterview instance, provided by dependency injection.

    Returns:
        SuccessResponse: Success message along with CandidateInterview details.
    """
    WildcardAuthHelper.has_route_permission(
        request=request,
        node_key=NodeName.EMPLOYEE_CANDIDATES,
        perm_names=PermissionName.READ_ONLY,
    )
    WildcardAuthHelper.has_route_permission(
        request=request,
        node_key=NodeName.EMPLOYEE_INTERVIEWS,
        perm_names=PermissionName.READ_EDIT,
    )
    try:
        interview_status = body.get("status")
        interview.status = interview_status
        interview.save()

        last_interview = interview.schedule_interviews.order_by(
            ScheduleInterview.id.desc()
        ).get()
        if (
            last_interview
            and interview.status.name == "Rejected"
            and last_interview.status.name == "Scheduled"
        ):
            # mark as rejected
            last_interview.status = 3
            last_interview.save()

        return SuccessResponse(
            message="Status updated successfully.", data=interview.info()
        )
    except RecordNotFoundException as e:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail=str(e))
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_422_UNPROCESSABLE_ENTITY, detail=str(e)
        )


@router.get(
    "/{id}/upcoming_interviews",
    summary="fetching the list of the upcoming interview schedule.",
    description="This end point api fetching the upcoming interview details according to the current date.",
    response_model=PaginationResponse,
)
def get_upcoming_interviews(
    request: Request,
    page: int = Query(1, gt=0),
    limit: int = Query(4, gt=0, le=10),
    business: Business = Depends(WildcardAuthHelper.validate_subdomain),
):
    """
    This end point api fetching the upcoming interview details according to the current date.
    Args:
        page (int): The page number for pagination. Defaults to 1.
        limit (int): The maximum number of interviews per page. Defaults to 10.
        business (Business): The business context provided by dependency injection.

    Returns:
        PaginationResponse: Paginated list of interviews.
    """
    WildcardAuthHelper.has_route_permission(
        request=request,
        node_key=NodeName.EMPLOYEE_CANDIDATES,
        perm_names=PermissionName.READ_ONLY,
    )
    WildcardAuthHelper.has_route_permission(
        request=request,
        node_key=NodeName.EMPLOYEE_INTERVIEWS,
        perm_names=PermissionName.READ_ONLY,
    )
    try:
        base_query = ScheduleInterview.select().where(
            ScheduleInterview.business_id == business.id,
            fn.DATE(ScheduleInterview.interview_at) >= datetime.date.today(),
        )

        total_records = base_query.count()

        if total_records > 0:
            offset = (page - 1) * limit
            records = base_query.offset(offset).limit(limit)
        else:
            records = []

        # Prepare interview list
        rows = [record.info() for record in records]

        return PaginationResponse(
            data={"page": page, "limit": limit, "count": total_records, "rows": rows},
            message=ConstantMessages.FETCHED_SUCCESSFULLY,
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_422_UNPROCESSABLE_ENTITY, detail=str(e)
        )


@router.get(
    "/{id}/candidate_and_job_detail",
    summary="Get Interview Detail + Candidate and Job",
    response_model=SuccessResponse,
)
async def get_interview_detail(
    request: Request,
    interview: CandidateInterview = Depends(get_interview),
    current_employee: Employee = Depends(WildcardAuthHelper.get_current_employee),
):
    """
    Endpoint to retrieve CandidateInterview details.

    Args:
        id (int): The ID of the interview to retrieve.
        interview (CandidateInterview): The CandidateInterview instance, provided by dependency injection.

    Returns:
        SuccessResponse: Success message along with CandidateInterview details.
    """
    WildcardAuthHelper.has_route_permission(
        request=request,
        node_key=NodeName.EMPLOYEE_CANDIDATES,
        perm_names=PermissionName.READ_ONLY,
    )
    WildcardAuthHelper.has_route_permission(
        request=request,
        node_key=NodeName.EMPLOYEE_INTERVIEWS,
        perm_names=PermissionName.READ_ONLY,
    )
    try:
        interview_scheduled = (
            ScheduleInterview.select()
            .where(ScheduleInterview.candidate_interview_id == interview.id)
            .order_by(ScheduleInterview.created_at.desc())
            .first()
        )
        if not interview_scheduled:
            raise RecordNotFoundException(message="Interview does not exist")
        if (
            current_employee.is_admin
            or (current_employee.employee_role.name == "HR")
            or (interview_scheduled.interviewer_id == current_employee.id)
        ):
            return SuccessResponse(
                message="Interview Candiate and Job Detail fetched successfully.",
                data={
                    "interview": interview.info(),
                    "candidate": interview.candidate.info(),
                    "opportunity": interview.opportunity.info(),
                },
            )
        else:
            raise RecordNotFoundException(message="Not authorized to do this action")
    except RecordNotFoundException as e:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail=str(e))
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_422_UNPROCESSABLE_ENTITY, detail=str(e)
        )


@router.get(
    "/{id}/review/{schedule_interview_id}",
    summary="Get Interview Detail + Candidate and Job",
    response_model=SuccessResponse,
)
async def get_interview_review(
    request: Request,
    schedule_interview_id: int,
    interview: CandidateInterview = Depends(get_interview),
    current_employee: Employee = Depends(WildcardAuthHelper.get_current_employee),
):
    """
    Endpoint to retrieve CandidateInterview details.

    Args:
        id (int): The ID of the interview to retrieve.
        schedule_interview_id (int): The ID of the schedule interview like screening etc.
        interview (CandidateInterview): The CandidateInterview instance, provided by dependency injection.

    Returns:
        SuccessResponse: Success message along with Review details.
    """
    WildcardAuthHelper.has_route_permission(
        request=request,
        node_key=NodeName.EMPLOYEE_INTERVIEWS,
        perm_names=PermissionName.READ_ONLY,
    )
    try:
        schedule_interview = (
            ScheduleInterview.select()
            .where(
                ScheduleInterview.id == schedule_interview_id,
                ScheduleInterview.candidate_interview_id == interview.id,
            )
            .first()
        )
        if not schedule_interview:
            raise RecordNotFoundException(message="Interview does not exist")

        if schedule_interview.interview_mode.name != "Screening":
            raise RecordNotFoundException(message="Interview does not exist")

        if (
            current_employee.is_admin
            or (current_employee.employee_role.name == "HR")
            or (schedule_interview.interviewer_id == current_employee.id)
        ):
            answers_query = CandidateScreeningAnswer.select().where(
                CandidateScreeningAnswer.schedule_interview_id == schedule_interview_id
            )
            answer_sheet = [record.info() for record in answers_query]

            interview_warnings_query = InterviewWarning.select().where(
                InterviewWarning.schedule_interview_id == schedule_interview_id
            )
            interview_warnings = [record.info() for record in interview_warnings_query]

            return SuccessResponse(
                message="Detail fetched successfully.",
                data={
                    "interview": interview.info(),
                    "schedule_interview": schedule_interview.screening_info(),
                    "answer_sheet": answer_sheet,
                    "candidate": interview.candidate.base_info(),
                    "interview_warnings": interview_warnings,
                },
            )
        else:
            raise RecordNotFoundException(message="Not authorized to do this action")
    except RecordNotFoundException as e:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail=str(e))
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_422_UNPROCESSABLE_ENTITY, detail=str(e)
        )
