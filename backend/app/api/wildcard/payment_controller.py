from fastapi import APIRouter, Depends, HTTPException, status, Request, Body
from app.models import (
    Business,
    PaymentPlan,
)
from app.schema import SuccessResponse
from app.exceptions import RecordNotFoundException
from app.helper import WildcardAuthHelper
from app.config import StripeClient

# Initialize the APIRouter with a tag for categorizing endpoints in the documentation.
router = APIRouter(
    prefix="/payments",
    tags=["Wildcard Payment API"],
    dependencies=[Depends(WildcardAuthHelper.get_current_auth_token)],
)

# ------------------------------ functions ------------------------------


@router.get(
    "/plans",
    summary="Get Plan List",
    description="Payment Webhook",
    response_model=SuccessResponse,
)
async def get_plans():
    try:
        # plans = StripeClient.active_plans()
        records = PaymentPlan.select().where(PaymentPlan.status == 1)
        rows = [record.info() for record in records]

        return SuccessResponse(
            message="Plans retrieved successfully",
            data=rows,
        )
    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))


@router.post(
    "/buy-plan",
    summary="Buy plan",
    description="Buy plan",
    response_model=SuccessResponse,
)
def buy_plan(
    business: Business = Depends(WildcardAuthHelper.validate_subdomain),
    body: dict = Body(
        ...,
        example={
            "payment_plan_id": 1,
        },
    ),
):
    """
    Retrieve dashboard statistics.

    Args:
    - type: Type of statistics to fetch (monthly, weekly, yearly).
    - date: The date used for fetching statistics, in YYYY-MM-DD format.
    - user: The authenticated admin user, injected by dependency injection.

    Returns:
    - A response model containing a message and a list of stats.
    """
    try:
        # Create a new stripe payment subscription
        payment_plan_id = body.get("payment_plan_id")
        payment_method_id = body.get("payment_method_id")

        # Fetch the payment plan
        payment_plan = PaymentPlan.get_or_none(id=payment_plan_id)
        if not payment_plan:
            raise RecordNotFoundException(message="Payment plan not found")

        metadata = {"business_id": business.id, "payment_plan_id": payment_plan_id}

        amount_in_cents = int(payment_plan.amount * 100)  # Amount in cents

        # domain = f"{build_subdomain_url(APP_URL, 'admin')}/payments/{business_id}"

        # Create a PaymentIntent with the plan amount and the payment method from the frontend
        payment_intent = StripeClient.create_payment_intent(
            amount=amount_in_cents,
            currency=payment_plan.currency,
            payment_method=payment_method_id,
            metadata=metadata,
            confirmation_method="automatic",
        )

        return SuccessResponse(
            message="Subscription created successfully",
            data={
                "client_secret": payment_intent.client_secret,
                "payment_intent_id": payment_intent.id,
                "status": payment_intent.status,
            },
        )
    except Exception as e:
        # If any exception occurs, raise an HTTP 500 error with the exception details
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=str(e)
        )
