from fastapi import APIRouter, Depends, Query, status, Body, Request
from app.schema import PaginationResponse, SuccessResponse
from app.models import (
    Employee,
    Business,
    ScheduleInterview,
    Version,
    CandidateInterviewFeedback,
)
from app.validations import NumberValidate, StringValidate
from app.exceptions import RecordNotFoundException, HTTPException
from app.models.concern.enum import Interview<PERSON><PERSON>
from app.helper import <PERSON><PERSON><PERSON>uthHelper, InterviewHelper
from app.constants import ConstantMessages, NodeName, PermissionName
from app.models.concern.enum import CandidateInterviewStatus
from datetime import datetime, timedelta
import logging


async def get_interview(
    id: int,
    business: Business = Depends(WildcardAuthHelper.validate_subdomain),
    current_employee: Employee = Depends(WildcardAuthHelper.get_current_employee),
) -> ScheduleInterview:
    """
    Retrieve a ScheduleInterview by its ID and business context.

    Args:
        id (int): The ID of the interview to retrieve.
        business (Business): The business context provided by dependency injection.
        current_employee (Employee): The current employee provided by dependency injection.

    Returns:
        ScheduleInterview: The ScheduleInterview object if found.

    Raises:
        RecordNotFoundException: If the ScheduleInterview does not exist.
    """
    interview: ScheduleInterview = ScheduleInterview.get_or_none(
        id=id, business_id=business.id
    )
    if not interview:
        raise RecordNotFoundException(message="Interview does not exist")

    if not current_employee.is_admin and not (
        interview.created_by_id == current_employee.id
        or interview.interviewer_id == current_employee.id
    ):
        raise RecordNotFoundException(message="Not auuthorized to do this action")

    return interview


router = APIRouter(
    prefix="/interviews",
    tags=["Wilcard Interview API"],
    dependencies=[
        Depends(WildcardAuthHelper.get_current_employee),
        Depends(WildcardAuthHelper.get_current_auth_token),
    ],
)

# ------------------------------ router functions ------------------------------


# list of interviews
@router.get(
    "",
    summary="List of Interview",
    description="Retrieve a paginated list of interviews.",
    response_model=PaginationResponse,
)
def get_interviews(
    request: Request,
    page: int = Query(1, gt=0),
    limit: int = Query(10, gt=0, le=100),
    filter: str = Query(None, description="Filter Interviews by 'today' or 'upcoming'"),
    business: Business = Depends(WildcardAuthHelper.validate_subdomain),
    current_employee: Employee = Depends(WildcardAuthHelper.get_current_employee),
):
    """
    Retrieve a paginated list of interviews.

    Args:
        page (int): The page number for pagination. Defaults to 1.
        limit (int): The maximum number of interviews per page. Defaults to 10.

    Returns:
        PaginationResponse: Paginated list of interviews.
    """

    WildcardAuthHelper.has_route_permission(
        request=request,
        node_key=NodeName.EMPLOYEE_INTERVIEWS,
        perm_names=PermissionName.READ_ONLY,
    )
    try:
        # Fetch total count
        is_admin = current_employee.is_admin
        if is_admin:
            base_query = ScheduleInterview.select().where(
                ScheduleInterview.business_id == business.id
            )
        else:
            base_query = ScheduleInterview.select().where(
                (ScheduleInterview.business_id == business.id)
                & (
                    (ScheduleInterview.created_by_id == current_employee.id)
                    | (ScheduleInterview.interviewer_id == current_employee.id)
                )
            )

        if filter == "today":
            now = datetime.now()
            start = datetime(now.year, now.month, now.day)
            end = start + timedelta(days=1)
            base_query = base_query.where(
                (ScheduleInterview.interview_at >= start)
                & (ScheduleInterview.interview_at < end)
            )
        elif filter == "upcoming":
            now = datetime.now()
            base_query = base_query.where(ScheduleInterview.interview_at >= now)

        base_query = base_query.order_by(ScheduleInterview.id.desc())
        total_records = base_query.count()

        # Paginate results
        if total_records > 0:
            offset = (page - 1) * limit
            records = base_query.offset(offset).limit(limit)
        else:
            records = []

        # Prepare interview list
        rows = [record.info() for record in records]

        return PaginationResponse(
            data={"page": page, "limit": limit, "count": total_records, "rows": rows},
            message=ConstantMessages.FETCHED_SUCCESSFULLY,
        )
    except Exception as e:
        logging.error(f"Exception in interview list: {str(e)}")
        return PaginationResponse(
            data={"page": page, "limit": limit, "count": 0, "rows": []},
            message=f"Failed to fetch data: {str(e)}",
        )


@router.get("/{id}", summary="Get Interview by ID", response_model=SuccessResponse)
async def get_interview_detail(
    request: Request, id: int, interview: ScheduleInterview = Depends(get_interview)
):
    """
    Endpoint to retrieve ScheduleInterview details.

    Args:
        id (int): The ID of the interview to retrieve.
        interview (ScheduleInterview): The ScheduleInterview instance, provided by dependency injection.

    Returns:
        SuccessResponse: Success message along with ScheduleInterview details.
    """
    WildcardAuthHelper.has_route_permission(
        request=request,
        node_key=NodeName.EMPLOYEE_INTERVIEWS,
        perm_names=PermissionName.READ_ONLY,
    )

    try:
        return SuccessResponse(
            message="Interview details fetched successfully.",
            data=interview.info(),  # Assuming `info()` method exists on ScheduleInterview
        )
    except RecordNotFoundException as e:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail=str(e))
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_422_UNPROCESSABLE_ENTITY, detail=str(e)
        )


@router.put("/{id}", summary="Update Interview by ID", response_model=SuccessResponse)
async def update_interview_detail(
    request: Request,
    schedule_interview: ScheduleInterview = Depends(get_interview),
    current_employee: Employee = Depends(WildcardAuthHelper.get_current_employee),
    business: Business = Depends(WildcardAuthHelper.validate_subdomain),
    body: dict = Body(...),
):
    """
    Schedule an interview for a candidate.

    Args:
        candidate (Candidate): The candidate for whom the interview is being scheduled.
        current_employee (Employee): The current employee scheduling the interview.
        body (dict): The request body containing interview details.

    Returns:
        SuccessResponse: The response containing the success message and scheduled interview details.

    Raises:
        HTTPException: If there is an error during the scheduling process.
    """
    WildcardAuthHelper.has_route_permission(
        request=request,
        node_key=NodeName.EMPLOYEE_INTERVIEWS,
        perm_names=PermissionName.READ_EDIT,
    )

    try:
        status_name = schedule_interview.status.name
        if status_name not in ["Rescheduled", "Scheduled", "Awaiting Feedback"]:
            raise RecordNotFoundException(
                message=f"Modification is not allowed for interviews with status '{status_name}'."
            )

        interviewer_id = body.get("interviewer_id")
        if interviewer_id:
            interviewer = Employee.get_or_none(
                id=interviewer_id, business_id=business.id, status=1
            )
            if not interviewer:
                raise RecordNotFoundException(message="Interviewer does not exist")
        else:
            raise RecordNotFoundException(message="Interviewer must exist")

        interview_mode_id = NumberValidate(
            body.get("interview_mode_id"),
            field="interview_mode",
            allow_zero=True,
            min_value=0,
            max_value=2,
        )
        interview_mode = InterviewMode(interview_mode_id)
        is_video_call_interview = interview_mode.name == "Video_Call"
        # validating interview data
        interview_at = StringValidate(
            body.get("interview_at"), field="Interview Time", required=True, strip=False
        )
        meeting_link = StringValidate(
            body.get("meeting_link"),
            field="Meeting Link",
            required=is_video_call_interview,
            strip=True,
        )
        comment = StringValidate(
            body.get("comment"), field="Comment", required=False, strip=True
        )
        interview_round = NumberValidate(
            body.get("interview_round"),
            field="Interview Round",
            allow_zero=False,
            min_value=1,
            max_value=20,
        )
        interview_status = NumberValidate(
            body.get("status"),
            field="Interview Status",
            allow_zero=True,
            min_value=0,
            max_value=10,
        )
        interviewer_id = NumberValidate(
            body.get("interviewer_id"), field="Interviewer", allow_zero=False
        )
        phone_number = StringValidate(
            body.get("phone_number"),
            field="Phone Number",
            required=True,
            max_length=20,
            strip=True,
        )

        passing_percentage = NumberValidate(
            (body.get("passing_percentage") or 0),
            field="Passing Percentage",
            allow_zero=True,
            min_value=0,
            max_value=100,
        )

        time_duration = NumberValidate(
            (body.get("time_duration") or 0),
            field="Time Duration",
            allow_zero=True,
            min_value=0,
            max_value=500,
        )

        show_marks = body.get("show_marks")

        # adding data of scheduling interview
        schedule_interview.set_value("interview_at", interview_at)
        schedule_interview.set_value("meeting_link", meeting_link)
        schedule_interview.set_value("comment", comment)
        schedule_interview.set_value("interviewer_id", interviewer_id)
        schedule_interview.set_value("interview_mode", interview_mode.value)
        schedule_interview.set_value("interview_round", interview_round)
        schedule_interview.set_value("status", interview_status)

        schedule_interview.set_value("phone_number", phone_number)
        schedule_interview.set_value("passing_percentage", passing_percentage)
        schedule_interview.set_value("time_duration", time_duration)
        schedule_interview.set_value("show_marks", show_marks)

        if schedule_interview.is_dirty():
            schedule_interview.created_by_id = current_employee.id
            schedule_interview.save()

        # Send email to candidate
        if schedule_interview.is_changed:
            InterviewHelper.schedule_interview_email(
                schedule_interview=schedule_interview
            )

        # Return success response with schedule interview details
        return SuccessResponse(
            message=f"Interview {schedule_interview.status.name} Successfully.",
            data=schedule_interview.info(),
        )

    except Exception as e:
        print("error", e, "error")

        raise HTTPException(
            status_code=status.HTTP_422_UNPROCESSABLE_ENTITY, detail=str(e)
        )


# list of interviews
@router.get(
    "/{id}/histories",
    summary="List of Interview",
    description="Retrieve a paginated list of interviews histories.",
    response_model=PaginationResponse,
)
def get_interviews(
    request: Request,
    page: int = Query(1, gt=0),
    limit: int = Query(10, gt=0, le=100),
    schedule_interview: ScheduleInterview = Depends(get_interview),
):
    """
    Retrieve a paginated list of interviews.

    Args:
        page (int): The page number for pagination. Defaults to 1.
        limit (int): The maximum number of interviews per page. Defaults to 10.

    Returns:
        PaginationResponse: Paginated list of interviews.
    """
    WildcardAuthHelper.has_route_permission(
        request=request,
        node_key=NodeName.EMPLOYEE_INTERVIEWS,
        perm_names=PermissionName.READ_ONLY,
    )

    try:
        # Fetch total count
        base_query = Version.where(
            {
                "model_name": schedule_interview.__class__.__name__,
                "model_id": schedule_interview.id,
            }
        )

        base_query = base_query.order_by(Version.id.desc())
        total_records = base_query.count()

        # Paginate results
        if total_records > 0:
            offset = (page - 1) * limit
            records = base_query.offset(offset).limit(limit)
        else:
            records = []

        rows = []
        # Prepare interview list
        for record in records:
            class_object = record.record_instance()
            rows.append(
                {
                    **record.info(),
                    **class_object.info(),
                }
            )

        return PaginationResponse(
            data={"page": page, "limit": limit, "count": total_records, "rows": rows},
            meta={"interview": schedule_interview.info()},
            message=ConstantMessages.FETCHED_SUCCESSFULLY,
        )
    except Exception as e:
        logging.error(f"Exception in interview history list: {str(e)}")
        return PaginationResponse(
            data={"page": page, "limit": limit, "count": 0, "rows": []},
            message=f"Failed to fetch data: {str(e)}",
        )


@router.get(
    "/{id}/feedback",
    summary="List of Interview Feedbacks",
    description="Retrieve a paginated list of interviews.",
    response_model=PaginationResponse,
)
async def get_interview_feedbacks(
    request: Request,
    page: int = Query(1, gt=0),
    limit: int = Query(10, gt=0, le=100),
    schedule_interview: ScheduleInterview = Depends(get_interview),
):
    """
    Retrieve a paginated list of interview feedbacks.

    Args:
        page (int): The page number for pagination. Defaults to 1.
        limit (int): The maximum number of interviews per page. Defaults to 10.

    Returns:
        PaginationResponse: Paginated list of interview feedbacks.
    """
    WildcardAuthHelper.has_route_permission(
        request=request,
        node_key=NodeName.EMPLOYEE_INTERVIEWS,
        perm_names=PermissionName.READ_ONLY,
    )

    try:
        # Fetch total count
        base_query = CandidateInterviewFeedback.where(
            {"schedule_interview_id": schedule_interview.id}
        )

        base_query = base_query.order_by(CandidateInterviewFeedback.id.desc())
        total_records = base_query.count()

        # Paginate results
        if total_records > 0:
            offset = (page - 1) * limit
            records = base_query.offset(offset).limit(limit)
        else:
            records = []

        # Prepare interview list
        rows = [record.info() for record in records]

        return PaginationResponse(
            data={"page": page, "limit": limit, "count": total_records, "rows": rows},
            message="Data fetched successfully",
        )
    except Exception as e:
        logging.error(f"Exception in interview list: {str(e)}")
        return PaginationResponse(
            data={"page": page, "limit": limit, "count": 0, "rows": []},
            message=f"Failed to fetch data: {str(e)}",
        )


@router.post(
    "/{id}/feedback",
    summary="Give Interview Feedback by ID",
    response_model=SuccessResponse,
)
async def save_interview_feedback(
    request: Request,
    schedule_interview: ScheduleInterview = Depends(get_interview),
    current_employee: Employee = Depends(WildcardAuthHelper.get_current_employee),
    business: Business = Depends(WildcardAuthHelper.validate_subdomain),
    body: dict = Body(...),
):
    """
    Schedule an interview for a candidate.

    Args:
        candidate (Candidate): The candidate for whom the interview is being scheduled.
        current_employee (Employee): The current employee scheduling the interview.
        body (dict): The request body containing interview details.

    Returns:
        SuccessResponse: The response containing the success message and scheduled interview details.

    Raises:
        HTTPException: If there is an error during the scheduling process.
    """
    WildcardAuthHelper.has_route_permission(
        request=request,
        node_key=NodeName.EMPLOYEE_INTERVIEWS,
        perm_names=PermissionName.READ_WRITE,
    )

    try:
        # validating interview data
        interview_round = NumberValidate(
            body.get("interview_round"),
            field="Interview Round",
            allow_zero=False,
            min_value=1,
            max_value=20,
        )

        interview_feedback = CandidateInterviewFeedback.get_or_none(
            CandidateInterviewFeedback.interview_round == interview_round,
            CandidateInterviewFeedback.candidate_id == schedule_interview.candidate_id,
            CandidateInterviewFeedback.schedule_interview_id == schedule_interview.id,
            CandidateInterviewFeedback.created_by_id == current_employee.id,
        )

        if interview_feedback:
            raise ValueError("Feedback already saved")

        feedback = StringValidate(
            body.get("feedback"), field="Feedback", required=False, strip=True
        )
        rating = NumberValidate(
            body.get("rating"),
            field="Rating",
            allow_zero=False,
            min_value=1,
            max_value=5,
        )

        # save interview feedback
        CandidateInterviewFeedback.create(
            feedback=feedback,
            rating=rating,
            interview_round=interview_round,
            candidate_id=schedule_interview.candidate_id,
            schedule_interview_id=schedule_interview.id,
            created_by_id=current_employee.id,
        )
        # Return success response with schedule interview details
        return SuccessResponse(
            message=f"Interview Feedback Saved Successfully.",
            data=schedule_interview.info(),
        )

    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_422_UNPROCESSABLE_ENTITY, detail=str(e)
        )


@router.put(
    "/{id}/status", summary="Get Interview by ID", response_model=SuccessResponse
)
async def get_interview_detail(
    request: Request,
    interview: ScheduleInterview = Depends(get_interview),
    body: dict = Body(...),
):
    """
    Endpoint to retrieve CandidateInterview details.

    Args:
        id (int): The ID of the interview to retrieve.
        interview (CandidateInterview): The CandidateInterview instance, provided by dependency injection.

    Returns:
        SuccessResponse: Success message along with CandidateInterview details.
    """
    WildcardAuthHelper.has_route_permission(
        request=request,
        node_key=NodeName.EMPLOYEE_INTERVIEWS,
        perm_names=PermissionName.READ_EDIT,
    )

    try:
        interview_status = body.get("status")
        interview.status = interview_status
        interview.save()

        if interview.status.name == "Rejected":
            candidate_interview = interview.candidate_interview
            # update as rejected candidate
            candidate_interview.status = CandidateInterviewStatus(2)
            candidate_interview.save()

        return SuccessResponse(
            message="Status updated successfully.", data=interview.info()
        )
    except RecordNotFoundException as e:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail=str(e))
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_422_UNPROCESSABLE_ENTITY, detail=str(e)
        )
