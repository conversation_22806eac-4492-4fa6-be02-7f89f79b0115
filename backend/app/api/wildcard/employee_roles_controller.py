from fastapi import APIRouter, Depends, Query, HTTPException, status, Body
from app.schema import SuccessResponse
from app.models import EmployeeRole, Business
from app.helper import WildcardAuthHelper
from app.validations.field_validations import StringValidate
from app.constants import ConstantMessages
from app.exceptions import CustomValidationError
from peewee import fn
from typing import Optional
import logging

router = APIRouter(
    prefix="/employee_roles",
    tags=["Wilcard Employees Role API"],
    dependencies=[
        Depends(WildcardAuthHelper.get_current_employee),
        Depends(WildcardAuthHelper.get_current_auth_token),
    ],
)


# ------------------------------ router functions ------------------------------
@router.get(
    "",
    summary="List of employee Roles",
    description="List of Employee Roles",
    response_model=SuccessResponse,
)
async def get_employee_roles(
    business: Business = Depends(WildcardAuthHelper.validate_subdomain),
):
    """
    Retrieve employee role data.

    Args:
    - user: The authenticated admin user, injected by dependency injection.
    Returns:
    - A response model containing a message and a list of employee roles.
    """
    try:
        employee_roles = EmployeeRole.select().where(
            (EmployeeRole.name != "Super Admin")
            & (
                (EmployeeRole.business_id == business.id)
                | (EmployeeRole.business_id.is_null())
            )
        )
        rows = [record.info() for record in employee_roles]
        return SuccessResponse(
            message="Employee Roles fetched successfully.", data=rows
        )
    except Exception as e:
        # If any exception occurs, raise an HTTP 500 error with the exception details
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=str(e)
        )


@router.post(
    "",
    summary="Create a new role",
    description="Create a new role for perticular business.",
    response_model=SuccessResponse,
    dependencies=[Depends(WildcardAuthHelper.employee_is_admin)],
)
async def selected_page_permissions(
    business: Business = Depends(WildcardAuthHelper.validate_subdomain),
    body: dict = Body(...),
):
    """
    Retrieve a list of all nodes available in the system.
    """
    try:
        role = StringValidate(
            body.get("role"),
            field="Role",
            required=True,
            max_length=25,
            strip=True,
            pattern=r"[A-Za-z ]+",
            pattern_error="field only allows alphabets only",
        )

        EmployeeRole.check_record_exist_with_lower(
            EmployeeRole.name, role, business_id=None
        )

        EmployeeRole.check_record_exist_with_lower(
            EmployeeRole.name, role, business_id=business.id
        )

        employee_role = EmployeeRole.create(
            name=role,
            business_id=business.id,
        )

        return SuccessResponse(
            message="New Role added successfully", data=employee_role.info()
        )
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_422_UNPROCESSABLE_ENTITY, detail=str(e)
        )
    except Exception as e:
        raise CustomValidationError(error=ConstantMessages.SOMETHING_WENT_WRONG)


@router.get(
    "/options/all",
    summary="Get Employee Roles as Options",
    description="Retrieve a options list of employee roles.",
    response_model=SuccessResponse,
)
def get_employee_roles_options(
    page: int = Query(1, gt=0),
    limit: int = Query(10, gt=0, le=100),
    search: Optional[str] = Query(
        None, description="Search term to filter employee roles"
    ),
    showId: Optional[int] = Query(None, description="ID to prioritize in the ordering"),
    business: Business = Depends(WildcardAuthHelper.validate_subdomain),
):
    """
    Retrieve a options list of employee roles..

    Args:
        page (int): The page number for pagination. Defaults to 1.
        limit (int): The maximum number of employee roles per page. Defaults to 10.
        search (Optional[str]): Search term to filter employee roles.
        showId (Optional[int]): ID to prioritize in the ordering.
    Returns:
    """
    try:
        base_query = EmployeeRole.select().where(
            (EmployeeRole.name != "Super Admin")
            & (
                (EmployeeRole.business_id == business.id)
                | (EmployeeRole.business_id.is_null())
            )
        )

        if search:
            search = search.strip().lower()
            base_query = base_query.where(fn.LOWER(EmployeeRole.name).contains(search))

        if showId is not None:
            base_query = base_query.orwhere(EmployeeRole.id == showId)
            base_query = base_query.order_by(
                fn.IF(EmployeeRole.id == showId, 0, 1), EmployeeRole.id
            )

        offset = (page - 1) * limit
        records = base_query.offset(offset).limit(limit)
        rows = [{"label": record.name, "value": record.id} for record in records]

        return SuccessResponse(data=rows, message="Options fetched successfully")
    except Exception as e:
        logging.error(f"Exception in employee role list: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to fetch data: {str(e)}")
