from fastapi import APIRouter, Depends, Request, Response, HTTPException, status
from app.schema import SuccessResponse
from app.exceptions import CustomValidationError, RecordNotFoundException
from app.models import (
    Business,
    Employee,
    AuthToken,
)
from app.helper import WildcardAuthHelper
from app.constants import ConstantMessages
import logging
from peewee import fn


def get_business(
    id: int,
    current_business: Business = Depends(WildcardAuthHelper.validate_subdomain),
) -> Business:
    business = None
    business = (
        Business.select()
        .where(
            Business.id == id,
            Business.user_id == current_business.user_id,
            Business.status == 1,
            Business.payment_status == 1,
        )
        .get()
    )
    if not business:
        raise RecordNotFoundException(message="Business not found or is inactive")
    return business


# Create the router
router = APIRouter(
    prefix="/businesses",
    tags=["Businesses (Super Admin's Only)"],
    dependencies=[
        Depends(WildcardAuthHelper.get_current_employee),
        Depends(WildcardAuthHelper.get_current_auth_token),
        Depends(WildcardAuthHelper.employee_is_super_admin),
    ],
)


@router.get(
    "",
    summary="Get List of businesses Available",
    description="Reterieve list of businesses.",
    response_model=SuccessResponse,
    dependencies=[
        Depends(WildcardAuthHelper.get_current_employee),
        Depends(WildcardAuthHelper.employee_is_super_admin),
    ],
)
async def businesses_list(
    business: Business = Depends(WildcardAuthHelper.validate_subdomain),
):
    """
    Retrieve a list of all activated business available.
    """
    try:
        businesses = Business.select().where(
            Business.user_id == business.user_id,
            Business.status == 1,
            Business.payment_status == 1,
        )
        businesses = businesses.order_by(
            fn.IF(Business.id == business.id, 0, 1), Business.id
        )
        data = [buiz.info() for buiz in businesses]
        return SuccessResponse(message="Data updated successfully", data=data)
    except Exception as e:
        logging.error(f"Permisions Page {str(e)}")
        raise CustomValidationError(error=ConstantMessages.SOMETHING_WENT_WRONG)


@router.post(
    "/{id}/portal_login",
    response_model=SuccessResponse,
    summary="Business Portal Login",
    description="Wildcard business portal login.",
    dependencies=[
        Depends(WildcardAuthHelper.get_current_employee),
        Depends(WildcardAuthHelper.employee_is_super_admin),
    ],
)
async def update_business_status(
    request: Request,
    response: Response,
    business: Business = Depends(get_business),
    current_employee: Employee = Depends(WildcardAuthHelper.get_current_employee),
):
    """
    Business Portal Login.

    Args:
        id (int): The ID of the business.
        business (Business): The business instance, provided by dependency injection.

    Returns:
        SuccessResponse: The response containing the result message and updated business information.
    """
    try:
        employee = Employee.get_or_none(
            Employee.business_id == business.id,
            Employee.created_by_id == None,
            Employee.email == current_employee.email,
        )
        token = await WildcardAuthHelper.get_cookie_value(
            request=request, custom_subdomain=business.subdomain
        )
        auth_token = None
        if token:
            auth_token = AuthToken.get_or_none(
                AuthToken.id == token,
                AuthToken.object_type == employee.__class__.__name__,
                AuthToken.object_id == employee.id,
            )

        if not auth_token:
            auth_token = AuthToken.create(
                object_type=employee.__class__.__name__, object_id=employee.id
            )

        auth_token.update_expiration()
        access_token = (
            auth_token.id
        )  # Assign the id of the created auth token to access_token

        # Set the auth token cookie with the newly created access_token
        await WildcardAuthHelper.set_auth_token_cookie(
            request=request,
            response=response,
            access_token=access_token,
            custom_subdomain=business.subdomain,
        )
        return SuccessResponse(message="Logged in successfully.")
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_422_UNPROCESSABLE_ENTITY, detail=str(e)
        )


@router.get(
    "/detail",
    response_model=SuccessResponse,
    summary="Business Portal Detail",
    description="Wildcard business Portal Detail",
    dependencies=[
        Depends(WildcardAuthHelper.get_current_employee),
        Depends(WildcardAuthHelper.employee_is_super_admin),
    ],
)
async def get_business_info(
    business: Business = Depends(WildcardAuthHelper.validate_subdomain),
):
    """
    Business Portal Login.

    Args:
        id (int): The ID of the business.
        business (Business): The business instance, provided by dependency injection.

    Returns:
        SuccessResponse: The response containing the result message and updated business information.
    """
    try:
        business_info = business.info()
        business_payment = business.current_payment

        expired_soon = False
        if business.total_credits > 0:
            remaining_credits = business.remaining_credits
            threshold = 0.2 * business.total_credits
            expired_soon = remaining_credits <= threshold and remaining_credits <= 40

        return SuccessResponse(
            message="Logged in successfully.",
            data={
                "business": business_info,
                "payment": business_payment and business_payment.info(),
                "payment_plan": business_payment
                and business_payment.payment_plan.info(),
                "expired_soon": expired_soon,
            },
        )
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_422_UNPROCESSABLE_ENTITY, detail=str(e)
        )
