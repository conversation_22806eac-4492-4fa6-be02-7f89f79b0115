from app.celery_app import celery_app
from app.tasks.base_task import BaseTask
from app.mailer import Candidate<PERSON><PERSON><PERSON>
from app.helper.job_helper import <PERSON><PERSON><PERSON><PERSON>
from datetime import datetime


class CandidateTask(BaseTask):
    @celery_app.task(bind=True, queue="high_priority")
    def send_document_request_link(
        cls, candidate_id: int, employee_id: int, columns: dict
    ):
        """Send the document upload request link."""
        from app.models.employee import Employee
        from app.models.candidate import Candidate

        candidate = Candidate.get_by_id(candidate_id)
        employee = Employee.get_by_id(employee_id)
        CandidateMailer.send_document_request_link(
            candidate=candidate, employee=employee, columns=columns
        )

    @celery_app.task(bind=True, queue="emails")
    def interview_scheduled_mail(cls, candidate_id: int, schedule_interview_id: int):
        """Send email about the scheduled interview."""
        from app.models.schedule_interview import ScheduleInterview
        from app.models.candidate import Candidate

        candidate = Candidate.get_by_id(candidate_id)
        schedule_interview = ScheduleInterview.get_by_id(schedule_interview_id)

        print("start CandidateTask.interview_scheduled_mail ")
        print(f"candidate_id: {candidate_id} - {candidate}")
        print(f"schedule_interview: {schedule_interview_id} - {schedule_interview}")

        CandidateMailer.interview_scheduled_mail(
            candidate=candidate, schedule_interview=schedule_interview
        )

    @celery_app.task(bind=True, queue="emails")
    def interview_reminder_mail(cls, candidate_id: int, schedule_interview_id: int):
        """Send email about the scheduled interview."""
        from app.models.schedule_interview import ScheduleInterview

        schedule_interview = ScheduleInterview.get_or_none(
            ScheduleInterview.id == schedule_interview_id,
            ScheduleInterview.candidate_id == candidate_id,
        )

        print("start CandidateTask.interview_reminder_mail ")
        print(f"candidate_id: {candidate_id} ")
        print(f"schedule_interview: {schedule_interview_id} - {schedule_interview}")

        if (
            schedule_interview
            and schedule_interview.interview_at > datetime.utcnow()
            and schedule_interview.status.name.strip().lower() == "scheduled"
        ):
            CandidateMailer.interview_reminder_mail(
                schedule_interview=schedule_interview
            )

        else:
            print("Skipping email: either interview is past or not scheduled.")

    @celery_app.task(bind=True, queue="high_priority")
    def send_custom_email(cls, candidate_id: int, subject: str, content: str):
        """Send a custom email to the candidate."""
        from app.models.candidate import Candidate

        candidate = Candidate.get_by_id(candidate_id)
        CandidateMailer.send_custom_email(
            candidate=candidate, subject=subject, content=content
        )

    @celery_app.task(bind=True, queue="high_priority")
    def reset_password_email(cls, candidate_id: int):
        """Send password reset OTP email."""
        from app.models.candidate import Candidate

        candidate = Candidate.get_by_id(candidate_id)
        CandidateMailer.reset_password_email(candidate=candidate)

    @celery_app.task(bind=True, queue="high_priority")
    def resend_otp_email(cls, candidate_id: int, resend_text: str):
        """Resend OTP to the candidate."""
        from app.models.candidate import Candidate

        candidate = Candidate.get_by_id(candidate_id)
        CandidateMailer.resend_otp_email(candidate=candidate, resend_text=resend_text)

    @celery_app.task(bind=True, queue="high_priority")
    def send_offer_letter_email(
        cls, candidate_id: int, offer_letter_id: int, subject: str
    ):
        """Resend OTP to the candidate."""
        from app.models.candidate import Candidate
        from app.models.candidites_offer_letter import CandidateOfferLetter

        candidate = Candidate.get_by_id(candidate_id)
        offer_letter = CandidateOfferLetter.get_or_none(
            id=offer_letter_id, candidate_id=candidate_id
        )
        CandidateMailer.send_offer_letter_email(
            candidate=candidate, offer_letter=offer_letter, subject=subject
        )

    @celery_app.task(bind=True, queue="high_priority")
    def extract_candidate_scores(cls, candidate_id: int):
        """
        Extracts opportunities score with candidate.
        :param candidate_id: Candidate ID
        """

        JobHelper.extract_candidate_scores(candidate_id=candidate_id)
        return f"Candidate score extracted for ids: {candidate_id}"
