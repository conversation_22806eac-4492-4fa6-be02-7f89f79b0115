from fastapi import Request, Body, HTTPException, status, Response
from app.exceptions import UnauthorizedException
from app.helper.concern.auth_helper_concern import AuthHelperConcern
from app.context import set_whodoneit, set_request_headers
from app.constants import ConstantMessages
from typing import List


class WildcardAuthHelper(AuthHelperConcern):
    role = "employee"
    from app.models.employee import Employee
    from app.models.business import Business

    @staticmethod
    async def validate_subdomain(subdomain: str, request: Request) -> Business:
        try:
            if hasattr(request.state, "current_business"):
                return request.state.current_business
            if not request.state.subdomain:
                request.state.subdomain = subdomain
                current_subdomain = subdomain
            elif request.state.subdomain == "api":
                current_subdomain = subdomain
            else:
                current_subdomain = request.state.subdomain
            business = WildcardAuthHelper.get_business(current_subdomain)
            request.state.current_business = business
            return business
        except UnauthorizedException as e:
            raise e
        except Exception:
            raise UnauthorizedException(error="Unauthorized.")

    @staticmethod
    async def authenticate_employee(
        business_id: int, email: str, password: str
    ) -> Employee:
        from app.models.employee import Employee

        employee = Employee.authenticate(
            business_id=business_id, email=email.lower(), password=password
        )
        return employee

    @staticmethod
    async def get_email_otp_employee(
        request: Request, body: dict = Body(...)
    ) -> Employee:
        subdomain = request and request.state and request.state.subdomain
        if subdomain is None:
            raise UnauthorizedException(error=ConstantMessages.USER_DOES_NOT_EXIST)
        business = WildcardAuthHelper.get_business(subdomain)
        email = WildcardAuthHelper.get_otp_email(request=request, body=body)
        from app.models.employee import Employee

        user = Employee.get_or_none(
            email=email.lower(), status=1, business_id=business.id
        )
        if user is None:
            raise UnauthorizedException(error=ConstantMessages.USER_DOES_NOT_EXIST)
        return user

    @staticmethod
    async def get_current_business(subdomain: str, request: Request) -> Business:
        return WildcardAuthHelper.validate_subdomain(subdomain, request)

    @staticmethod
    async def get_current_employee(request: Request, response: Response) -> Employee:
        try:
            if hasattr(request.state, "current_employee"):
                return request.state.current_employee
            auth_token = await WildcardAuthHelper.get_current_auth_token(
                request=request,
                response=response,
                api_key_token=request.headers.get("X-API-KEY"),
            )
            if not auth_token or auth_token.object_type != "Employee":
                raise UnauthorizedException(error=ConstantMessages.AUTH_FAILED)
            if auth_token.is_expired():
                raise UnauthorizedException(error=ConstantMessages.SESSION_EXPIRED)
            from app.models import Employee

            current_business = request.state.current_business
            current_employee = Employee.get_or_none(
                id=auth_token.object_id, business_id=current_business.id, status=1
            )
            if not current_employee:
                raise UnauthorizedException(error=ConstantMessages.AUTH_FAILED)
            request.state.current_employee = current_employee

            # save in thread request headers and current logged in user
            from app.helper.request_header_helper import RequestHeaderHelper

            request_data = RequestHeaderHelper.get_request_ip_basic(request)
            set_whodoneit(current_employee)
            set_request_headers(request_data)

            return current_employee
        except UnauthorizedException as e:
            raise e
        except Exception:
            raise UnauthorizedException(error=ConstantMessages.AUTH_FAILED)

    @staticmethod
    async def employee_is_super_admin(request: Request, response=Response) -> True:
        try:
            if hasattr(request.state, "current_employee"):
                current_employee = request.state.current_employee
            else:
                auth_token = await WildcardAuthHelper.get_current_auth_token_only(
                    request=request
                )
                if not auth_token or auth_token.object_type != "Employee":
                    raise UnauthorizedException(error=ConstantMessages.AUTH_FAILED)
                if auth_token.is_expired():
                    raise UnauthorizedException(error=ConstantMessages.SESSION_EXPIRED)
                from app.models import Employee

                current_business = request.state.current_business
                current_employee = Employee.get_or_none(
                    id=auth_token.object_id, business_id=current_business.id, status=1
                )
                if not current_employee:
                    raise UnauthorizedException(error=ConstantMessages.AUTH_FAILED)

            # Check if employee is a Super Admin
            if current_employee.employee_role.name != "Super Admin":
                raise UnauthorizedException(error=ConstantMessages.PERMISSION_DENIED)
            return True
        except UnauthorizedException as e:
            raise e
        except Exception as e:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=ConstantMessages.INTERNAL_SERVER_ERROR,
            )

    @staticmethod
    async def employee_is_admin(request: Request) -> bool:
        try:
            # Retrieve current employee from request state
            if hasattr(request.state, "current_employee"):
                current_employee = request.state.current_employee
            else:
                auth_token = await WildcardAuthHelper.get_current_auth_token_only(
                    request=request
                )
                if not auth_token or auth_token.object_type != "Employee":
                    raise UnauthorizedException(error=ConstantMessages.AUTH_FAILED)
                if auth_token.is_expired():
                    raise UnauthorizedException(error=ConstantMessages.SESSION_EXPIRED)
                from app.models import Employee

                current_business = request.state.current_business
                current_employee = Employee.get_or_none(
                    id=auth_token.object_id, business_id=current_business.id, status=1
                )
                if not current_employee:
                    raise UnauthorizedException(error=ConstantMessages.AUTH_FAILED)

            # Check if employee is a Super Admin
            if not current_employee.is_admin:
                raise UnauthorizedException(error=ConstantMessages.PERMISSION_DENIED)
            return True
        except UnauthorizedException as e:
            raise e
        except Exception as e:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=ConstantMessages.INTERNAL_SERVER_ERROR,
            )

    @staticmethod
    def has_route_permission(
        request: Request, node_key: str, perm_names: List[str] = []
    ) -> bool:
        try:
            if hasattr(request.state, "current_employee"):
                employee = request.state.current_employee
            else:
                raise HTTPException(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    detail=ConstantMessages.PERMISSION_DENIED,
                )

            from app.models.employee_role import EmployeeRole
            from app.models.permission import Permission
            from app.models.node import Node
            from app.models.business_employee_role_permission import (
                BusinessEmployeeRolePermission,
            )

            # business id extract
            business_id = employee.business_id
            employee_role_id = employee.employee_role_id

            role = EmployeeRole.get_by_id(employee_role_id)
            if role.name == "Super Admin":
                return True
            node = Node.get_or_none(Node.unique_key == node_key)
            if not node:
                return False

            perm_query = Permission.select().where(Permission.name.in_(perm_names))
            perm_ids = Permission.pluck_from_query(perm_query, "id")

            main_query = BusinessEmployeeRolePermission.select().where(
                BusinessEmployeeRolePermission.business_id == business_id,
                BusinessEmployeeRolePermission.node_id == node.id,
                BusinessEmployeeRolePermission.permission_id.in_(perm_ids),
            )

            perm_access_ids = BusinessEmployeeRolePermission.pluck_from_query(
                main_query, "permission_id"
            )
            if set(perm_ids) == set(perm_access_ids):
                return True
            else:
                raise HTTPException(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    detail=ConstantMessages.PERMISSION_DENIED,
                )

        except Exception as e:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=ConstantMessages.PERMISSION_DENIED,
            )
