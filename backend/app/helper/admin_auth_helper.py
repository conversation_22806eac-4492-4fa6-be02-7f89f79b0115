from fastapi import Request, Body, Response, status
from app.exceptions import UnauthorizedException, HTTPException
from app.helper.concern.auth_helper_concern import AuthHelperConcern
from app.context import set_whodoneit, set_request_headers
from app.constants import ConstantMessages


class AdminAuthHelper(AuthHelperConcern):
    role = "admin"
    from app.models.user import User

    @staticmethod
    async def authenticate_user(email: str, password: str) -> User:
        from app.models.user import User

        user = User.authenticate(email=email.lower(), password=password)
        return user

    @staticmethod
    async def get_email_otp_admin(request: Request, body: dict = Body(...)) -> User:
        email = AdminAuthHelper.get_otp_email(request=request, body=body)
        from app.models.user import User

        user = User.get_or_none(email=email.lower(), status=1)
        if user is None:
            raise UnauthorizedException(error=ConstantMessages.USER_DOES_NOT_EXIST)
        return user

    @staticmethod
    async def get_admin_user(request: Request, response: Response) -> User:
        try:
            if hasattr(request.state, "current_user"):
                return request.state.current_user
            auth_token = await AdminAuthHelper.get_current_auth_token(
                request=request,
                response=response,
                api_key_token=request.headers.get("X-API-KEY"),
            )
            if not auth_token or auth_token.object_type != "User":
                raise UnauthorizedException(error=ConstantMessages.AUTH_FAILED)
            if auth_token.is_expired():
                raise UnauthorizedException(error=ConstantMessages.SESSION_EXPIRED)
            from app.models import User

            user = User.get_or_none(id=auth_token.object_id, status=1)
            if not user:
                raise UnauthorizedException(error=ConstantMessages.AUTH_FAILED)
            request.state.current_user = user

            # save in thread request headers and current logged in user
            from app.helper.request_header_helper import RequestHeaderHelper

            request_data = RequestHeaderHelper.get_request_ip_basic(request)
            set_whodoneit(user)
            set_request_headers(request_data)

            return user
        except UnauthorizedException as e:
            raise e
        except Exception:
            raise UnauthorizedException(error=ConstantMessages.AUTH_FAILED)

    @staticmethod
    async def user_is_super_admin(request: Request) -> bool:
        try:
            if hasattr(request.state, "current_user"):
                current_user = request.state.current_user
            else:
                auth_token = await AdminAuthHelper.get_current_auth_token_only(
                    request=request
                )
                if not auth_token or auth_token.object_type != "User":
                    raise UnauthorizedException(error=ConstantMessages.AUTH_FAILED)
                if auth_token.is_expired():
                    raise UnauthorizedException(error=ConstantMessages.SESSION_EXPIRED)
                from app.models import User

                current_user = User.get_or_none(id=auth_token.object_id, status=1)
                if not current_user:
                    raise UnauthorizedException(error=ConstantMessages.AUTH_FAILED)

            # Check if User is a Super Admin
            if current_user.user_type.name != "Super Admin":
                raise UnauthorizedException(error=ConstantMessages.PERMISSION_DENIED)
            return True
        except UnauthorizedException as e:
            raise e
        except Exception as e:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=ConstantMessages.INTERNAL_SERVER_ERROR,
            )
