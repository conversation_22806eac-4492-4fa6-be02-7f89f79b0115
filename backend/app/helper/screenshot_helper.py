import base64
import io
import mimetypes
from starlette.datastructures import UploadFile, Headers as UploadFileHeaders
import logging


class ScreenshotHelper:
    @staticmethod
    def create_upload_file_from_base64(
        base64_string: str, filename_prefix: str = "screenshot"
    ) -> UploadFile:
        """
        Parses a base64 image string and returns an UploadFile object.
        """
        try:
            if "," not in base64_string:
                raise ValueError("Invalid base64 format.")

            header, encoded = base64_string.split(",", 1)
            mime_type = header.split(";")[0].split(":")[1]
            extension = mimetypes.guess_extension(mime_type) or ".png"

            decoded_bytes = base64.b64decode(encoded)
            buffer = io.BytesIO(decoded_bytes)

            filename = f"{filename_prefix}{extension}"

            headers = UploadFileHeaders(
                {
                    "content-type": mime_type,
                    "content-disposition": f'form-data; name="file"; filename="{filename}"',
                }
            )

            return UploadFile(filename=filename, file=buffer, headers=headers)

        except Exception as e:
            logging.warning(f"Failed to parse screenshot: {str(e)}")
            raise
