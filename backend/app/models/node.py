from app.models.base import ActiveRecord
from peewee import (
    ForeignKeyField,
    CharField,
    DateTimeField,
    BigIntegerField,
    SQL,
    TextField,
)
from typing import Optional


class Node(ActiveRecord):
    # to prevent circular import

    id = <PERSON>Integer<PERSON>ield(primary_key=True)
    name = <PERSON><PERSON><PERSON><PERSON>(max_length=255, null=False)
    unique_key = Char<PERSON>ield(max_length=255, null=False, unique=True)
    path = Char<PERSON>ield(max_length=255, null=True)
    icon = Cha<PERSON><PERSON><PERSON>(max_length=255, null=True)
    type = Char<PERSON>ield(max_length=255, null=False)
    singular_name = Char<PERSON>ield(max_length=255, null=False)
    description = TextField()
    parent_id = BigIntegerField(null=True)
    order_num = BigIntegerField(null=True)
    created_at = DateTimeField(constraints=[SQL("DEFAULT CURRENT_TIMESTAMP")])
    updated_at = DateTimeField(constraints=[SQL("DEFAULT CURRENT_TIMESTAMP")])

    # associations
    parent = ForeignKeyField("self", backref="childrens", null=True, lazy_load=True)

    def info(self):
        childrens = [record.info() for record in self.childrens]
        return {
            "id": self.id,
            "name": self.name,
            "unique_key": self.unique_key,
            "path": self.path,
            "icon": self.icon,
            "type": self.type,
            "singular_name": self.singular_name,
            "description": self.description,
            "childrens": childrens,
        }

    @classmethod
    def get_permissions_list_employee_nodes(
        cls, employee_role_id: int, business_id: int
    ):
        from app.models.permission import Permission
        from app.models.business_employee_role_permission import (
            BusinessEmployeeRolePermission,
        )
        from app.models.node import Node

        permissions_dict = {}

        not_in_nodes = set(["Dashboard", "Role Permissions", "Signature Managements"])
        nodes = Node.select(Node).where(
            Node.type == "EmployeeNode", Node.name.not_in(not_in_nodes)
        )
        permissions_query = (
            BusinessEmployeeRolePermission.select(
                BusinessEmployeeRolePermission.node_id, Permission.id
            )
            .join(
                Permission,
                on=(BusinessEmployeeRolePermission.permission_id == Permission.id),
            )
            .where(
                BusinessEmployeeRolePermission.employee_role_id == employee_role_id,
                BusinessEmployeeRolePermission.business_id == business_id,
            )
        )

        for bperm in permissions_query:
            if bperm.node_id not in permissions_dict:
                permissions_dict[bperm.node_id] = []
            permissions_dict[bperm.node_id].append(bperm.permission_id)

        result = []
        nodes = nodes.order_by(Node.order_num.asc())
        for node in nodes:
            node_permissions = permissions_dict.get(node.id, [])
            result.append(
                {
                    "id": node.id,
                    "name": node.name,
                    "unique_key": node.unique_key,
                    "path": node.path,
                    "icon": node.icon,
                    "type": node.type,
                    "permissions": node_permissions,
                }
            )

        return result

    @classmethod
    def get_employee_dependent_permissions(cls):
        not_in_nodes = set(["Dashboard", "Role Permissions", "Signature Managements"])
        nodes = Node.select(Node).where(
            Node.type == "EmployeeNode", Node.name.not_in(not_in_nodes)
        )
        from app.models.permission import Permission

        read_permission = Permission.get(name="read")
        write_permission = Permission.get(name="write")
        edit_permission = Permission.get(name="edit")
        delete_permission = Permission.get(name="delete")

        required_permissions: dict = {}

        for node in nodes:
            if node.id not in required_permissions:
                required_permissions[node.id] = [
                    {
                        "permission_id": write_permission.id,
                        "node_id": node.id,
                        "dependent_permission_id": read_permission.id,
                        "tooltip": f"{node.name} Write permission requires {node.name} read permission.",
                    },
                    {
                        "permission_id": edit_permission.id,
                        "node_id": node.id,
                        "dependent_permission_id": read_permission.id,
                        "tooltip": f"{node.name} Edit permission requires {node.name} read permission.",
                    },
                    {
                        "permission_id": delete_permission.id,
                        "node_id": node.id,
                        "dependent_permission_id": read_permission.id,
                        "tooltip": f"{node.name} Delete permission requires {node.name} read permission.",
                    },
                ]
                if node.unique_key == "employee_interviews":
                    candidate_node = Node.get_or_none(
                        unique_key="employee_candidates", type="EmployeeNode"
                    )
                    if candidate_node is not None:
                        required_permissions[node.id].append(
                            {
                                "permission_id": write_permission.id,
                                "node_id": candidate_node.id,
                                "dependent_permission_id": read_permission.id,
                                "tooltip": f"Add Interview require a {candidate_node.name} read permission.",
                            }
                        )

        return required_permissions

    @classmethod
    def get_employee_disabled_permissions(
        cls, role_id: Optional[int] = None
    ) -> dict[int, dict[int, str]]:

        not_delete_in_nodes = set(
            [
                "Dashboard",
                "Role Permissions",
                "Staff Management",
                "Signature Managements",
            ]
        )

        add_in_nodes = set(["Job Requests", "Analytics Management"])

        update_in_nodes = set(["Job Requests", "Analytics Management"])

        # static code for check disable schedule permission
        if role_id and role_id == 4:
            update_in_nodes.add("Schedule Interview")

        # Fetch nodes that are of type 'EmployeeNode' and their names not in 'not_in_nodes'
        not_delete_nodes = Node.select(Node).where(
            Node.type == "EmployeeNode", Node.name.not_in(not_delete_in_nodes)
        )

        not_add_nodes = Node.select(Node).where(
            Node.type == "EmployeeNode", Node.name.in_(add_in_nodes)
        )

        not_update_nodes = Node.select(Node).where(
            Node.type == "EmployeeNode", Node.name.in_(update_in_nodes)
        )

        from app.models.permission import Permission

        add_permission = Permission.get(name="write")
        edit_permission = Permission.get(name="edit")
        delete_permission = Permission.get(name="delete")
        # Define the disabled_permissions dictionary
        disabled_permissions: dict[int, dict[int, str]] = {}

        # Populate the dictionary with node IDs and permission IDs
        for node in not_delete_nodes:
            if node.id not in disabled_permissions:
                disabled_permissions[node.id] = {
                    delete_permission.id: f"Delete functionality is not available for {node.name}.",
                }

        for node in not_add_nodes:
            if node.id not in disabled_permissions:
                disabled_permissions[node.id] = {
                    add_permission.id: f"Add/Create functionality is not available for {node.name}.",
                }
            else:
                # Update existing permissions if the dictionary is not empty
                disabled_permissions[node.id][
                    add_permission.id
                ] = f"Add/Create functionality is not available for {node.name}."

        for node in not_update_nodes:
            if node.id not in disabled_permissions:
                disabled_permissions[node.id] = {
                    edit_permission.id: f"Edit/Update functionality is not available for {node.name}.",
                }
            else:
                # Update existing permissions if the dictionary is not empty
                disabled_permissions[node.id][
                    edit_permission.id
                ] = f"Edit/Update functionality is not available for {node.name}."

        return disabled_permissions

    @classmethod
    def get_permissions_with_employee_nodes(
        cls, employee_role_id: int, business_id: int
    ):
        from app.models.permission import Permission
        from app.models.business_employee_role_permission import (
            BusinessEmployeeRolePermission,
        )
        from app.models.node import Node

        # Check if the employee_role_id is for superadmin
        is_superadmin = employee_role_id == 1  # Replace with actual superadmin ID

        # Create a dictionary to hold permissions for each node
        permissions_dict = {}

        if is_superadmin:
            # For superadmin, select all nodes
            nodes = Node.select().where(Node.type == "EmployeeNode")
            nodes = nodes.order_by(Node.order_num.asc())
            permission_names = []
            for permission in Permission.all():
                permission_names.append(permission.name)

            for node in nodes:
                if node.id not in permissions_dict:
                    permissions_dict[node.id] = []
                permissions_dict[node.id].append(permission_names)

        else:
            # Fetch nodes associated with the specific employee role
            nodes = (
                Node.select(Node)
                .join(
                    BusinessEmployeeRolePermission,
                    on=(BusinessEmployeeRolePermission.node_id == Node.id),
                )
                .where(
                    BusinessEmployeeRolePermission.employee_role_id == employee_role_id,
                    Node.type == "EmployeeNode",
                )
                .distinct()
            )

            # Fetch permissions and group them by node_id
            permissions_query = (
                BusinessEmployeeRolePermission.select(
                    BusinessEmployeeRolePermission.node_id, Permission.name
                )
                .join(
                    Permission,
                    on=(BusinessEmployeeRolePermission.permission_id == Permission.id),
                )
                .where(
                    BusinessEmployeeRolePermission.employee_role_id == employee_role_id,
                    BusinessEmployeeRolePermission.business_id == business_id,
                )
            )

            for bperm in permissions_query:
                if bperm.node_id not in permissions_dict:
                    permissions_dict[bperm.node_id] = []
                permissions_dict[bperm.node_id].append(bperm.permission.name)

        def info(self):
            childrens = []
            for record in self.childrens:
                node_info = info(record)
                if node_info:
                    childrens.append(node_info)

            # Attach permissions to each node
            node_permissions = permissions_dict.get(self.id, [])
            if node_permissions:
                return {
                    "name": self.name,
                    "path": self.path,
                    "unique_key": node.unique_key,
                    "singular_name": node.singular_name,
                    "description": node.description,
                    "icon": self.icon,
                    "type": self.type,
                    "childrens": childrens,
                    "permissions": node_permissions,
                }
            return None

        result = []
        nodes = nodes.order_by(Node.order_num.asc())
        for node in nodes:
            if node.parent_id is None:
                node_info = info(node)
                if node_info:
                    result.append(node_info)

        return result

    class Meta:
        table_name = "nodes"
