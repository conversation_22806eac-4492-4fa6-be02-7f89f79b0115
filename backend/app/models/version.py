from app.models.base import ActiveRecord
from peewee import (
    CharField,
    TextField,
    DateTimeField,
    BigAutoField,
    BigIntegerField,
    SQL,
)
from datetime import datetime
from app.models.concern.enum import EnumField
import json
import importlib


class Version(ActiveRecord):
    id = BigAutoField(primary_key=True, index=True)
    whodoneit = CharField()
    record = TextField(null=False)
    request_headers = TextField(null=False)
    model_name = CharField(null=False)
    model_id = BigIntegerField(null=False)
    action = Char<PERSON>ield(null=False)
    created_at = DateTimeField(constraints=[SQL("DEFAULT CURRENT_TIMESTAMP")])

    def record_instance(self):
        data = json.loads(self.record)

        # Import the module and get the model class
        module = importlib.import_module("app.models")
        model_class = getattr(module, self.model_name)

        # Create an instance of the model with the modified data
        instance = model_class()

        # Iterate through the data and set each field on the instance
        for field, value in data.items():
            if hasattr(model_class, field):
                model_field = getattr(model_class, field)

                if isinstance(model_field, EnumField):
                    # Convert string value to the appropriate enum
                    value = model_field.python_value(value)
                elif isinstance(model_field, DateTimeField) and isinstance(value, str):
                    # Convert string value to datetime (assuming a specific format)
                    value = datetime.fromisoformat(value)
                # Set the attribute on the instance
                setattr(instance, field, value)

        return instance

    def info(self):
        return {
            "id": self.id,
            "version_action": self.action,
            "version_created_at": str(self.created_at),
        }

    # class meta
    class Meta:
        table_name = "versions"
