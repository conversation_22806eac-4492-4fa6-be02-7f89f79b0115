from typing import Tuple, Dict, List, Optional
from peewee import fn, Raw<PERSON><PERSON><PERSON>, JOIN, Case
from collections import defaultdict
from datetime import date, datetime, timedelta
from dateutil.relativedelta import relativedelta
from collections import defaultdict


class CandidateHelper:
    @staticmethod
    def get_candidate_count_by_grouping(
        today: datetime, grouping_type: str, business_ids: list[int]
    ):
        # prevent circular import
        from app.models.candidate import Candidate

        # Base query
        title = f"Candidates Stats"
        yLabel = "Total Candidates"
        query = Candidate.select(fn.COUNT(Candidate.id).alias("count")).where(
            Candidate.business.in_(business_ids), Candidate.is_deleted == False
        )
        has_data = query.count() > 0

        labels, counts, title, xLabel, yLabel, has_data = (
            CandidateHelper.get_datewise_stats(
                query=query,
                model=Candidate,
                grouping_type=grouping_type,
                today=today,
                title=title,
                yLabel=yLabel,
                has_data=True,
            )
        )

        return labels, counts, title, xLabel, yLabel, has_data

    @staticmethod
    def get_candidate_status_counts(
        business_ids: list[int],
    ) -> Tuple[List[Dict[str, any]], str, List[str]]:
        # prevent circular import
        from app.models.candidate import Candidate
        from app.models.candidate_interview import CandidateInterview

        # Base query to get counts by status
        query = (
            Candidate.select(Candidate.status, fn.COUNT(Candidate.id).alias("count"))
            .where(Candidate.business.in_(business_ids), Candidate.is_deleted == False)
            .group_by(Candidate.status)
        )

        candidate_status_expr = Case(
            None,
            ((CandidateInterview.status.is_null(True), "Pending"),),
            CandidateInterview.status,
        )

        query = (
            Candidate.select(
                candidate_status_expr.alias("candidate_status"),
                fn.COUNT(fn.DISTINCT(Candidate.id)).alias("count"),
            )
            .join(
                CandidateInterview,
                JOIN.LEFT_OUTER,
                on=(CandidateInterview.candidate_id == Candidate.id),
            )
            .where(
                Candidate.business.in_(business_ids),
                Candidate.is_deleted == False,
            )
            .group_by(candidate_status_expr)  # Group by the CASE expression directly
            .order_by(candidate_status_expr)
        )

        results = list(query)
        has_data = len(results) > 0

        status_counts = {
            "Cancelled": 0,
            "Scheduled": 0,
            "Rejected": 0,
            "Finalize": 0,
            "Pending": 0,
            "Other": 0,
        }

        for result in results:
            status = (
                result.candidate_status
            )  # string like "Pending", "Shortlisted", etc.
            count = result.count
            if status == "Pending":
                status_counts["Pending"] = count
            elif status == "0":
                status_counts["Cancelled"] = count
            elif status == "1":
                status_counts["Scheduled"] = count
            elif status == "2":
                status_counts["Rejected"] = count
            elif status == "3":
                status_counts["Finalize"] = count
            elif status == "Other":
                status_counts["Other"] = count

        # Convert to the desired format
        response_data = [
            {"id": i, "value": status_counts[label], "label": label}
            for i, label in enumerate(
                ["Cancelled", "Scheduled", "Rejected", "Finalize", "Pending", "Other"]
            )
        ]

        if len(results) == 0:
            response_data = [{"id": 0, "value": 1, "label": "No Data"}]

        title = "Candidate Status Distribution"

        colors = [
            "#d32f2f",
            "#2ed1a5",
            "#f57c00",
            "#02263b",
            "#ffca28",
            "#9e9e9e",
        ]
        return (
            response_data,
            title,
            has_data,
            colors,
        )

    @staticmethod
    def generate_report(
        business_id: int,
        candidate_joined_from: Optional[str],
        candidate_joined_till: Optional[str],
        page: Optional[int] = None,
        limit: Optional[int] = None,
    ) -> Tuple[List[Dict[str, any]], str]:
        # prevent circular import
        from app.models.base import db
        from app.models.concern.enum import CandidateInterviewStatus

        base_sql_query = """
            SELECT
                c.name AS candidate_name,
                c.email AS candidate_email,
                c.created_at AS candidate_created_at,
                i.status AS interview_status,
                o.title AS opportunity_title,
                o.id AS opportunity_id
            FROM
                candidate_interviews AS i
            JOIN
                candidates AS c
            ON
                i.candidate_id = c.id
            JOIN
                opportunities AS o
            ON
                i.opportunity_id = o.id
            WHERE
                c.business_id = %s
                AND c.is_deleted = 0 
                AND (c.created_at BETWEEN %s AND %s)
        """

        current_date = (datetime.now().date() + timedelta(days=1)).strftime("%Y-%m-%d")

        if candidate_joined_from:
            if candidate_joined_till:
                start_date = candidate_joined_from
                end_date = candidate_joined_till
            else:
                start_date = candidate_joined_from
                end_date = current_date
        else:
            start_date = "2000-01-01"
            end_date = current_date

        count_sql_query = f"select count(*) from ({base_sql_query}) as total_records"
        cursor = db.execute_sql(count_sql_query, (business_id, start_date, end_date))

        # Fetch the count value
        total_records = cursor.fetchone()[0]

        raw_sql_query = f"{base_sql_query} LIMIT %s OFFSET %s;"

        offset = 0
        if page and limit:
            offset = (page - 1) * limit
        else:
            limit = total_records

        query = RawQuery(
            raw_sql_query, (business_id, start_date, end_date, limit, offset)
        )

        data = []
        total_records = 0
        with db.atomic():  # Ensure database transactions are properly managed
            for row in query.execute(database=db):
                total_records += 1
                dict_data = {}
                dict_data["candidate_name"] = row["candidate_name"]
                dict_data["candidate_email"] = row["candidate_email"]
                dict_data["candidate_created_at"] = (
                    row["candidate_created_at"].date().strftime("%Y-%m-%d")
                )
                dict_data["interview_status"] = CandidateInterviewStatus(
                    row["interview_status"]
                ).name
                dict_data["opportunity_title"] = row["opportunity_title"]
                dict_data["opportunity_id"] = row["opportunity_id"]
                data.append(dict_data)

        return {"count": total_records, "rows": data}

    @staticmethod
    def get_shortlist_candidate_by_group(
        today: datetime, grouping_type: str, business_ids: list[int]
    ):
        from app.models.candidate_interview import CandidateInterview

        title = f"Shortlisted Candidates Stats"
        yLabel = "Total Shortlisted Candidates"

        # Base_query
        query = CandidateInterview.select(
            fn.COUNT(CandidateInterview.id).alias("count")
        ).where(
            CandidateInterview.business.in_(business_ids),
            CandidateInterview.status == 3,
        )
        has_data = query.count() > 0

        labels, counts, title, xLabel, yLabel, has_data = (
            CandidateHelper.get_datewise_stats(
                query=query,
                model=CandidateInterview,
                grouping_type=grouping_type,
                today=today,
                title=title,
                yLabel=yLabel,
                has_data=True,
            )
        )

        return labels, counts, title, xLabel, yLabel, has_data

    @staticmethod
    def get_datewise_stats(query, model, grouping_type, today, title, yLabel, has_data):
        """
        Returns labels, counts, title, xLabel, yLabel, and has_data based on grouping_type.

        Args:
            query: base peewee query object (e.g. model.select())
            model: peewee model class (e.g. Candidate, CandidateInterview)
            grouping_type: one of ['daily', 'weekly', 'monthly', 'yearly']
            today: date or datetime object representing "now"
            title: string title for chart
            yLabel: string y-axis label
            has_data: bool indicating if there is any data

        Returns:
            tuple: (labels_list, counts_dict, title, xLabel, yLabel, has_data)
        """

        counts = defaultdict(lambda: 0)
        labels = []

        if grouping_type == "daily":
            days_back = 29
            start_date = today - timedelta(days=days_back)

            query = (
                query.select(
                    fn.DATE(model.created_at).alias("date"),
                    fn.COUNT(model.id).alias("count"),
                )
                .where(model.created_at >= start_date)
                .group_by(fn.DATE(model.created_at))
                .order_by(fn.DATE(model.created_at))
            )

            LABEL_FMT = "%b %d, %Y"
            for i in reversed(range(days_back + 1)):
                d = today - timedelta(days=i)
                labels.append(d.strftime(LABEL_FMT))

            results = list(query)
            for r in results:
                key = datetime.strptime(r.date, "%Y-%m-%d").strftime(LABEL_FMT)
                counts[key] = r.count

            xLabel = f"Daily ({labels[0]} - {labels[-1]})"

        elif grouping_type == "weekly":
            weeks_back = 6
            start_date = today - timedelta(weeks=weeks_back)

            query = (
                query.select(
                    fn.YEARWEEK(model.created_at, 1).alias("week"),
                    fn.COUNT(model.id).alias("count"),
                )
                .where(model.created_at >= start_date)
                .group_by(fn.YEARWEEK(model.created_at, 1))
                .order_by(fn.YEARWEEK(model.created_at, 1))
            )

            for i in reversed(range(weeks_back)):
                week_start = today - timedelta(weeks=i)
                week_start_date = week_start - timedelta(
                    days=week_start.weekday()
                )  # Monday
                week_end_date = week_start_date + timedelta(days=6)  # Sunday
                label = f"{week_start_date.strftime('%b %d')} - {week_end_date.strftime('%b %d, %Y')}"
                labels.append(label)

            results = list(query)
            for r in results:
                year = r.week // 100
                week_num = r.week % 100
                try:
                    week_start_date = date.fromisocalendar(year, week_num, 1)  # Monday
                    week_end_date = week_start_date + timedelta(days=6)
                    key = f"{week_start_date.strftime('%b %d')} - {week_end_date.strftime('%b %d, %Y')}"
                    counts[key] = r.count
                except ValueError:
                    continue

            first_week_start = today - timedelta(weeks=weeks_back - 1)
            first_week_date = first_week_start - timedelta(
                days=first_week_start.weekday()
            )
            last_week_date = today - timedelta(days=today.weekday()) + timedelta(days=6)
            xLabel = f"Week ({first_week_date.strftime('%d').lstrip('0')}th {first_week_date.strftime('%b')} to {last_week_date.strftime('%d').lstrip('0')}th {last_week_date.strftime('%b')})"

        elif grouping_type == "monthly":
            months_back = 12
            start_date = today.replace(day=1) - relativedelta(months=months_back)

            query = (
                query.select(
                    fn.DATE_FORMAT(model.created_at, "%Y-%m").alias("month"),
                    fn.COUNT(model.id).alias("count"),
                )
                .where(model.created_at >= start_date)
                .group_by(fn.DATE_FORMAT(model.created_at, "%Y-%m"))
                .order_by(fn.DATE_FORMAT(model.created_at, "%Y-%m"))
            )

            for i in reversed(range(months_back)):
                m = today.replace(day=1) - relativedelta(months=i)
                labels.append(m.strftime("%b %Y"))

            results = list(query)
            for r in results:
                key = datetime.strptime(r.month, "%Y-%m").strftime("%b %Y")
                counts[key] = r.count

            xLabel = f"Month ({labels[0]} - {labels[-1]})"

        elif grouping_type == "yearly":
            years_back = 5
            start_year = today.year - (years_back - 1)

            query = (
                query.select(
                    fn.YEAR(model.created_at).alias("year"),
                    fn.COUNT(model.id).alias("count"),
                )
                .where(fn.YEAR(model.created_at) >= start_year)
                .group_by(fn.YEAR(model.created_at))
                .order_by(fn.YEAR(model.created_at))
            )

            for i in reversed(range(years_back)):
                labels.append(str(today.year - i))

            results = list(query)
            for r in results:
                counts[str(r.year)] = r.count

            xLabel = f"Year ({labels[0]} - {labels[-1]})"

        else:
            raise ValueError(f"Invalid grouping_type: {grouping_type}")

        return labels, counts, title, xLabel, yLabel, has_data
