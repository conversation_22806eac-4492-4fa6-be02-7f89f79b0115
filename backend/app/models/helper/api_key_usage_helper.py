from typing import <PERSON><PERSON>, Dict, List
from peewee import fn


class ApiKeyUsageHelper:
    @staticmethod
    def get_usage_stats(
        business_id: int,
    ) -> Tuple[List[Dict[str, any]], str, List[str]]:
        # prevent circular import
        from app.models.api_key_usage import ApiKeyUsage

        status_counts = {"Completed": 0, "Failed": 0, "Other": 0}

        # Base query to get counts by status
        query = (
            ApiKeyUsage.select(
                ApiKeyUsage.response_status, fn.COUNT(ApiKeyUsage.id).alias("count")
            )
            .where(ApiKeyUsage.business == business_id)
            .group_by(ApiKeyUsage.response_status)
        )

        results = list(query)

        # Populate status_counts with results
        for result in results:
            count = result.count
            if result.response_status == "Completed":
                status_counts["Completed"] = count
            elif result.response_status == "Failed":
                status_counts["Failed"] = count
            else:
                status_counts["Other"] = count

        # Convert to the desired format
        response_data = [
            {"id": i, "value": status_counts[label], "label": label}
            for i, label in enumerate(["Completed", "Failed", "Other"])
        ]

        if len(results) == 0:
            response_data = [{"id": 0, "value": 1, "label": "No Data"}]

        title = "API usage Stats"
        colors = [
            "#2ed1a5",
            "#d32f2f",
            "#9e9e9e",
        ]
        return response_data, title, colors
