from app.models.base import ActiveRecord
from peewee import (
    <PERSON><PERSON><PERSON><PERSON>,
    ForeignKeyField,
    DateTimeField,
    BigIntegerField,
    SQL,
)
from datetime import datetime, timedelta
from pytz import UTC
from app.models.mixins import VersioningMixin
import random


class EsignVerificationRequest(ActiveRecord, VersioningMixin):
    # to prevent circular import
    from app.models.employee import Employee
    from app.models.candidate import Candidate

    id = BigInteger<PERSON>ield(primary_key=True)
    employee_id = BigIntegerField(null=False)
    candidate_id = BigIntegerField(null=False)
    reason = CharField(max_length=255, null=True)
    otp = CharField(max_length=10, null=False)
    otp_expire_at = DateTimeField(null=True)

    created_at = DateTimeField(constraints=[SQL("DEFAULT CURRENT_TIMESTAMP")])
    updated_at = DateTimeField(constraints=[SQL("DEFAULT CURRENT_TIMESTAMP")])

    # associations
    employee = ForeignKeyField(
        Employee, backref="esign_verification_requests", lazy_load=True
    )
    candidate = ForeignKeyField(
        Candidate, backref="esign_verification_requests", lazy_load=True
    )

    def generate_otp(self):
        # Generate a 6-digit OTP
        self.otp = "".join(random.choices("0123456789", k=6))
        self.otp_expire_at = datetime.now(UTC) + timedelta(minutes=15)
        self.save()
        return self

    class Meta:
        table_name = "esign_verification_requests"
