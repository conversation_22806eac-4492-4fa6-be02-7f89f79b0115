from app.models.base import ActiveRecord
from app.models.concern.enum import <PERSON><PERSON><PERSON><PERSON>, QuestionType
from peewee import (
    BigIntegerField,
    TextField,
    DateTimeField,
    ForeignKeyField,
    SQL,
    BooleanField,
)


class CandidateScreeningAnswer(ActiveRecord):
    # to prevent circular import
    from app.models.business import Business
    from app.models.candidate import Candidate
    from app.models.opportunity import Opportunity
    from app.models.screening_interview_question import ScreeningInterviewQuestion
    from app.models.schedule_interview import ScheduleInterview

    id = BigIntegerField(primary_key=True, index=True)
    answer = TextField(null=False)
    question_type = EnumField(QuestionType)

    opportunity_id = BigIntegerField(null=False)
    business_id = BigIntegerField(null=False)
    screening_interview_question_id = BigIntegerField(null=False)
    candidate_id = BigIntegerField(null=False)
    schedule_interview_id = BigIntegerField(null=False)
    is_correct = BooleanField(default=False)
    answer_explanation = TextField()

    created_at = DateTimeField(constraints=[SQL("DEFAULT CURRENT_TIMESTAMP")])
    updated_at = DateTimeField(constraints=[SQL("DEFAULT CURRENT_TIMESTAMP")])

    # associations
    candidate = ForeignKeyField(
        Candidate, null=True, backref="candidate_screening_answers", lazy_load=True
    )
    business = ForeignKeyField(
        Business, null=True, backref="candidate_screening_answers", lazy_load=True
    )
    screening_interview_question = ForeignKeyField(
        ScreeningInterviewQuestion,
        null=True,
        backref="candidate_screening_answers",
        lazy_load=True,
    )
    opportunity = ForeignKeyField(
        Opportunity, null=True, backref="candidate_screening_answers", lazy_load=True
    )
    schedule_interview = ForeignKeyField(
        ScheduleInterview,
        null=True,
        backref="candidate_screening_answers",
        lazy_load=True,
    )

    def info(self):
        return {
            "id": self.id,
            "question": self.screening_interview_question.question,
            "actual_answer": self.screening_interview_question.answer,
            "answer": self.answer,
            "is_correct": self.is_correct,
            "answer_explanation": self.answer_explanation,
            "question_type": self.question_type,
            "options": self.screening_interview_question.question_option_list,
        }

    class Meta:
        table_name = "candidate_screening_answers"
        indexes = (
            (
                "candidate_id",
                "business_id",
                "screening_interview_question_id",
                "opportunity_id",
            ),
            True,
        )
