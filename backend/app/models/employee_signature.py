from app.models.base import ActiveRecord
from peewee import (
    ForeignKeyField,
    DateTimeField,
    BigIntegerField,
    TextField,
    SmallIntegerField,
    SQL,
)
from app.models.mixins import VersioningMixin, SoftDeletedMixin


class EmployeeSignature(ActiveRecord, SoftDeletedMixin, VersioningMixin):
    # to prevent circular import
    from app.models.employee import Employee

    id = BigIntegerField(primary_key=True)
    employee_id = BigIntegerField(null=False)
    base64_sign = TextField(null=True)  # Store base64 encoded signature blob
    signature = TextField(null=True)  # Store image url
    status = SmallIntegerField(constraints=[SQL("DEFAULT 1")])
    created_at = DateTimeField(constraints=[SQL("DEFAULT CURRENT_TIMESTAMP")])
    updated_at = DateTimeField(constraints=[SQL("DEFAULT CURRENT_TIMESTAMP")])

    # associations
    employee = ForeignKeyField(
        Employee, backref="employee_signatures", on_delete="CASCADE", lazy_load=True
    )

    @property
    def signature_url(self):
        return self.get_url(self.signature)

    def info(self):
        return {
            "employee_id": self.employee_id,
            "signature_url": self.signature_url,
            "status": self.status,
            "employee_name": (
                (self.get_instance("employee") and self.employee.full_name())
                or "Recruitease Pro - employee (Deleted)"
            ),
            "employee_email": (
                self.get_instance("employee")
                and self.employee.email
                or "<EMAIL>"
            ),
            "created_at": str(f"{self.created_at} utc"),
            "updated_at": str(f"{self.updated_at} utc"),
        }

    class Meta:
        table_name = "employee_signatures"
        indexes = (("employee_id",), False)
