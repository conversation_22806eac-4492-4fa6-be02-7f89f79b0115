from app.models.base import ActiveRecord
from peewee import (
    TextField,
    DateTimeField,
    BigAutoField,
    CharField,
    BigIntegerField,
    SQL,
)


class EmailTrack(ActiveRecord):
    id = BigAutoField(primary_key=True, index=True)
    body = TextField(null=False)
    subject = TextField(null=False)
    to_email = TextField(null=False)
    cc_email = TextField(null=True)
    bcc_email = TextField(null=True)
    model_name = CharField(null=True)
    model_id = BigIntegerField(null=True)
    created_at = DateTimeField(constraints=[SQL("DEFAULT CURRENT_TIMESTAMP")])

    # class meta
    class Meta:
        table_name = "email_tracks"
