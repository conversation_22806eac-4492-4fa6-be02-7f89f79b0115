from fastapi import <PERSON><PERSON><PERSON>, Request
from fastapi.staticfiles import StaticFiles
from fastapi_pagination import add_pagination
from fastapi.middleware.gzip import GZipMiddleware
from app.api import api_router
from app.exceptions import (
    custom_http_exception_handler,
    custom_record_not_found_exception_handler,
    custom_validation_exception_handler,
    custom_integrity_exception_handler,
    unauthorized_exception_handler,
    method_not_allowed_exception_handler,
    RecordNotFoundException,
    CustomValidationError,
    UnauthorizedException,
    MethodNotAllowedException,
    HTTPException,
    IntegrityError,
)
from fastapi.middleware.cors import CORSMiddleware
from app.config import ALLOWED_ORIGINS, logger
from app.utils import create_allow_origin_regex, generate_random_string
from database.connection import db
from app.docs_router import docs_router

app = FastAPI(title="Recruitease Pro", docs_url=None, redoc_url=None)

app.add_middleware(
    CORSMiddleware,
    allow_origin_regex=create_allow_origin_regex(ALLOWED_ORIGINS),
    allow_credentials=True,
    allow_methods=[
        "GET",
        "POST",
        "PUT",
        "PATCH",
        "DELETE",
        "OPTIONS",
    ],  # Allow specific methods
    allow_headers=[
        "Content-Type",
        "Authorization",
        "X-Requested-With",
        "X-Forwarded-For",
    ],
)


#  --------------------- Start for Custom Exception Handler ---------------------

app.add_exception_handler(HTTPException, custom_http_exception_handler)
app.add_exception_handler(
    RecordNotFoundException, custom_record_not_found_exception_handler
)
app.add_exception_handler(CustomValidationError, custom_validation_exception_handler)
app.add_exception_handler(IntegrityError, custom_integrity_exception_handler)
app.add_exception_handler(UnauthorizedException, unauthorized_exception_handler)
app.add_exception_handler(
    MethodNotAllowedException, method_not_allowed_exception_handler
)

#  --------------------- End for Custom Exception Handler ---------------------


@app.middleware("http")
async def set_request_subdomain(request: Request, call_next):
    # Extract the subdomain from the Host header
    with db.connection_context():
        subdomain = None
        origin = request.headers.get("Origin")
        request.request_id = generate_random_string(20)
        if origin:
            subdomain = origin.split("//")[-1].split(".")[0] if "." in origin else None
        request.state.subdomain = subdomain
        request.client_ip = request.headers.get("X-Forwarded-For", None)
        # Continue processing the request
        logger.info(
            f"{request.client_ip} - {request.method} {request.url.path}",
            extra={"request_id": request.request_id},
        )
        # Log parameters
        response = await call_next(request)
        status_code_message = (
            "successfully" if response.status_code < 400 else "with failure"
        )
        logger.info(
            f"Completed {status_code_message} (status code: {response.status_code})",
            extra={"request_id": request.request_id},
        )
        return response


app.add_middleware(GZipMiddleware, minimum_size=1000)

# Register the docs router
app.include_router(docs_router)

# api's
app.include_router(api_router, prefix="/api/v1")

# static or public files
app.mount("/uploads", StaticFiles(directory="uploads"), name="uploads")
app.mount("/", StaticFiles(directory="public"), name="public")


add_pagination(app)
