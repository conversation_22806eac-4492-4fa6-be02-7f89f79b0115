pipeline {
    agent any

    environment {
        DOCKER_CREDENTIALS_ID = 'tlgt-git' // <PERSON> Docker credentials ID
        IMAGE_NAME = 'ghcr.io/talentelgia-technologies-pvt-ltd/ats-frontend'
        K8S_NAMESPACE = 'ats' // Namespace in Kubernetes
        SONARQUBE_SERVER = 'sonarqube-server'
        SONARSCANNER = 'sonarqube-scanner'
        STRIPE_PUBLISHABLE_KEY = credentials('NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY')
        ATS_APP_URL = credentials("ATS_APP_URL")
        ATS_API_URL = credentials("ATS_API_URL")
        ATS_BASE_DOMAIN = credentials("ATS_BASE_DOMAIN")
        ATS_PUBLIC_KEY = credentials("ATS_PUBLIC_KEY")
        ATS_ALLOW_IMAGES_DOMAIN = credentials("ATS_ALLOW_IMAGES_DOMAIN")
        ATS_AUTH_BACKEND = credentials("ATS_AUTH_BACKEND")
        ATS_EXAM_TERTMINATION_MAX_WARNINGS = credentials("ATS_EXAM_TERTMINATION_MAX_WARNINGS")
        ATS_DROPBOX_APP_KEY = credentials("ATS_DROPBOX_APP_KEY")
        ATS_GOOGLE_API_KEY = credentials("ATS_GOOGLE_API_KEY")
        ATS_TINY_MCE_API_KEY = credentials("ATS_TINY_MCE_API_KEY")
    }

    stages {
        stage('Checkout Code') {
            steps {
                // Checkout the code from your Git repository
                git url: 'https://github.com/Talentelgia-Technologies-Pvt-Ltd/ATS.git', credentialsId: 'tlgt-git', branch: 'master'
            }
        }

        stage('SonarQube Analysis') {
            environment {
                scannerHome = tool name: "${SONARSCANNER}"
            }
            steps {
                script {
                    def executable = "${scannerHome}/bin/sonar-scanner"
                    
                    withCredentials([string(credentialsId: 'jenkins-sonar-token', variable: 'SONAR_TOKEN')]) {
                        withSonarQubeEnv("${SONARQUBE_SERVER}") {
                            sh """
                                #!/bin/bash
                                ${executable} \\
                                -Dsonar.projectKey=ats-frontend \\
                                -Dsonar.projectName=ATS_Frontend \\
                                -Dsonar.projectVersion=1.0 \\
                                -Dsonar.sources=app \\
                                -Dsonar.exclusions='**/node_modules/**/*,**/build/**/*' \\
                                -Dsonar.sourceEncoding=UTF-8 \\
                                -Dsonar.host.url=https://sonar.teamtalentelgia.com/ \\
                                -Dsonar.login=${SONAR_TOKEN} \\
                                -Dsonar.language=js \\
                                -Dsonar.exclusions=**/*.jsx,**/*.tsx \\
                                -Dsonar.javascript.lcov.reportPaths=app/coverage/lcov-report/index.html
                            """
                        }
                    }
                }
            }
        }

        stage('Quality Gate') {
            steps {
                script {
                    timeout(time: 1, unit: 'HOURS') {
                        def qualityGate = waitForQualityGate()
                        if (qualityGate.status != 'OK') {
                            error "Pipeline aborted due to quality gate failure: ${qualityGate.status}"
                        }
                    }
                }
            }
        }

        stage('Build Image') {
            steps {
                script {
                    try {

                        // Add the context '.' to indicate the current directory
                        dockerImage = docker.build IMAGE_NAME, "--build-arg NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=${STRIPE_PUBLISHABLE_KEY} --build-arg NEXT_PUBLIC_AUTH_BACKEND=${ATS_AUTH_BACKEND} --build-arg NEXT_PUBLIC_EXAM_TERTMINATION_MAX_WARNINGS=${ATS_EXAM_TERTMINATION_MAX_WARNINGS} --build-arg NEXT_PUBLIC_DROPBOX_APP_KEY=${ATS_DROPBOX_APP_KEY} --build-arg NEXT_PUBLIC_GOOGLE_API_KEY=${ATS_GOOGLE_API_KEY} --build-arg NEXT_PUBLIC_TINY_MCE_API_KEY=${ATS_TINY_MCE_API_KEY} app"
                    } catch (Exception e) {
                        error "Docker build failed: ${e.message}"
                    }
                }
            }
        }

        stage('Push Image') {
            steps {
                script {
                    // Push the Docker image with the build number tag
                    docker.withRegistry('https://ghcr.io', DOCKER_CREDENTIALS_ID) {
                        dockerImage.push("${env.BUILD_NUMBER}")
                    }
                }
            }
        }

        stage('Trigger ManifestUpdate') {
            steps {
                echo "triggering updatemanifestjob"
                build job: 'ATS-Manifest-Frontend', parameters: [string(name: 'DOCKERTAG', value: env.BUILD_NUMBER)]
            }
        }
    }

    // Optionally, you can add post actions here if needed
}
