# Use Node.js 20 <PERSON> as the base image
FROM node:21.7.3-alpine

# Set the working directory
WORKDIR /app

# Install pnpm
RUN npm install -g pnpm@9.0.6

# Set environment variables
ARG NEXT_PUBLIC_APP_URL=https://recruiteasepro.com
ARG NEXT_PUBLIC_API_URL=https://api.recruiteasepro.com
ARG NEXT_PUBLIC_BASE_DOMAIN=recruiteasepro.com
ARG NEXT_PUBLIC_KEY=E5FBPJne7udyY8GgbV83vPwqe
ARG NEXT_PUBLIC_ALLOW_IMAGES_DOMAIN=api.recruiteasepro.com

# -------------------------------------- build args --------------------------------------
ARG NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY
ARG NEXT_PUBLIC_TINY_MCE_API_KEY
ARG NEXT_PUBLIC_AUTH_BACKEND
ARG NEXT_PUBLIC_EXAM_TERTMINATION_MAX_WARNINGS
ARG NEXT_PUBLIC_DROPBOX_APP_KEY
ARG NEXT_PUBLIC_GOOGLE_API_KEY

# ----------------- Set environments variables using the build arguments -----------------
ENV NEXT_PUBLIC_APP_URL=${NEXT_PUBLIC_APP_URL}
ENV NEXT_PUBLIC_API_URL=${NEXT_PUBLIC_API_URL}
ENV NEXT_PUBLIC_BASE_DOMAIN=${NEXT_PUBLIC_BASE_DOMAIN}
ENV NEXT_PUBLIC_KEY=${NEXT_PUBLIC_KEY}
ENV NEXT_PUBLIC_ALLOW_IMAGES_DOMAIN=${NEXT_PUBLIC_ALLOW_IMAGES_DOMAIN}

# --------------------------------- build arguments envs ---------------------------------
ENV NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=${NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY}
ENV NEXT_PUBLIC_TINY_MCE_API_KEY=${NEXT_PUBLIC_TINY_MCE_API_KEY}
ENV NEXT_PUBLIC_AUTH_BACKEND=${NEXT_PUBLIC_AUTH_BACKEND}
ENV NEXT_PUBLIC_EXAM_TERTMINATION_MAX_WARNINGS=${NEXT_PUBLIC_EXAM_TERTMINATION_MAX_WARNINGS}
ENV NEXT_PUBLIC_DROPBOX_APP_KEY=${NEXT_PUBLIC_DROPBOX_APP_KEY}
ENV NEXT_PUBLIC_GOOGLE_API_KEY=${NEXT_PUBLIC_GOOGLE_API_KEY}

# Copy package.json and pnpm-lock.yaml files
COPY package.json ./
#COPY pnpm-lock.yaml ./

# Install dependencies using pnpm
RUN pnpm install

# Copy the rest of the application code
COPY . .

# Build the Next.js application
RUN pnpm build

# Expose the port the app runs on
EXPOSE 3000

# Start the Next.js application
CMD ["pnpm", "start"]

