{"name": "@ats/app", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@emotion/react": "^11.11.4", "@emotion/styled": "^11.11.5", "@ffmpeg/core": "^0.8.3", "@ffmpeg/ffmpeg": "^0.9.4", "@monaco-editor/react": "^4.7.0", "@mui/icons-material": "^5.15.17", "@mui/material": "^5.15.17", "@mui/x-charts": "^7.16.0", "@popperjs/core": "^2.11.8", "@reduxjs/toolkit": "^2.2.4", "@stripe/react-stripe-js": "^2.8.1", "@stripe/stripe-js": "^4.7.0", "@tensorflow-models/blazeface": "^0.1.0", "@tensorflow/tfjs": "^4.22.0", "@tinymce/tinymce-react": "6.1.0", "antd": "^5.17.1", "axios": "^1.6.8", "crypto-js": "^4.2.0", "dot-object": "^2.1.5", "moment": "^2.30.1", "next": "14.2.3", "next-redux-wrapper": "^8.1.0", "react": "^18", "react-big-calendar": "^1.19.2", "react-bootstrap": "^2.10.2", "react-copy-to-clipboard": "^5.1.0", "react-datepicker": "^6.9.0", "react-dom": "^18", "react-phone-number-input": "^3.4.9", "react-quill": "^2.0.0", "react-redux": "^9.1.2", "react-signature-canvas": "1.1.0-alpha.2", "react-slick": "^0.30.3", "react-speech-recognition": "^3.10.0", "react-webcam": "^7.2.0", "redux": "^5.0.1", "redux-persist": "^6.0.0", "redux-persist-transform-encrypt": "^5.1.1", "sharp": "^0.33.4", "slick-carousel": "^1.8.1", "validator": "^13.12.0"}, "devDependencies": {"@types/crypto-js": "^4.2.2", "@types/dot-object": "^2.1.6", "@types/node": "^20", "@types/react": "^18", "@types/react-big-calendar": "^1.16.2", "@types/react-copy-to-clipboard": "^5.0.7", "@types/react-datepicker": "^6.2.0", "@types/react-dom": "^18", "@types/react-slick": "^0.23.13", "@types/react-speech-recognition": "^3.9.5", "@types/validator": "^13.12.2", "autoprefixer": "^10.4.19", "eslint": "^8", "eslint-config-next": "14.2.3", "postcss": "^8", "tailwindcss": "^3.4.1", "typescript": "^5"}}