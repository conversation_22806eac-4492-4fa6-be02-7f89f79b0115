import { CANDIDATE_APIS } from "../ApiConstant";
import { API } from "../api";

const { INTERVIEWS, CODING_ROUND } = CANDIDATE_APIS;

const ReturnSuccess = (data: any) => data;

const ReturnError = (error: any) => {
  return {
    success: false,
    message: error.message,
    errors: error.errors ?? [],
  };
};

// Function to fetch wildcard dashboard details from the API
const candidateInterviewsList = (params?: any) => {
  return API.get(`${INTERVIEWS}/list`, params)
    .then((response) => response.data)
    .then(ReturnSuccess)
    .catch(ReturnError);
};

const candidateJobInterviews = (job_id: number, params?: any) => {
  return API.get(`${INTERVIEWS}/jobs/${job_id}`, params)
    .then((response) => response.data)
    .then(ReturnSuccess)
    .catch(ReturnError);
};

const candidateInterviewsDetail = (interview_id: number, params?: any) => {
  return API.get(`${INTERVIEWS}/${interview_id}/detail`, params)
    .then((response) => response.data)
    .then(ReturnSuccess)
    .catch(ReturnError);
};

const candidateJobInterviewQuestions = (job_id: number, params?: any) => {
  return API.get(`${INTERVIEWS}/${job_id}/questions`, params)
    .then((response) => response.data)
    .then(ReturnSuccess)
    .catch(ReturnError);
};

const saveInterviewAnswers = (job_id: number, body: any) => {
  return API.post(`${INTERVIEWS}/${job_id}/answers`, body)
    .then((response) => response.data)
    .then(ReturnSuccess)
    .catch(ReturnError);
};

// Function to save a feedback
const saveFeedback = (interview_id: number, data: any) => {
  return API.post(`${INTERVIEWS}/${interview_id}/feedback`, data)
    .then((response) => response.data)
    .then(ReturnSuccess)
    .catch(ReturnError);
};

// Function to convert audio to text
const convertAudioToText = (
  interview_id: number,
  data: any,
  onUploadProgress: (progress: any) => void,
) => {
  return API.post(
    `${INTERVIEWS}/${interview_id}/convert-audio`,
    data,
    onUploadProgress,
  )
    .then((response) => response.data)
    .then(ReturnSuccess)
    .catch(ReturnError);
};

// Function to convert audio to text
const faceDetection = (data: any) => {
  return API.post(`${INTERVIEWS}/detect/face`, data)
    .then((response) => response.data)
    .then(ReturnSuccess)
    .catch(ReturnError);
};

// Function to verify individual coding interview answer
const runTestCases = (questionId: number, body: any) => {
  return API.post(`${CODING_ROUND}/${questionId}/run-code`, body)
    .then((response) => response.data)
    .then(ReturnSuccess)
    .catch(ReturnError);
};

const saveInterviewWarnings = (interview_id: number, body: FormData) => {
  return API.post(`${INTERVIEWS}/${interview_id}/warnings`, body)
    .then((response) => response.data)
    .then(ReturnSuccess)
    .catch(ReturnError);
};

const getInterviewWarning = (job_id: number, params?: any) => {
  return API.get(`${INTERVIEWS}/${job_id}/warnings`, params)
    .then((response) => response.data)
    .then(ReturnSuccess)
    .catch(ReturnError);
};

// eslint-disable-next-line import/no-anonymous-default-export
export default {
  candidateInterviewsList,
  candidateJobInterviews,
  candidateJobInterviewQuestions,
  saveInterviewAnswers,
  candidateInterviewsDetail,
  saveFeedback,
  convertAudioToText,
  faceDetection,
  runTestCases,
  saveInterviewWarnings,
  getInterviewWarning,
};
