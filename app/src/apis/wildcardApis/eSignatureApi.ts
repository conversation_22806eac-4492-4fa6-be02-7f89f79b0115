import { WILDCARD_APIS } from "../ApiConstant";
import { API } from "../api";
import { JsonBody } from "../api/requestBuilder";

const { EMPLOYEE_SIGNATURES } = WILDCARD_APIS;

const ReturnSuccess = (data: any) => data;

const ReturnError = (error: any) => {
  return {
    success: false,
    message: error.message,
    errors: error.errors ?? [],
  };
};

// Employee Signature API functions
const getEmployeeSignatures = (params: JsonBody) => {
  return API.get(EMPLOYEE_SIGNATURES, params)
    .then((response) => response.data)
    .then(ReturnSuccess)
    .catch(ReturnError);
};

const getEmployeeSignaturesDetails = (id: number) => {
  return API.get(`${EMPLOYEE_SIGNATURES}/${id}`)
    .then((response) => response.data)
    .then(ReturnSuccess)
    .catch(ReturnError);
};

const createEmployeeSignature = (data: FormData) => {
  return API.post(EMPLOYEE_SIGNATURES, data)
    .then((response) => response.data)
    .then(ReturnSuccess)
    .catch(ReturnError);
};

const updateEmployeeSignatureStatus = (id: number, data: JsonBody) => {
  return API.patch(`${EMPLOYEE_SIGNATURES}/${id}/status`, data)
    .then((response) => response.data)
    .then(ReturnSuccess)
    .catch(ReturnError);
};

const getEmployeeSignatureOption = (params: JsonBody) => {
  return API.get(`${EMPLOYEE_SIGNATURES}/option/all-active-signatures`, params)
    .then((response) => response.data)
    .then(ReturnSuccess)
    .catch(ReturnError);
};

// eslint-disable-next-line import/no-anonymous-default-export
export default {
  getEmployeeSignatures,
  createEmployeeSignature,
  updateEmployeeSignatureStatus,
  getEmployeeSignatureOption,
  getEmployeeSignaturesDetails,
};
