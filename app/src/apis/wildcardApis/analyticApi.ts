import { WILDCARD_APIS } from "../ApiConstant";
import { API } from "../api";

const { ANALYTICS } = WILDCARD_APIS;

const ReturnSuccess = (data: any) => data;

const ReturnError = (error: any) => {
  return {
    success: false,
    message: error.message,
    errors: error.errors ?? [],
  };
};

// Function to fetch hiring management dashoard stats from the API
const hiringManagementDashboardStats = () => {
  return API.get(ANALYTICS)
    .then((response) => response.data)
    .then(ReturnSuccess)
    .catch(ReturnError);
};

//Function to fetch hiring management Line chart stats from the API
const candidateAnalyticStats = (params: any) => {
  return API.get(`${ANALYTICS}/candidate-analytic-stats`, params)
    .then((response) => response.data)
    .then(ReturnSuccess)
    .catch(ReturnError);
};

const statusDistibutionStats = () => {
  return API.get(`${ANALYTICS}/candidate-status-distribution-stats`)
    .then((response) => response.data)
    .then(ReturnSuccess)
    .catch(ReturnError);
};

const exportAnalyticsCsv = (data: any) => {
  return API.getBlob(`${ANALYTICS}/export-stats`, data)
    .then((response) => response)
    .catch(ReturnError);
};

// eslint-disable-next-line import/no-anonymous-default-export
export default {
  hiringManagementDashboardStats,
  candidateAnalyticStats,
  statusDistibutionStats,
  exportAnalyticsCsv,
};
