import employeeAuthenticate<PERSON>pi from "./authenticateApi";
import resumeExtractionApi from "./resumeExtractionApi";
import departmentApi from "./departmentApi";
import employeeManagementApi from "./employeeManagementApi";
import opportunityApi from "./opportunityApi";
import candidateApi from "./candidateApi";
import dashboardApi from "./dashboardApi";
import interviewApi from "./interviewApi";
import locationApi from "./locationApi";
import healthCheckApi from "./healthCheckApi";
import emailTemplateApi from "./emailTemplateApi";
import jobsApi from "./jobsApi";
import jobRequestApi from "./jobRequestApi";
import candidateInterviewApi from "./candidateInterviewApi";
import wildcardBusinessesApi from "./businessesApi";
import wildCardPaymentApi from "./paymentApi";
import analyticApi from "./analyticApi";
import eSignatureApi from "./eSignatureApi";

export {
  employeeAuthenticateApi,
  resumeExtractionApi,
  candidateApi,
  departmentApi,
  employeeManagement<PERSON>pi,
  opportunityApi,
  dashboard<PERSON>pi,
  interviewApi,
  locationApi,
  healthCheck<PERSON>pi,
  emailTemplate<PERSON>pi,
  jobsApi,
  jobRequestApi,
  candidateInterviewApi,
  wildcardBusinessesApi,
  wildCardPaymentApi,
  analyticApi,
  eSignatureApi,
};
