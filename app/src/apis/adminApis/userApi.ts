import { ADMIN_APIS } from "../ApiConstant";
import { API } from "../api";
import { JsonBody } from "../api/requestBuilder";

const { USER } = ADMIN_APIS;

const ReturnSuccess = (data: any) => data;

const ReturnError = (error: any) => {
  return {
    success: false,
    message: error.message,
    errors: error.errors ?? [],
  };
};

// Employee Signature API functions
const getUsers = (params: JsonBody) => {
  return API.get(USER, params)
    .then((response) => response.data)
    .then(ReturnSuccess)
    .catch(ReturnError);
};

const updateUserStatus = (id: number, data: JsonBody) => {
  return API.patch(`${USER}/${id}/status`, data)
    .then((response) => response.data)
    .then(ReturnSuccess)
    .catch(ReturnError);
};

// eslint-disable-next-line import/no-anonymous-default-export
export default {
  getUsers,
  updateUserStatus,
};
