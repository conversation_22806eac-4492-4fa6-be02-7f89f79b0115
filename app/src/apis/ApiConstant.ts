export const WILDCARD_APIS = {
  LOGIN: "/api/v1/wildcard/auth/login",
  FORGOT_PASSWORD: "/api/v1/wildcard/auth/forgot-password",
  VERIFY_FORGOT_PASSWORD_OTP:
    "/api/v1/wildcard/auth/verify-forgot-password-otp",
  RESEND_FORGOT_PASSWORD_OTP:
    "/api/v1/wildcard/auth/resend-forgot-password-otp",
  CHANGE_PASSWORD: "/api/v1/wildcard/auth/change-password",

  VERIFY_ME: "/api/v1/wildcard/auth/me",
  UPDATE_PASSWORD: "/api/v1/wildcard/auth/update-password",
  LOG_OUT: "/api/v1/wildcard/auth/logout",

  RESUME_EXTRACTION: "/api/v1/wildcard/resumes",
  ROUTES: "/api/v1/wildcard/routes",
  SETTINGS: "/api/v1/wildcard/settings",

  DEPARTMENT: "/api/v1/wildcard/departments",
  EMPLOYEES: "/api/v1/wildcard/employees",
  EMPLOYEE_ROLES: "/api/v1/wildcard/employee_roles",
  EMPLOYEE_SIGNATURES: "/api/v1/wildcard/employee-signatures",
  LOCATION: "/api/v1/wildcard/locations",
  CANDIDATE: "/api/v1/wildcard/candidates",
  OPPORTUNITY: "/api/v1/wildcard/opportunities",
  INTERVIEW: "/api/v1/wildcard/interviews",
  CANDIDATE_INTERVIEW: "/api/v1/wildcard/candidate_interviews",

  CANDIDATE_DOCUMENT: "/api/v1/wildcard/candidate/documents",
  EMAIL_TEMPLATE: "/api/v1/wildcard/email_templates",
  JOBS: "/api/v1/wildcard/jobs",
  JOB_REQUEST: "/api/v1/wildcard/job_requests",

  DASHBOARD: "/api/v1/wildcard/dashboard",
  BUSINESS: "/api/v1/wildcard/businesses",
  PAYMENT: "/api/v1/wildcard/payments",

  ANALYTICS: "/api/v1/wildcard/analytics",
};

export const CANDIDATE_APIS = {
  LOGIN: "/api/v1/candidates/auth/login",
  VERIFY_ME: "/api/v1/candidates/auth/me",
  LOG_OUT: "/api/v1/candidates/auth/logout",
  FORGOT_PASSWORD: "/api/v1/candidates/auth/forgot-password",
  CHANGE_PASSWORD: "/api/v1/candidates/auth/change-password",
  VERIFY_FORGOT_PASSWORD_OTP:
    "/api/v1/candidates/auth/verify-forgot-password-otp",
  RESEND_FORGOT_PASSWORD_OTP:
    "/api/v1/candidates/auth/resend-forgot-password-otp",
  UPDATE_PASSWORD: "/api/v1/candidates/auth/update-password",

  DASHBOARD: "/api/v1/candidates/dashboard",
  PROFILE: "/api/v1/candidates/profile",
  INTERVIEWS: "/api/v1/candidates/interviews",
  OPPORTUNITY: "/api/v1/candidates/opportunities",
  CODING_ROUND: "/api/v1/candidates/coding-rounds",
};

export const ADMIN_APIS = {
  LOGIN: "/api/v1/admin/auth/login",
  FORGOT_PASSWORD: "/api/v1/admin/auth/forgot-password",
  VERIFY_FORGOT_PASSWORD_OTP: "/api/v1/admin/auth/verify-forgot-password-otp",
  RESEND_FORGOT_PASSWORD_OTP: "/api/v1/admin/auth/resend-forgot-password-otp",
  CHANGE_PASSWORD: "/api/v1/admin/auth/change-password",

  VERIFY_ME: "/api/v1/admin/auth/me",
  UPDATE_PASSWORD: "/api/v1/admin/auth/update-password",
  LOG_OUT: "/api/v1/admin/auth/logout",

  ROUTES: "/api/v1/admin/routes",
  SETTINGS: "/api/v1/admin/settings",

  REGISTER: "/api/v1/admin/register",
  VERIFY_USER_ACCOUNT: "/api/v1/admin/verify-account",
  RESEND_ACCOUNT_VERIFY_OTP: "/api/v1/admin/resend-verify-email",

  PAYMENT: "/api/v1/admin/payments",
  BUSINESS: "/api/v1/admin/businesses",
  BUSINESS_LOGIN: "/api/v1/admin/auth/business_login",
  DASHBOARD: "/api/v1/admin/dashboard",
  USER: "/api/v1/admin/users",
};

export const HOMEPAGE_APIS = {
  CONTACT_US_ENQUERY: "/api/v1/home/<USER>",
};
