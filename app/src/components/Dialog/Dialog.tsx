import { useAppDispatch } from "@src/redux/store";
import { Modal } from "antd";
import { closeDialog } from "@src/redux/actions";
import React from "react";

// Define the type for the Dialog component props
type DialogProps = {
  open: boolean; // Whether the dialog should be open or closed
  className?: string; // Additional CSS class name for styling the dialog
  title: string; // Title of the dialog
  component: React.ComponentType<{ close: () => void } & Record<string, any>>; // Component to render inside the dialog
  beforeCloseCallback?: () => void; // Optional function to call when the dialog is closed
  [key: string]: any; // This allows any other props to be passed
};

// Define the Dialog component
export const Dialog: React.FC<DialogProps> = (props) => {
  // Destructure props
  const {
    open,
    className,
    title,
    styles,
    component: DialogComponent,
    width,
    maskClosable = false,
    closable = true,
    beforeCloseCallback,
    ...otherProps
  } = props;

  // Get the dispatch function from the Redux store
  const dispatch = useAppDispatch();

  // Function to handle closing the dialog
  const onClose = () => {
    // Dispatch the closeDialog action to update the Redux state
    if (beforeCloseCallback) {
      beforeCloseCallback();
    }
    dispatch(closeDialog());
  };

  // Render the Dialog component
  return (
    <>
      <Modal
        open={open} // Specify whether the modal is visible or hidden
        title={title} // Title of the modal
        onOk={onClose} // Function to handle OK button click
        onCancel={onClose} // Function to handle Cancel button click and clicking outside the modal
        className={className} // Additional CSS class name for styling the modal
        footer={null} // Hide the footer buttons (OK and Cancel)
        styles={styles}
        closable={closable}
        keyboard={closable}
        maskClosable={maskClosable} // Prevent closing the modal by clicking outside
        width={width}>
        {/* Render the component inside the modal */}
        <DialogComponent close={onClose} closable={closable} {...otherProps} />
      </Modal>
    </>
  );
};
