import React, { useState } from "react";
import type { GlobalInputFieldType } from "../input/GlobalInput";
import { ModalFormInput } from "../ModalInput/ModalFormInput";
import { KeyPairInterface } from "@src/redux/interfaces";
import { useAppDispatch } from "@src/redux/store";
import { setLoader } from "@src/redux/actions";
import flashMessage from "../FlashMessage";
import { candidateApi } from "@src/apis/wildcardApis";

// Props for the SignatureOTPModal component
type SignatureOTPModalProps = {
  candidateId: number;
  handleSignatureSave: (base64Image: string) => void;
  close: () => void;
  employeeId: number;
};

// Default values for the OTP
const defaultValue: KeyPairInterface = {
  otp: "",
};

const DefaultOTPFields: GlobalInputFieldType[] = [
  {
    name: "otp",
    label: "OTP",
    type: "text",
    dataType: "onlynumber",
    minLength: 6,
    maxLength: 6,
    required: true,
  },
];

function SignatureOTPModal(props: SignatureOTPModalProps) {
  const { close, candidateId, handleSignatureSave, employeeId } = props;
  const dispatch = useAppDispatch();

  // State to manage form data
  const [state, setState] = useState<KeyPairInterface>(defaultValue);

  // Function to handle form submission
  const onSubmit = async () => {
    await dispatch(setLoader(true));
    // Call API to create a new employee
    const { success, ...response } = await candidateApi.verifySignatureOTP(
      candidateId,
      { ...state, employee_id: employeeId },
    );
    await dispatch(setLoader(false));
    // Display flash message based on API response
    flashMessage(response.message, success ? "success" : "error");
    // If employee creation is successful, call the callback function to update the UI
    if (success) {
      // const base64Image = response.data?.base64_image ?? "";
      const signature = response.data?.signature_url ?? "";
      const responseData = await fetch(signature);
      const blob = await responseData.blob();

      // Convert the blob to base64
      const base64Image: any = await convertBlobToBase64(blob);
      handleSignatureSave(base64Image);
      close();
    }
  };

  const convertBlobToBase64 = (blob: any) => {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onloadend = () => resolve(reader.result);
      reader.onerror = (error) => reject(error);
      reader.readAsDataURL(blob); // This converts the Blob to base64
    });
  };

  const handleResend = async (): Promise<boolean> => {
    await dispatch(setLoader(true));
    const { success, ...response } =
      await candidateApi.resendSignatureOTPRequest(candidateId, {
        employee_id: employeeId,
      });
    await dispatch(setLoader(false));

    flashMessage(response.message, success ? "success" : "error");
    return success;
  };

  return (
    <>
      {/* Render the modal form input component */}
      <ModalFormInput
        buttonTitle="Submit"
        fields={DefaultOTPFields}
        setState={setState}
        state={state}
        onSubmit={onSubmit}
        onClose={close}
        onResend={handleResend}
      />
    </>
  );
}

export default SignatureOTPModal;
