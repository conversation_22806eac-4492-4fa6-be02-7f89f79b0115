import React, { useEffect, useState } from "react";
import { useAppDispatch } from "@src/redux/store";
import { setGoogleAuthToken, setLoader } from "@src/redux/actions";
import { Button } from "antd";
import flashMessage from "@src/components/FlashMessage";
import { RootState } from "@src/redux/reducers";
import { useSelector } from "react-redux";
import Image from "next/image";
import Script from "next/script";
import { fetchGoogleDriveFile } from "@src/helper/oauth";
import { GOOGLE_API_KEY } from "@src/constants";

// Props for the GoogleDriveResume component
type GoogleDriveResumeProps = {
  disabled: boolean;
  onSetResumes: (files: any[]) => void;
};

const DISCOVERY_DOC =
  "https://www.googleapis.com/discovery/v1/apis/drive/v3/rest";

// Component for the modal to add a new candidate
export const GoogleDriveResume = ({
  onSetResumes,
  disabled,
}: GoogleDriveResumeProps) => {
  const dispatch = useAppDispatch();

  // ======================== Google Drive functions =============================
  const [isGapiLoaded, setIsGapiLoaded] = useState<boolean>(false);
  const [isGisLoaded, setIsGisLoaded] = useState<boolean>(false);
  const googleAuth = useSelector((state: RootState) => state.ouath.google_auth);

  useEffect(() => {
    if (window.gapi) {
      window.gapi.load("picker", () => {
        setIsGapiLoaded(true);
      });
    }

    if (window.google && !isGisLoaded) {
      setIsGisLoaded(true);
    }
  }, []);

  const openGoogleOAuthPopup = () => {
    const callbackUrl = `${window.location.origin}/google/auth/callback`;
    const authUrl = `${process.env.NEXT_PUBLIC_AUTH_BACKEND}/google/login?callback=${encodeURIComponent(callbackUrl)}`;

    const popup = window.open(authUrl, "_blank", "width=500,height=600");

    // Listen for message from popup
    const handleMessage = (event: MessageEvent) => {
      if (event.origin !== window.location.origin) return;

      const { token, expires_at } = event.data;
      if (token) {
        dispatch(setGoogleAuthToken({ token, expires_at }));
        flashMessage("Google account linked successfully", "success");
        openGoogleDrivePicker(token);
      }

      window.removeEventListener("message", handleMessage);
      popup?.close();
    };

    window.addEventListener("message", handleMessage);
  };

  const clickOpenDrive = () => {
    if (!googleAuth) {
      // Redirect user to login
      openGoogleOAuthPopup();
      return;
    }

    if (googleAuth.expires_at * 1000 < Date.now()) {
      openGoogleOAuthPopup();
      return;
    }

    openGoogleDrivePicker(googleAuth.token);
  };

  const openGoogleDrivePicker = async (token: string) => {
    if (!isGapiLoaded || !isGisLoaded) return;

    const view = new window.google.picker.DocsView()
      .setIncludeFolders(false)
      .setMimeTypes("application/pdf,application/vnd.google-apps.document");

    const picker = new window.google.picker.PickerBuilder()
      .enableFeature(window.google.picker.Feature.MULTISELECT_ENABLED)
      .addView(view)
      .setOAuthToken(token)
      .setDeveloperKey(GOOGLE_API_KEY)
      .setOrigin(window.location.origin)
      .setCallback(async (data: any) => {
        if (data.action === window.google.picker.Action.PICKED) {
          const selectedFiles = data.docs;
          if (selectedFiles.length > 5) {
            flashMessage("Please select up to 5 files only.", "error");
            return;
          }
          dispatch(setLoader(true));
          const uploadListPromises = selectedFiles.map(async (file: any) => {
            const fileObj = await fetchGoogleDriveFile(
              file.id,
              file.name,
              file.mimeType,
              token,
            );
            let finalFileName = file.name;
            if (!finalFileName.includes(".")) {
              finalFileName = `${finalFileName}.pdf`;
            }

            return {
              uid: file.id,
              name: finalFileName,
              status: "done",
              originFileObj: fileObj,
            };
          });

          const files = await Promise.all(uploadListPromises);
          dispatch(setLoader(false));

          onSetResumes(files);
        }
      })
      .build();

    picker.setVisible(true);
  };

  // ======================== Google Drive functions =============================

  return (
    <>
      {/* Google API */}
      <Script
        src="https://apis.google.com/js/api.js"
        strategy="afterInteractive"
        onLoad={() => {
          if (window.gapi && !isGapiLoaded) {
            window.gapi.load("client:picker", async () => {
              await window.gapi.client.init({
                apiKey: GOOGLE_API_KEY,
                discoveryDocs: [DISCOVERY_DOC],
              });
              setIsGapiLoaded(true);
            });
          }
        }}
      />
      {/* Google Identity */}
      <Script
        src="https://accounts.google.com/gsi/client"
        strategy="afterInteractive"
        onLoad={() => {
          if (window.google && !isGisLoaded) {
            setIsGisLoaded(true);
          }
        }}
      />

      <Button
        className="cloud-select-picker-button"
        onClick={clickOpenDrive}
        disabled={disabled}>
        <div>
          <Image
            src={"/images/icons/google-drive.svg"}
            height={78}
            width={88}
            alt=""
          />
        </div>
        Upload From Google Drive
      </Button>
    </>
  );
};
