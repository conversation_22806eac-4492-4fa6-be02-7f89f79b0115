import React from "react";
import { Button } from "antd";

// Props for the SuccessModal component
type SuccessModalProps = {
  message: string | React.ReactNode; // Message to display in the modal
  close: Function; // Function to close the modal
  closable: boolean;
  closeButtonTitle?: string;
};

// Component for the success modal
const SuccessModal = ({
  message,
  close,
  closeButtonTitle,
  closable,
}: SuccessModalProps) => {
  return (
    <>
      {/* Display the message */}
      {message ?? ""}
      {/* Modal footer with close button */}
      {closable && (
        <div className="ant-modal-footer">
          <Button className="btn" key="cancel" onClick={() => close()}>
            {closeButtonTitle ?? "Cancel"}
          </Button>
        </div>
      )}
    </>
  );
};

export default SuccessModal;
