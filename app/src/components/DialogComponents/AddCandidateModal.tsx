import { <PERSON><PERSON> } from "react-bootstrap";
import FileUploadIcon from "@mui/icons-material/FileUpload";
import AddIcon from "@mui/icons-material/Add";
import Image from "next/image";
import { DROPBOX_APP_KEY, GOOGLE_API_KEY } from "@src/constants";

// Props for the AddCandidateModal component
type AddCandidateModalProps = {
  close: Function;
  addCandidateManually: Function;
  addByUpload: (type: string) => void;
};

// Component for the modal to add a new candidate
const AddCandidateModal = ({
  addCandidateManually,
  addByUpload,
  close,
}: AddCandidateModalProps) => {
  const onAddManully = () => {
    close();
    addCandidateManually();
  };

  const byUploadFile = (type: string) => {
    addByUpload(type);
  };

  return (
    <>
      <div>
        <p>
          Select your preferred method to add a new candidate to our system. You
          can either input the details manually or upload a resume, and we will
          automatically extract the relevant information for you.
        </p>
      </div>
      <div className="candidate-modal pt-4 pb-4 text-center">
        <Button
          onClick={() => byUploadFile("Upload File")}
          className="btn-upload-resume w-100 mb-4">
          <FileUploadIcon /> Upload Resume
        </Button>

        {GOOGLE_API_KEY && GOOGLE_API_KEY != "" && (
          <Button
            onClick={() => byUploadFile("Google Drive")}
            className="btn-upload-resume w-100 mb-4">
            <Image
              src={"/images/icons/google-drive.svg"}
              className={"mr-2"}
              height={24}
              width={24}
              alt=""
            />
            From Google Drive
          </Button>
        )}

        {DROPBOX_APP_KEY && DROPBOX_APP_KEY != "" && (
          <Button
            onClick={() => byUploadFile("Dropbox")}
            className="btn-upload-resume w-100 mb-4">
            <Image
              src={"/images/icons/dropbox.svg"}
              className={"mr-2"}
              height={24}
              width={24}
              alt=""
            />
            From Dropbox
          </Button>
        )}

        <div className="mb-3">OR</div>

        <Button onClick={onAddManully} className="btn-add-manually w-100">
          <AddIcon /> Add Manually
        </Button>
      </div>
    </>
  );
};

export default AddCandidateModal;
