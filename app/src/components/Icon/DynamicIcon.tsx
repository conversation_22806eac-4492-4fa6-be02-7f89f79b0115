// DynamicIcon.tsx
import React from "react";

// src/components/Icon/iconMap.ts
import DashboardCustomize from "@mui/icons-material/DashboardCustomize";
import GroupAdd from "@mui/icons-material/GroupAdd";
import Department from "@mui/icons-material/Category";
import Work from "@mui/icons-material/Work";
import Group from "@mui/icons-material/Group";
import Settings from "@mui/icons-material/Settings";
import Schedule from "@mui/icons-material/Schedule";
import Event from "@mui/icons-material/Schedule";
import Location from "@mui/icons-material/LocationOn";
import CheckBoxOutlineBlankIcon from "@mui/icons-material/CheckBoxOutlineBlank";
import TocIcon from "@mui/icons-material/Toc";
import BusinessIcon from "@mui/icons-material/Business";
import Image from "next/image";

// Add more icons as needed

const AnalyticIcon: React.FC<{ color?: string; size?: number }> = ({
  size = 24,
}) => (
  <Image
    src="/images/icons/hiring-managment-icon.svg"
    alt="custom icon"
    width={size}
    height={size}
    className="custom-node-icon"
    style={{ display: "inline-block" }}
  />
);

const SignatureIcon: React.FC<{ color?: string; size?: number }> = ({
  size = 24,
}) => (
  <Image
    src="/images/icons/signature-pencil-management-icon.svg"
    alt="custom icon"
    width={size}
    height={size}
    className="custom-node-icon"
    style={{ display: "inline-block" }}
  />
);

const iconMap: { [key: string]: React.ComponentType<any> } = {
  dashboard: DashboardCustomize,
  candidate: GroupAdd,
  department: Department,
  opportunity: Work,
  employee: Group,
  business: BusinessIcon,
  user: Group,
  permission: Settings,
  schedule: Schedule,
  event: Event,
  location: Location,
  job_request: TocIcon,
  analytics_management: AnalyticIcon,
  signature_management: SignatureIcon,
};

// Define props for the component
interface DynamicIconProps {
  name: keyof typeof iconMap;
  color?: string;
  size?: number;
}

const DynamicIcon: React.FC<DynamicIconProps> = ({
  name,
  color = "inherit",
  size = 24,
}) => {
  // Create a dynamic import for the icon based on the name

  const IconComponent = iconMap[name];

  if (!IconComponent) {
    return <CheckBoxOutlineBlankIcon style={{ color, fontSize: size }} />;
  }

  return <IconComponent style={{ color, fontSize: size }} />;
};

export default DynamicIcon;
