import React, { useEffect } from "react";
import { WarningAmber } from "@mui/icons-material";

type Props = {
  message: string;
  type?: "success" | "error" | "warning";
  duration?: number; // in ms
  warningCount?: number;
  maxWarning?: number;
  onClose: () => void;
};

const iconColors = {
  success: "#28a745",
  error: "#dc3545",
  warning: "#dc3545", //"#ffc107",
};

export const FullscreenFlashMessage: React.FC<Props> = ({
  message,
  type = "success",
  duration = 5000,
  maxWarning = 5,
  warningCount = 0,
  onClose,
}) => {
  useEffect(() => {
    const modalWrap = document.querySelector(".ant-modal-wrap");
    if (modalWrap) {
      modalWrap.classList.add("warning-modal-opacity");
    }
  }, []);

  useEffect(() => {
    const timer = setTimeout(onClose, duration);
    return () => clearTimeout(timer);
  }, [duration, onClose]);

  const headerColor = iconColors[type];

  return (
    <>
      <div className="d-flex flex-row gap-3 warning-modal">
        <span>
          <WarningAmber style={{ fontSize: 35 }} />
        </span>

        <div className="content-box">
          <div className="flex-box">
            <h4>Exam Violation Warning</h4>
          </div>
          <div
            style={{ color: "#8f8f8f", paddingLeft: "0px", margin: "10px 0" }}
            dangerouslySetInnerHTML={{
              __html: message
                .split("\n")
                .filter((line) => line.trim() !== "")
                .map((line, index) =>
                  index === 0
                    ? `<div style="margin-bottom: 10px;">${line.trim()}</div>`
                    : `<li style="list-style-type: disc; margin-left: 0;">${line.trim()}</li>`,
                )
                .join(""),
            }}
          />
          <div className="note">
            Note: The examination will be terminated after {maxWarning}{" "}
            warnings.
          </div>
        </div>
        <div className="count">
          Warning Count: <span>{warningCount}</span>
        </div>
      </div>
    </>
  );
};
