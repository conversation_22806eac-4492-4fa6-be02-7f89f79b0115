import React from "react";
import { CurrentTime } from "../Common";
import { useRouter } from "next/router";
import { ArrowBack } from "@mui/icons-material";

interface PageHeaderProps {
  pageTitle: string;
  pageDescription?: string | React.ReactNode;
  buttonComponent?: React.ReactNode;
  children?: React.ReactNode;
  breadcrumb?: string[];
  insideCard?: boolean;
  showBackButton?: boolean;
  onBack?: () => void;
}

const PageHeader: React.FC<PageHeaderProps> = ({
  pageTitle,
  pageDescription,
  buttonComponent,
  children,
  breadcrumb = [],
  insideCard = true,
  showBackButton,
  onBack,
}) => {
  const router = useRouter();

  const handleBack = () => {
    if (onBack) {
      onBack();
    } else {
      router.back();
    }
  };

  return (
    <>
      <div className="d-flex align-items-center justify-content-between  mb-3">
        <div className="bredcrubs d-flex gap-3 align-items-end align-self-center">
          {showBackButton && (
            <ArrowBack
              fontSize={"small"}
              onClick={handleBack}
              className="cursor-pointer"
            />
          )}
          <h1 className="m-0 page-head">{pageTitle}</h1>
          {breadcrumb.map((val: string, index: number) => (
            <h4
              className="m-0 page-head primary-clr position-relative ps-3"
              key={index}>
              {val}
            </h4>
          ))}
        </div>
        <div className="d-flex align-items-center gap-3">
          <CurrentTime />
          {!insideCard && buttonComponent}
        </div>
      </div>

      {insideCard ? (
        <div className="row">
          <div className="col-12">
            <div className="card m-0">
              {(pageDescription || buttonComponent) && (
                <div className="card-header common-heading border-bottom p-3 d-flex align-items-center">
                  <div className="d-flex align-items-center flex-wrap flex-lg-nowrap gap-3 w-100">
                    <h4 className="mb-0 heading-clr page-description">
                      {pageDescription}
                    </h4>
                    {buttonComponent}
                  </div>
                </div>
              )}
              <div className="card-body">{children}</div>
            </div>
          </div>
        </div>
      ) : (
        children
      )}
    </>
  );
};

export default PageHeader;
