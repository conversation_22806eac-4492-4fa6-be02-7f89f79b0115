import React, { useEffect, useState } from "react";
import { useSelector } from "react-redux";
import { RootState } from "@src/redux/reducers";
import { useAppDispatch } from "@src/redux/store";
import {
  EmployeeRoleInterface,
  KeyPairInterface,
  NodeSettingInterface,
  PermissionInterface,
} from "@src/redux/interfaces";
import {
  setLoader,
  getAllPermissionList,
  getAllNodesList,
  getAllEmployeeRolesOptions,
  openDialog,
} from "@src/redux/actions";
import SelectField from "../input/SelectField";
import { Button, Form } from "react-bootstrap";
import { settingApi } from "@src/apis/commonApis";
import flashMessage from "../FlashMessage";
import PageHeader from "../PageHeader/PageHeader";
import { SwitchWithTooltip } from "@src/helper/actions";
import DialogComponents from "../DialogComponents";

type NodeSettingListProps = {
  subdomain: string;
  pageDetail: KeyPairInterface;
};

type NodePermissionInterface = {
  [key: number]: number[];
};

type PermissionRequired = {
  [key: string]: { [dependent_permission_id: number]: string };
};

type DisabledPermission = {
  [key: string]: { [dependent_permission_id: number]: string };
};

export function NodeSettingList({ pageDetail }: NodeSettingListProps) {
  const dispatch = useAppDispatch();
  const [loading, setLoading] = useState<boolean>(false);
  const [employeeRoleId, setEmployeeRoleId] = useState<any>();
  const [nodePermissions, setNodePermissions] =
    useState<NodePermissionInterface>({});
  const [requiredPermission, setRequiredPermission] =
    useState<PermissionRequired>({});
  const employeeRoleOptions = useSelector(
    (state: RootState) => state.employee.rolesOption,
  );
  const { permissions, dependentPermissions, disabledPermissions } =
    useSelector((state: RootState) => state.setting);

  const nodes = useSelector((state: RootState) => state.setting.nodes);

  const currentEmployee = useSelector(
    (state: RootState) => state.auth.employee,
  );

  const isAdmin = ["Super Admin", "Admin"].includes(
    currentEmployee?.employee_role ?? "",
  );

  useEffect(() => {
    fetchPermissions(employeeRoleId);
    /* eslint-disable react-hooks/exhaustive-deps */
  }, [employeeRoleId]);

  useEffect(() => {
    if (!employeeRoleId && employeeRoleOptions.length > 0) {
      const roleId = employeeRoleOptions[0].value as number;
      setEmployeeRoleId(roleId);
      fetchEmployeeRoleNodes(roleId);
    }
    /* eslint-disable react-hooks/exhaustive-deps */
  }, [employeeRoleOptions]);

  useEffect(() => {
    let nodePermissions: NodePermissionInterface = {};
    nodes.map((node: NodeSettingInterface) => {
      nodePermissions = { ...nodePermissions, [node.id]: node.permissions };
    });
    setNodePermissions(nodePermissions);
    /* eslint-disable react-hooks/exhaustive-deps */
  }, [nodes]);

  useEffect(() => {
    const updateRequiredPermission = () => {
      const updatedRequiredPermission: PermissionRequired = {};

      Object.keys(nodePermissions).forEach((nodeId) => {
        const permissions = nodePermissions[+nodeId];

        if (permissions) {
          permissions.forEach((permissionId) => {
            if (dependentPermissions[+nodeId]) {
              dependentPermissions[+nodeId].forEach((dep) => {
                if (dep.permission_id === permissionId) {
                  if (!updatedRequiredPermission[+dep.node_id]) {
                    updatedRequiredPermission[+dep.node_id] = {};
                  }
                  if (
                    !updatedRequiredPermission[+dep.node_id][
                      dep.dependent_permission_id
                    ]
                  ) {
                    updatedRequiredPermission[+dep.node_id][
                      dep.dependent_permission_id
                    ] = dep.tooltip;
                  } else {
                    updatedRequiredPermission[+dep.node_id][
                      dep.dependent_permission_id
                    ] += "<br /><br />" + dep.tooltip;
                  }
                }
              });
            }
          });
        }
      });
      setRequiredPermission(updatedRequiredPermission);
    };

    updateRequiredPermission();
  }, [nodePermissions, dependentPermissions]);

  const fetchPermissions = async (role_id?: number) => {
    await dispatch(setLoader(true));
    await dispatch(getAllPermissionList(false, { role_id }));
    await dispatch(setLoader(false));
  };

  const fetchEmployeeRolesOption = async (search: string) => {
    await setLoading(true);
    await dispatch(getAllEmployeeRolesOptions({ search: search }));
    await setLoading(false);
  };

  const fetchEmployeeRoleNodes = async (employee_role_id: number) => {
    await setLoading(true);
    await dispatch(getAllNodesList(false, { employee_role_id }));
    await setLoading(false);
  };

  const onEmployeeRoleSelect = async (event: any) => {
    await setEmployeeRoleId(event.target.value);
    await fetchEmployeeRoleNodes(event.target.value);
  };

  const onPermissionChange = async (nodeId: number, permissionId: number) => {
    setNodePermissions((prev) => {
      const updatedNodePermissions = { ...prev };

      if (!updatedNodePermissions[nodeId]) {
        updatedNodePermissions[nodeId] = [];
      }

      const permissionIndex =
        updatedNodePermissions[nodeId].indexOf(permissionId);

      if (permissionIndex === -1) {
        // Permission not present, add it
        updatedNodePermissions[nodeId] = [
          ...updatedNodePermissions[nodeId],
          permissionId,
        ];

        // Add dependent permissions recursively
        const addDependentPermissions = (
          nodeId: number,
          permissionId: number,
        ) => {
          if (dependentPermissions[nodeId]) {
            dependentPermissions[nodeId].forEach((dep) => {
              if (dep.permission_id === permissionId) {
                updatedNodePermissions[dep.node_id] =
                  updatedNodePermissions[dep.node_id] ?? [];
                if (
                  !updatedNodePermissions[dep.node_id].includes(
                    dep.dependent_permission_id,
                  )
                ) {
                  updatedNodePermissions[dep.node_id] = [
                    ...updatedNodePermissions[dep.node_id],
                    dep.dependent_permission_id,
                  ];
                  addDependentPermissions(
                    dep.node_id,
                    dep.dependent_permission_id,
                  );
                }
              }
            });
          }
        };

        addDependentPermissions(nodeId, permissionId);
      } else {
        // Permission present, remove it
        updatedNodePermissions[nodeId] = updatedNodePermissions[nodeId].filter(
          (id) => id !== permissionId,
        );
      }

      return updatedNodePermissions;
    });
  };

  const updatePermissions = async () => {
    await dispatch(setLoader(true));
    const nodePermissionsHash: any = {};
    nodes.map((node) => {
      nodePermissionsHash[node.id] = nodePermissions[node.id];
    });
    const { success, ...response } = await settingApi.updateBulkNodePermissions(
      false,
      {
        employee_role_id: employeeRoleId,
        permissions: nodePermissionsHash,
      },
    );
    flashMessage(response.message, success ? "success" : "error");
    await dispatch(setLoader(false));
  };

  // Open the modal to add a new business
  const openNewEmployeeRoleModal = () => {
    dispatch(
      openDialog({
        config: DialogComponents.NEW_EMPLOYEE_ROLE_MODAL,
        options: {
          onEmployeeRoleCreate: async (employeeRole: EmployeeRoleInterface) => {
            await fetchEmployeeRolesOption("");
            await setEmployeeRoleId(employeeRole.id);
          },
        },
      }),
    );
  };

  const addNewRoleButton = isAdmin && (
    <Button onClick={openNewEmployeeRoleModal} className="ms-auto mw-180px">
      + Add New Role
    </Button>
  );

  return (
    <>
      <PageHeader
        pageTitle={pageDetail?.title ?? "Permission Management"}
        pageDescription={
          pageDetail?.description ?? "Permission Management Description"
        }
        buttonComponent={addNewRoleButton}>
        <>
          <div className="d-flex mb-4">
            <div className="d-flex">
              <Form.Label className="align-content-center m-0 mr-3">
                <strong>Role:</strong>
              </Form.Label>
              <SelectField
                value={employeeRoleId}
                name={"employee_role_id"}
                label={"Role"}
                style={{ display: "none" }}
                className={"employee-role-select"}
                placeholder={"Select Role"}
                showSearch={false}
                options={employeeRoleOptions}
                onSearch={fetchEmployeeRolesOption}
                loading={loading}
                onChangeInput={onEmployeeRoleSelect}
              />
            </div>
            <div className="ms-auto">
              <Button className="btn-theme" onClick={() => updatePermissions()}>
                Update
              </Button>
            </div>
          </div>

          <div className="node-setting-list">
            <table
              className="table table-hover dataTable"
              style={{ width: "100%" }}>
              <thead>
                <tr role="row">
                  <th className="mw-100px">Page</th>
                  {permissions.map(
                    (permission: PermissionInterface, index: number) => {
                      return (
                        <th className="mw-100px capitalize-text" key={index}>
                          {permission.name == "write"
                            ? "add/create"
                            : permission.name == "read"
                              ? "View"
                              : permission.name}
                        </th>
                      );
                    },
                  )}
                  {/* <th className="mw-100px">Action</th> */}
                </tr>
              </thead>
              <tbody>
                {nodes.map((node: NodeSettingInterface, index: number) => {
                  return (
                    <tr key={index}>
                      <th>{node.name}</th>
                      {permissions.map(
                        (permission: PermissionInterface, index: number) => {
                          const disabledTooltip =
                            disabledPermissions &&
                            disabledPermissions[node.id] &&
                            disabledPermissions[node.id][permission.id];
                          const tooltip =
                            requiredPermission &&
                            requiredPermission[node.id] &&
                            requiredPermission[node.id][permission.id];
                          const checked = !!(
                            tooltip ||
                            (nodePermissions[node.id] &&
                              nodePermissions[node.id].includes(permission.id))
                          );

                          return (
                            <td key={index}>
                              <SwitchWithTooltip
                                className={`switch-theme ${!disabledTooltip ? "" : "disabled"}`}
                                checked={!disabledTooltip && checked}
                                disabled={!!(tooltip || disabledTooltip)}
                                tooltipTitle={disabledTooltip || tooltip}
                                htmlToolTip={true}
                                onChange={() =>
                                  onPermissionChange(node.id, permission.id)
                                }
                              />
                            </td>
                          );
                        },
                      )}
                    </tr>
                  );
                })}
              </tbody>
            </table>
          </div>
        </>
      </PageHeader>
    </>
  );
}
