import React from "react";
import Link from "next/link";
import { APP_ROUTE } from "@src/constants";

export const ReadyHomeSection = () => {
  return (
    <>
      <section className="cta-section">
        <div className="container">
          <div className="cta-wrapper secondarybg rounded-4">
            {/* <Image
              src="/images/home/<USER>/cta-bg.png"
              layout="fill"
              priority={true}
              objectFit="cover"
              className="background-banner"
              alt="background-image"
              quality={80}
              style={{ zIndex: "-1" }}
            /> */}
            <h4 className="text-white">
              Your Ideal Candidate is Now Just A Click Away
            </h4>
            <p className="text-white mb-4">
              Join countless HR professionals and recruiters who trust our
              Recruitease Pro for their hiring needs. Register today to start
              optimizing your recruitment process, or contact us to learn more
              about how our system can benefit your organization
            </p>
            <ul className="list-inline">
              <li className="list-inline-item">
                <Link
                  href={APP_ROUTE.CONTACT_US}
                  className="btn btn-light d-inline-flex align-items-center">
                  Schedule a Call
                </Link>
              </li>
              <li className="list-inline-item">
                <Link
                  href={APP_ROUTE.REGISTER.subdomainLink("admin")}
                  className="btn btn-primary">
                  Sign Up Now
                </Link>
              </li>
            </ul>
          </div>
        </div>
      </section>
    </>
  );
};
