import React from "react";
import Image from "next/image";
import Link from "next/link";
import { APP_ROUTE } from "@src/constants";

export const Footer = () => {
  const currentYear = new Date().getFullYear();
  return (
    <>
      <footer className="footer text-center">
        <div className="container">
          <Image
            src="/images/logo-new.svg"
            className="mb-4"
            width={280}
            height={40}
            alt="logo"
          />
          <p className="max-550 mx-auto">
            Recruitease Pro is making the recruitment process easier with its{" "}
            <strong>A</strong>pplicant <strong>T</strong>racking{" "}
            <strong>S</strong>ystem. It makes parsing resumes to scheduling
            interviews and tracking analytics easier.
            {/* Whether you're a seasoned HR professional or just getting started, we've compiled a comprehensive list of frequently asked questions to address any queries you may have about our advanced Recruitease Pro platform.  Checkout our FAQ section now to simplify  your recruitment process with our innovative Recruitease Pro. */}
          </p>
          <ul className="list-inline footer-menu mb-4">
            <li className="list-inline-item">
              <Link href={APP_ROUTE.HOME.subdomainLink("www")}>Home</Link>
            </li>
            <li className="list-inline-item">
              <Link href={APP_ROUTE.ABOUT_US}>About Us</Link>
            </li>
            <li className="list-inline-item">
              <Link href={APP_ROUTE.WORK_PROCESS}>How it works</Link>
            </li>
            <li className="list-inline-item">
              <Link href={APP_ROUTE.CONTACT_US}>Contact Us</Link>
            </li>
            <li className="list-inline-item">
              <Link href={APP_ROUTE.TERMS_AND_CONDITION}>Terms of Use</Link>
            </li>
            <li className="list-inline-item">
              <Link href={APP_ROUTE.PRIVACY_POLICY}>Privacy Policy</Link>
            </li>
          </ul>
          <ul className="list-inline social-media mb-3">
            <li className="list-inline-item">
              <Link
                href="https://www.facebook.com/TalentelgiaTechnologies/"
                target="_blank">
                <Image
                  src="/images/home/<USER>/sm-icon1.png"
                  width={18}
                  height={18}
                  alt="icon"
                />
              </Link>
            </li>
            <li className="list-inline-item">
              <Link href="https://twitter.com/talentelgia" target="_blank">
                <Image
                  src="/images/home/<USER>/sm-icon2.png"
                  width={18}
                  height={18}
                  alt="icon"
                />
              </Link>
            </li>
            <li className="list-inline-item">
              <Link
                href="https://in.pinterest.com/talentelgia/"
                target="_blank">
                <Image
                  src="/images/home/<USER>/sm-icon3.png"
                  width={18}
                  height={18}
                  alt="icon"
                />
              </Link>
            </li>
            <li className="list-inline-item">
              <Link
                href="https://www.linkedin.com/company/talentelgia-technologies"
                target="_blank">
                <Image
                  src="/images/home/<USER>/sm-icon4.png"
                  width={18}
                  height={18}
                  alt="icon"
                />
              </Link>
            </li>
            <li className="list-inline-item">
              <Link
                href="https://www.instagram.com/talentelgia_technologies/"
                target="_blank">
                <Image
                  src="/images/home/<USER>/sm-icon5.png"
                  width={18}
                  height={18}
                  alt="icon"
                />
              </Link>
            </li>
            <li className="list-inline-item">
              <Link href="https://www.youtube.com/@talentelgia" target="_blank">
                <Image
                  src="/images/home/<USER>/sm-icon6.png"
                  width={18}
                  height={18}
                  alt="icon"
                />
              </Link>
            </li>
          </ul>
        </div>
        <div className="copyright-content py-3 mt-5">
          <div className="container-fluid">
            <div className="row text-muted">
              <div className="col-12 text-center">
                <p className="mb-0">
                  Copyright © {currentYear}{" "}
                  <Link
                    href={APP_ROUTE.HOME.subdomainLink("www")}
                    className="secondary">
                    Recruitease Pro
                  </Link>
                  &nbsp; Inc. All rights reserved. | Powered by{" "}
                  <a
                    href="https://www.talentelgia.com/"
                    target="_blank"
                    rel="noopener noreferrer"
                    className="secondary">
                    Talentelgia Technologies Pvt Limited
                  </a>
                </p>
              </div>
            </div>
          </div>
        </div>
      </footer>
    </>
  );
};
