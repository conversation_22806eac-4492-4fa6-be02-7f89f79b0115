import React, { useEffect, useState } from "react";
import { APP_ROUTE } from "@src/constants";
import useWindowWidth from "@src/helper/resize";
import {
  AuthAdminInterface,
  SidebarNodeInterface,
} from "@src/redux/interfaces";
import { useAppDispatch } from "@src/redux/store";
import Image from "next/image";
import Link from "next/link";
import { useRouter } from "next/router";
import { useSelector } from "react-redux";
import { RootState } from "@src/redux/reducers";
import { setSideBar } from "@src/redux/actions";
import { Nav } from "react-bootstrap";
import DynamicIcon from "@src/components/Icon/DynamicIcon";

type AdminSidebarProps = {
  admin: AuthAdminInterface;
};

export const AdminSidebar: React.FC<AdminSidebarProps> = () => {
  const dispatch = useAppDispatch();
  const { sidebarOpen } = useSelector((state: RootState) => state.menu);
  const nodes = useSelector((state: RootState) => state.auth.nodes);
  const width = useWindowWidth();
  const [prevOpen, setPrevOpen] = useState<boolean>(false);

  useEffect(() => {
    if (width > 768 && !prevOpen) {
      dispatch(setSideBar(true));
    }
  }, [width, prevOpen, dispatch]);

  const router = useRouter();

  return (
    <>
      <Nav
        id="sidebar"
        className={`sidebar-content js-simplebar sidebar js-sidebar ${sidebarOpen ? "" : "collapsed"}`}>
        <div className="sidebar-content js-simplebar">
          <Link
            className="sidebar-brand"
            href={APP_ROUTE.HOME.subdomainLink("www")}>
            <Image
              src={"/images/logo-new.svg"}
              height={38}
              width={220}
              alt=""
            />
          </Link>

          <ul className="sidebar-nav">
            {nodes.length > 0 &&
              nodes.map((node: SidebarNodeInterface, index: number) => {
                return (
                  <li
                    className={`sidebar-item ${router.pathname.startsWith(node.path) ? "active" : ""}`}
                    key={index}>
                    <Link
                      className="sidebar-link align-middle"
                      href={node.path}>
                      {node?.icon && <DynamicIcon name={node.icon} />}
                      <span>{node.name}</span>
                    </Link>
                  </li>
                );
              })}
          </ul>
        </div>
      </Nav>
    </>
  );
};
