import React, { useEffect, useState } from "react";
import { useSelector } from "react-redux";
import { RootState } from "@src/redux/reducers";
import Head from "next/head";
import Loader from "@src/components/Loader/Loader";
import { Header as PublicHeader } from "../wildcard_public_layout/Header";
import { useRouter } from "next/router";
import { useAppDispatch } from "@src/redux/store";
import { candidateVerify, logoutCandidate } from "@src/redux/actions";
import flashMessage from "@src/components/FlashMessage";
import { APP_ROUTE } from "@src/constants";
import DialogContainer from "@src/components/Dialog/DialogContainer";

type CandidatePublicLayoutProps = {
  children: React.ReactNode;
  subdomain: string;
};

export const CandidatePublicLayout: React.FC<CandidatePublicLayoutProps> = ({
  children,
}) => {
  const [loader, setLoader] = useState<boolean>(true);
  const { loader: loading, loaderText } = useSelector(
    (state: RootState) => state.loader,
  );
  const candidate = useSelector((state: RootState) => state.auth.candidate);
  const router = useRouter();
  const dispatch = useAppDispatch();

  const verifyUserAuth = async () => {
    const { success, message: msg } = await dispatch(candidateVerify());
    if (!success) {
      flashMessage(msg, "error");
      await dispatch(logoutCandidate());
      await router.replace(APP_ROUTE.CANDIDATE_LOGIN);
    }
    await setLoader(false);
  };

  useEffect(() => {
    if (candidate) {
      verifyUserAuth();
    } else {
      router.replace(APP_ROUTE.CANDIDATE_LOGIN);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [candidate?.id]);

  if (loader) {
    return (
      <div>
        <div id="wrapper" />
      </div>
    );
  }

  return (
    <>
      <Head>
        <title>Recruitease Pro</title>
        <meta name="description" content="Recruitease Pro" />
        <meta name="viewport" content="width=device-width, initial-scale=1" />
        <link rel="icon" href="/favicon.ico" />
      </Head>

      <DialogContainer />
      {loading && <Loader loaderText={loaderText} />}

      <section className="candidate-layout h-100">
        <PublicHeader />
        <div id="wrapper">
          <div className="container-fluid">{children}</div>
        </div>
      </section>
    </>
  );
};
