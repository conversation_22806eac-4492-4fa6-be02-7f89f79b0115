import { APP_ROUTE } from "@src/constants";
import Image from "next/image";
import Link from "next/link";
import React from "react";

export const Header = () => {
  return (
    <>
      <nav className="navbar navbar-expand navbar-dark bg-theme topbar mb-4 static-top shadow">
        <Link
          className="navbar-brand ml-4"
          href={APP_ROUTE.HOME.subdomainLink("www")}>
          <Image
            src="/images/logo-white-1.svg"
            alt="logo"
            width={220}
            height={60}
            style={{ height: "auto" }}
            priority
          />
        </Link>
      </nav>
    </>
  );
};
