import Image from "next/image";
import React, { useEffect, useState } from "react";
import { useRouter } from "next/router";
import { useSelector } from "react-redux";
import { RootState } from "@src/redux/reducers";
import Head from "next/head";
import { APP_ROUTE } from "@src/constants";
import Loader from "@src/components/Loader/Loader";
import { useAppDispatch } from "@src/redux/store";
import { setAdminDetailState } from "@src/redux/actions";
import Link from "next/link";

type AuthLayoutProps = {
  children: React.ReactNode;
  readonly subdomain: string;
  readonly layoutfor: string;
};

export const AuthLayout: React.FC<AuthLayoutProps> = ({
  children,
  subdomain,
  layoutfor,
}) => {
  const [loader, setLoader] = useState<boolean>(true);
  const { admin, employee, candidate } = useSelector(
    (state: RootState) => state.auth,
  );
  const { loader: loading, loaderText } = useSelector(
    (state: RootState) => state.loader,
  );

  const router = useRouter();
  const dispatch = useAppDispatch();

  useEffect(() => {
    if (admin && subdomain == "admin") {
      const verify_path = `${APP_ROUTE.VERIFY_ACCOUNT}?token=${btoa(admin.email.trim())}`;
      if (admin.email_verified) {
        router.replace(APP_ROUTE.DASHBOARD);
      } else if (verify_path !== router.asPath) {
        router.replace(verify_path);
        dispatch(setAdminDetailState(null));
      } else {
        setLoader(false);
      }
    } else if (employee && subdomain != "admin" && layoutfor != "candidate") {
      if (employee.id) {
        router.replace(APP_ROUTE.DASHBOARD);
      } else {
        setLoader(false);
      }
    } else if (candidate && subdomain != "admin" && layoutfor == "candidate") {
      if (candidate.id) {
        router.replace(APP_ROUTE.CANDIDATE_DASHBOARD);
      } else {
        setLoader(false);
      }
    } else {
      setLoader(false);
    }
  }, [admin, employee, candidate, subdomain, layoutfor, router, dispatch]);

  if (loader) {
    return (
      <div>
        <div id="wrapper" />
      </div>
    );
  }

  return (
    <>
      <Head>
        <title>Recruitease Pro</title>
        <meta name="description" content="Recruitease Pro" />
        <meta name="viewport" content="width=device-width, initial-scale=1" />
        <link rel="icon" href="/favicon.ico" />
      </Head>

      {loading && <Loader loaderText={loaderText} />}
      <section className="auth">
        <div className="row">
          <div className="col sm-12 md-6 xl-6 d-flex align-items-center justify-content-center min-vh-100 mobile-bg">
            <div className="content-box">
              <div className="box">
                <Link
                  href={APP_ROUTE.HOME.subdomainLink("www")}
                  className="mb-4">
                  <Image
                    className="main-logo desktop-logo"
                    src={"/images/logo-new.svg"}
                    width={350}
                    height={60}
                    alt="Logo Here"
                  />
                  <Image
                    className="main-logo mobile-logo"
                    src={"/images/logo.png"}
                    width={350}
                    height={60}
                    alt="Logo Here"
                  />
                </Link>
                {children}
              </div>
            </div>
          </div>

          <div className="col sm-6 md-6 xl-6 d-none d-md-flex auth-banner">
            <div className="banner-text">
              <h2>
                Empower Your <span>Hiring</span> -Transform the Way You Recruit
              </h2>
              <Image
                className="main-logo desktop-logo img-responsive"
                src={"/images/home/<USER>"}
                width={535}
                height={514}
                alt="Banner"
              />
            </div>
          </div>
        </div>
      </section>
    </>
  );
};
