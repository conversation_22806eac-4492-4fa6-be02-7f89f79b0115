import React from "react";
import Image from "next/image";
import icon1 from "../../../assets/images/icon1.svg";
import bottomlayer from "../../../assets/images/bottom-layer.svg";

export const HowItWorkSection = () => {
  return (
    <section className="position-relative w-100">
      <div className="how-it-work py-100 secondarybg text-white position-relative ">
        <div className="container">
          <div className="text-center">
            <h6 className="ft20 primary mb-0">How It Works</h6>
            <h4 className="ft38 medium">
              How Recruitease Pro Works to Boost Your Efficiency
            </h4>
          </div>

          <div className="row mt-50">
            <div className="col-lg-3 col-md-6 work-box text-center">
              <div className="shape-box position-relative mb-2">
                <Image src={icon1} alt="icon" />
                <div className="textOverlay">01</div>
              </div>
              <h5 className="ft20 medium">Create Job Postings</h5>
              <p className="text-white ft14">
                Post and share job descriptions—fast, simple, and fully
                customizable.
              </p>
            </div>

            <div className="col-lg-3 col-md-6 work-box text-center">
              <div className="shape-box position-relative mb-2">
                <Image src={icon1} alt="icon" />
                <div className="textOverlay">02</div>
              </div>
              <h5 className="ft20 medium">Review Resumes</h5>
              <p className="text-white ft14">
                AI automatically filters resumes to spot top talent without the
                clutter.
              </p>
            </div>

            <div className="col-lg-3 col-md-6 work-box text-center">
              <div className="shape-box position-relative mb-2">
                <Image src={icon1} alt="icon" />
                <div className="textOverlay">03</div>
              </div>
              <h5 className="ft20 medium">Manage Candidates</h5>
              <p className="text-white ft14">
                Monitor candidate’s progress easily, all organized in one place.
              </p>
            </div>

            <div className="col-lg-3 col-md-6 work-box text-center">
              <div className="shape-box position-relative mb-2">
                <Image src={icon1} alt="icon" />
                <div className="textOverlay">04</div>
              </div>
              <h5 className="ft20 medium">Schedule Interviews</h5>
              <p className="text-white ft14">
                Schedule and manage interviews directly for better coordination
                and communication.
              </p>
            </div>
          </div>
        </div>
      </div>
      <Image src={bottomlayer} alt="layer" className="w-100 bottomlayer" />

      {/* <section className="work-process py-80 lightbg">
        <div className="container">
          <div className="common-heading mb-5">
            <h5 className="primary">How It Works</h5>
            <h4 className="semibold mb-4">
              How Recruitease Pro Works to Boost Your Efficiency
            </h4>
            <p>
              Recruitease Pro streamlines the hiring process by saving time and
              improving productivity for HR teams. Read to find out how:{" "}
            </p>
          </div>
          <div className="row">
            <div className="col-md-12 col-lg-4 col-xl-4 col-sm-12">
              <div className="work-process-card mb-4 position-relative">
                <p className="steps">01</p>
                <div className="process-icon position-relative mb-4">
                  <Image
                    className="position-relative"
                    src="/images/home/<USER>/process-icon1.png"
                    height={40}
                    width={40}
                    alt="icon"
                  />
                </div>
                <div className="process-content">
                  <h4 className="ft20 medium heading">Create Job Postings</h4>
                  <p className="mb-0">
                    Share job ads on different websites to reach more people.
                  </p>
                </div>
              </div>
            </div>
            <div className="col-md-12 col-lg-4 col-xl-4 col-sm-12">
              <div className="work-process-card mb-4 position-relative">
                <p className="steps">02</p>
                <div className="process-icon position-relative mb-4">
                  <Image
                    className="position-relative"
                    src="/images/home/<USER>/process-icon2.png"
                    height={40}
                    width={40}
                    alt="icon"
                  />
                </div>
                <div className="process-content">
                  <h4 className="ft20 medium heading">Review Resumes</h4>
                  <p className="mb-0">
                    The system automatically checks resumes and picks out the
                    best ones based on your needs.
                  </p>
                </div>
              </div>
            </div>
            <div className="col-md-12 col-lg-4 col-xl-4 col-sm-12">
              <div className="work-process-card mb-4 position-relative">
                <p className="steps">03</p>
                <div className="process-icon position-relative mb-4">
                  <Image
                    className="position-relative"
                    src="/images/home/<USER>/process-icon3.png"
                    height={40}
                    width={40}
                    alt="icon"
                  />
                </div>
                <div className="process-content">
                  <h4 className="ft20 medium heading">Manage Candidates</h4>
                  <p className="mb-0">
                    Keep track of all the people applying for jobs in one place.
                  </p>
                </div>
              </div>
            </div>
            <div className="col-md-12 col-lg-4 col-xl-4 col-sm-12">
              <div className="work-process-card mb-4 position-relative">
                <p className="steps">04</p>
                <div className="process-icon position-relative mb-4">
                  <Image
                    className="position-relative"
                    src="/images/home/<USER>/process-icon4.png"
                    height={40}
                    width={40}
                    alt="icon"
                  />
                </div>
                <div className="process-content">
                  <h4 className="ft20 medium heading">Schedule Interviews</h4>
                  <p className="mb-0">
                    Arrange interviews with candidates easily. It syncs with
                    your calendar to avoid double-booking.
                  </p>
                </div>
              </div>
            </div>
            <div className="col-md-12 col-lg-4 col-xl-4 col-sm-12">
              <div className="work-process-card mb-4 position-relative">
                <p className="steps">05</p>
                <div className="process-icon position-relative mb-4">
                  <Image
                    className="position-relative"
                    src="/images/home/<USER>/process-icon5.png"
                    height={40}
                    width={40}
                    alt="icon"
                  />
                </div>
                <div className="process-content">
                  <h4 className="ft20 medium heading">Analyze Data</h4>
                  <p className="mb-0">
                    Get reports on how your hiring process is going. See
                    what&apos;s working well and what needs improvement.
                  </p>
                </div>
              </div>
            </div>
            <div className="col-md-12 col-lg-4 col-xl-4 col-sm-12">
              <div className="work-process-card mb-4 position-relative">
                <p className="steps">06</p>
                <div className="process-icon position-relative mb-4">
                  <Image
                    className="position-relative"
                    src="/images/home/<USER>/process-icon6.png"
                    height={40}
                    width={40}
                    alt="icon"
                  />
                </div>
                <div className="process-content">
                  <h4 className="ft20 medium heading">Stay Updated</h4>
                  <p className="mb-0">
                    Get reminders so you don&apos;t miss any important steps in
                    the hiring process. Everyone involved knows what&apos;s
                    happening.
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section> */}
    </section>
  );
};
