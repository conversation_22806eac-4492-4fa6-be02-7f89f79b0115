import React from "react";
import Image from "next/image";
import Link from "next/link";
import { APP_ROUTE } from "@src/constants";
import shape1 from "../../../assets/images/shape1.svg";
import shape2 from "../../../assets/images/shape2.svg";
import bottomlayer1 from "../../../assets/images/bottom-shape.svg";

export const MainBannerSection = () => {
  return (
    <section className="position-relative w-100">
      <div className="main-banner secondarybg mb-80">
        <Image
          className="shape1 position-absolute img-responsive m-auto"
          src={shape1}
          alt="image"
        />
        <Image
          className="shape2 position-absolute img-responsive"
          src={shape2}
          alt="image"
        />
        <div className="container">
          <div className="row align-items-center">
            <div className="col-sm-12 col-md-12 col-lg-12 col-xl-12">
              <div className="banner-content ps-0 text-center">
                <p className="ft20 medium primary mb-2">
                  Welcome to our Advanced Recruitease Pro System!
                </p>
                <h1 className="text-white">
                  Empower Your Hiring -Transform the Way You Recruit
                </h1>
                <p className="text-white mb-5">
                  Capture the best talent effortlessly. Upload resumes, extract
                  vital skills, and match perfect candidates with AI-driven
                  precision.
                </p>
                <Link
                  href={APP_ROUTE.CONTACT_US.subdomainLink("www")}
                  className="btn btn-primary d-inline-flex align-items-center rounded-5">
                  Schedule a Demo Today!
                </Link>
              </div>
            </div>
            <div className="col-sm-12 col-md-12 col-lg-12 col-xl-12">
              <div className="banner-vector position-relative text-center">
                <Image
                  src="/images/home/<USER>"
                  alt="Banner"
                  height={498}
                  width={900}
                  priority
                  className="w-full h-auto"
                  sizes="(max-width: 768px) 100vw, 1200px"
                />
              </div>
            </div>
          </div>
        </div>
      </div>
      <Image src={bottomlayer1} alt="image" className="w-100 bottomlayer1" />
    </section>
  );
};
