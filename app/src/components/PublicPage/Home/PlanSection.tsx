import React, { useState } from "react";
import Image from "next/image";
import star from "../../../assets/images/star.svg";
import shape1 from "../../../assets/images/shape1.svg";
import shape2 from "../../../assets/images/shape2.svg";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { APP_ROUTE } from "@src/constants";

export const PlanSection = () => {
  const [activeTab, setActiveTab] = useState<string>("annual");
  const router = useRouter();
  const handleTabChange = (tab: string) => setActiveTab(tab);

  const plansDetails: any = {
    monthly: {
      pro: {
        plan: "Month",
        price: 9.99,
        features: [
          "Resume screening (up to 100 resumes)",
          "AI-driven interview scheduling",
          "Collaboration workflows",
          "AI Video Score Analysis",
          "100 AI credits/month",
          "5 AI screening rounds/month",
        ],
      },
      premium: {
        plan: "Month",
        price: 19.99,
        features: [
          "Resume screening (up to 200 resumes)",
          "AI-driven interview scheduling",
          "Collaboration workflows",
          "AI Video Score Analysis",
          "200 AI credits/month",
          "10 AI screening rounds/month",
        ],
      },
    },
    annual: {
      pro: {
        plan: "Year",
        price: 99.99,
        features: [
          "AI-powered assessments",
          "Comprehensive candidate analytics",
          "Interview Report Analysis",
          "500 AI credits/month",
          "18 AI screening rounds/month",
          "Up to 1000 resumes/month",
        ],
      },
      premium: {
        plan: "Year",
        price: 199.99,
        features: [
          "Advanced customization options",
          "Dedicated account support",
          "Additional ATS and HRMS integrations",
          "600 AI credits",
          "250 AI screening rounds",
          "300 resume uploads",
        ],
      },
    },
  };

  const handleBuyNow = () => {
    router.push(APP_ROUTE.REGISTER.subdomainLink("admin"));
  };

  return (
    <section className="plan-section pt-140 pb-0">
      <div className="container">
        <h4 className="medium ft38 text-center mb-5">
          Find the Perfect Plan to Match Your Hiring Goals
        </h4>

        {/* Toggle Tabs */}
        <div className="d-flex justify-content-center">
          <div className="plan-tabs-btn p-2 d-inline-flex">
            <button
              className={` ${activeTab === "monthly" ? "active" : ""}`}
              onClick={() => handleTabChange("monthly")}>
              Monthly
            </button>
            <button
              className={` ${activeTab === "annual" ? "active" : ""}`}
              onClick={() => handleTabChange("annual")}>
              Annual
            </button>
          </div>
        </div>

        {/* Pricing Cards */}
        <div className="row justify-content-center">
          {
            <>
              <div className="col-md-4">
                <div className="fixed-plan secondarybg">
                  <Image
                    className="planShape1 position-absolute img-responsive m-auto"
                    src={shape1}
                    alt="image"
                  />
                  <Image
                    className="planShape2 position-absolute img-responsive m-auto"
                    src={shape2}
                    alt="image"
                  />
                  <h5 className="text-white mb-2 ft24">
                    Transparent & Flexible Pricing Plans
                  </h5>
                  <span className="text-white mb-3">
                    Choose the plan that fits your hiring needs no hidden fees,
                    just powerful features.
                  </span>
                  <p>
                    Simplify your hiring process with our smart Applicant
                    Tracking System. From resume screening to candidate
                    selection, manage everything in one powerful
                    platform—faster, smarter, and easier.
                  </p>
                  <Link href="/contact-us" className="btn btn-primary mt-1">
                    Schedule a Demo Today!
                  </Link>
                </div>
              </div>
              <div className="col-md-4">
                <div className="plan-card">
                  <h6>Pro</h6>
                  <p>
                    Choose the plan that fits your hiring needs no hidden fees,
                    just powerful features.
                  </p>
                  <h2 className="ft38 semibold">
                    ${plansDetails[activeTab]["pro"]["price"]}{" "}
                    <span>/- Per {plansDetails[activeTab]["pro"]["plan"]}</span>
                  </h2>
                  <button
                    className="btn btn-primary w-100"
                    onClick={handleBuyNow}>
                    Buy Now
                  </button>
                  <ul>
                    {plansDetails[activeTab]["pro"]["features"].map(
                      (feature: string, index: number) => (
                        <li key={index}>{feature}</li>
                      ),
                    )}
                  </ul>
                </div>
              </div>
              <div className="col-md-4">
                <div className="plan-card plan-popular">
                  <span className="popular-tag">
                    <Image src={star} alt="star" width={16} height={16} />
                    Most Popular
                  </span>
                  <h6>Premium</h6>
                  <p>
                    Choose the plan that fits your hiring needs no hidden fees,
                    just powerful features.
                  </p>
                  <h2 className="ft38 semibold">
                    ${plansDetails[activeTab]["premium"]["price"]}{" "}
                    <span>
                      /- Per {plansDetails[activeTab]["premium"]["plan"]}
                    </span>
                  </h2>
                  <button className="btn btn-primary w-100">Buy Now</button>
                  <ul>
                    {plansDetails[activeTab]["premium"]["features"].map(
                      (feature: string, index: number) => (
                        <li key={index}>{feature}</li>
                      ),
                    )}
                  </ul>
                </div>
              </div>
            </>
          }
        </div>
      </div>
    </section>
  );
};
