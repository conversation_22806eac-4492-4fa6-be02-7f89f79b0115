import React from "react";
import Image from "next/image";

export const IntroSection = () => {
  return (
    <>
      <section className="intro-sec py-80 pt-120">
        <div className="container">
          <div className="row align-items-center">
            <div className="col-md-6">
              <h4>
                Revolutionizing Recruitment with{" "}
                <span> Advanced Recruitease Pro</span>
              </h4>
              <p>
                Our Applicant Tracking System (Recruitease Pro) redefines
                recruitment by streamlining the process of job posting,
                applicant tracking, resume screening, and interviewing—all
                within one powerful platform. Designed for HR professionals and
                recruiters, our system ensures you connect with the right
                candidates efficiently and effectively.
              </p>
              <a href="#" className="btn btn-primary">
                Know More
              </a>
            </div>
            <div className="col-md-6">
              <div className="ats-boxes d-flex flex-column">
                <div className="boxes-inner">
                  <div className="box-Items">
                    <div className="boxicon">
                      <Image
                        src="/images/home/<USER>/intro-recruit-icon.svg"
                        alt="icon"
                        width={32}
                        height={32}
                      />
                    </div>

                    <div>
                      <h5 className="medium">Streamlined Recruitment</h5>
                      <p className="mb-0">
                        Makes hiring faster and easier for smoother processes.
                      </p>
                    </div>
                  </div>
                </div>

                <div className="boxes-inner">
                  <div className="box-Items">
                    <div className="boxicon">
                      <Image
                        src="/images/home/<USER>/intro-resume-icon.svg"
                        alt="icon"
                        width={32}
                        height={32}
                      />
                    </div>

                    <div>
                      <h5 className="medium">Advanced Resume Screening</h5>
                      <p className="mb-0">
                        Uses smart technology to review candidate resumes
                        effectively
                      </p>
                    </div>
                  </div>
                </div>

                <div className="boxes-inner">
                  <div className="box-Items">
                    <div className="boxicon">
                      <Image
                        src="/images/home/<USER>/intro-mgt-icon.svg"
                        alt="icon"
                        width={32}
                        height={32}
                      />
                    </div>

                    <div>
                      <h5 className="medium">Efficient Candidate Management</h5>
                      <p className="mb-0">
                        Organizes candidate interactions efficiently for better
                        tracking
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
    </>
  );
};
