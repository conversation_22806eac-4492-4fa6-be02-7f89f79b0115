import React from "react";
import Image from "next/image";
import Link from "next/link";

export const FeatureNew = () => {
  return (
    <>
      <section className="Feature-Section">
        <div className="container">
          <h4 className="medium ft38 text-center">
            Discover More Engaging and Interactive Features to Elevate Your
            Experience
          </h4>

          <div className="feature-box">
            <div className="feature-box-inner row align-items-center">
              <div className="feature-img-box col-md-6">
                <div className="ft-box position-relative">
                  <div className="overlay-link-icon-outer">
                    <a href="" className="overlay-link-icon">
                      <Image
                        src="/images/call_received.svg"
                        alt="feature icon"
                        width={15}
                        height={15}
                        className="img-responsive"
                      />
                    </a>
                    <div className="shape-top"></div>
                    <div className="shape-bottom"></div>
                  </div>
                  <div className="img-box">
                    <Image
                      src="/images/feature1.jpg"
                      alt="feature icon"
                      width={1000}
                      height={1000}
                      className="img-responsive"
                    />

                    <div className="overlay-feature-text text-center">
                      <div className="overlay-feature-inner">
                        <h3 className="ft24 text-white">
                          Click to Explore Smarter Hiring
                        </h3>
                        <p className="text-white">
                          Discover how our AI-driven ATS can simplify your
                          recruitment workflow.
                        </p>
                        <Link href="/contact-us" className="btn btn-primary">
                          Schedule a Demo Today!
                        </Link>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <div className="feature-text col-md-6">
                <h5 className="ft20 medium mb-2 primary">
                  Extract and structure resume data instantly.
                </h5>
                <h3 className="medium mb-3">Intelligent Resume Parsing</h3>
                <p>
                  The system uses AI to scan and parse resumes, converting
                  unstructured data into organized profiles. This ensures faster
                  shortlisting and more accurate candidate matching.
                </p>
                <ul>
                  <li>Skill & Experience Mapping</li>
                  <li>Automated Candidate Profiles</li>
                </ul>
              </div>
            </div>

            <div className="feature-box-inner row align-items-center">
              <div className="feature-img-box col-md-6">
                <div className="ft-box position-relative">
                  <div className="overlay-link-icon-outer">
                    <a href="" className="overlay-link-icon">
                      <Image
                        src="/images/call_received.svg"
                        alt="feature icon"
                        width={15}
                        height={15}
                        className="img-responsive"
                      />
                    </a>
                    <div className="shape-top"></div>
                    <div className="shape-bottom"></div>
                  </div>
                  <div className="img-box">
                    <Image
                      src="/images/feature2.jpg"
                      alt="feature"
                      width={1000}
                      height={1000}
                      className="img-responsive"
                    />

                    <div className="overlay-feature-text text-center">
                      <div className="overlay-feature-inner">
                        <h3 className="ft24 text-white">
                          Click to Explore Smarter Hiring
                        </h3>
                        <p className="text-white">
                          Discover how our AI-driven ATS can simplify your
                          recruitment workflow.
                        </p>
                        <Link href="/contact-us" className="btn btn-primary">
                          Schedule a Demo Today!
                        </Link>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <div className="feature-text col-md-6">
                <h5 className="ft20 medium mb-2 primary">
                  Prioritize top talent with AI scoring
                </h5>
                <h3 className="medium mb-3">Smart Candidate Ranking</h3>
                <p>
                  Our system evaluates applicants based on role relevance,
                  experience, and custom job criteria—ranking them automatically
                  for quick decision-making.
                </p>
                <ul>
                  <li>AI-Driven Scoring Engine</li>
                  <li>Customizable Ranking Parameters</li>
                </ul>
              </div>
            </div>

            <div className="feature-box-inner row align-items-center">
              <div className="feature-img-box col-md-6">
                <div className="ft-box position-relative">
                  <div className="overlay-link-icon-outer">
                    <a href="" className="overlay-link-icon">
                      <Image
                        src="/images/call_received.svg"
                        alt="feature icon"
                        width={15}
                        height={15}
                        className="img-responsive"
                      />
                    </a>
                    <div className="shape-top"></div>
                    <div className="shape-bottom"></div>
                  </div>
                  <div className="img-box">
                    <Image
                      src="/images/feature3.jpg"
                      alt="feature"
                      width={1000}
                      height={1000}
                      className="img-responsive"
                    />

                    <div className="overlay-feature-text text-center">
                      <div className="overlay-feature-inner">
                        <h3 className="ft24 text-white">
                          Click to Explore Smarter Hiring
                        </h3>
                        <p className="text-white">
                          Discover how our AI-driven ATS can simplify your
                          recruitment workflow.
                        </p>
                        <Link href="/contact-us" className="btn btn-primary">
                          Schedule a Demo Today!
                        </Link>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <div className="feature-text col-md-6">
                <h5 className="ft20 medium mb-2 primary">
                  Keep candidates informed at every step
                </h5>
                <h3 className="medium mb-3">Automated Communication</h3>
                <p>
                  Send personalized emails and updates automatically, improving
                  candidate engagement and saving recruiter time.
                </p>
                <ul>
                  <li>Email & SMS Integration</li>
                  <li>Trigger-Based Messaging</li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </section>
    </>
  );
};
