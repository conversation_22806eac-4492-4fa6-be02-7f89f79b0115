import React from "react";

export const WorkProcessCRMSection = () => {
  return (
    <>
      <section className="dashboard-features-wrap secondarybg py-80">
        <div className="container">
          <div className="row align-items-center">
            <div className="col-md-12 col-sm-12 col-lg-6 col-xl-6">
              <div className="dashboard-features-pointer">
                <div className="text-white common-heading mb-5">
                  <h5>CRM and Dashboard</h5>
                  <h4 className="semibold">
                    Stay Organized And Stay in Control
                  </h4>
                </div>
                <div className="pointers-list">
                  <ul className="text-white d-grid">
                    <li className="position-relative">Resume Parsing</li>
                    <li className="position-relative">Advanced Search</li>
                    <li className="position-relative">Candidate Sourcing</li>
                    <li className="position-relative">In-built CRM Software</li>
                    <li className="position-relative">
                      Real-time Collaboration
                    </li>
                    <li className="position-relative">
                      Ability to Schedule Interviews
                    </li>
                    <li className="position-relative">Various Integrations</li>
                    <li className="position-relative">
                      Multi-channel Candidate Sourcing
                    </li>
                    <li className="position-relative">
                      Advanced Analytics and Reporting
                    </li>
                    <li className="position-relative">
                      Automation based on Events and Conditions
                    </li>
                  </ul>
                </div>
              </div>
            </div>
            <div className="col-md-12 col-sm-12 col-lg-6 col-xl-6">
              <div className="dashboard-features-pointer">
                <iframe
                  src="https://www.youtube.com/embed/hdpHNU2zKoE?si=DKUlM3LgzrKBB2aY"
                  title="Demo Video"
                  width={581}
                  height={410}
                  allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
                  referrerPolicy="strict-origin-when-cross-origin"></iframe>

                {/* <Image
                  src="/images/home/<USER>/featuring-vector.webp"
                  className="img-responsive"
                  alt="image"
                  width={581}
                  height={410}
                /> */}
              </div>
            </div>
          </div>
        </div>
      </section>
    </>
  );
};
