import Image from "next/image";
import React from "react";

type PageTitleProps = {
  title: string;
};

export const PageTitle = ({ title }: PageTitleProps) => {
  return (
    <>
      {/* inner-page-pattern.png */}
      <section className="inner-banner secondarybg text-center text-white">
        <Image
          src="/images/home/<USER>"
          layout="fill"
          priority={true}
          objectFit="cover"
          className="background-banner"
          alt="background-image"
          quality={80}
        />
        <div className="container relative">
          <h1 className="ft32 m-0 mt-5 text-white">{title}</h1>
        </div>
      </section>
    </>
  );
};
