import React from "react";
import { Pagination, Switch } from "antd";
import { useAppDispatch } from "@src/redux/store";
import { openDialog, setLoader, updateUserStatus } from "@src/redux/actions";
import DialogComponents from "@src/components/DialogComponents";
import { useSelector } from "react-redux";
import { RootState } from "@src/redux/reducers";
import Image from "next/image";

type AdminUserListProps = {
  fetchData: (page: number, limit: number) => void;
};

export function AdminUserList({ fetchData }: AdminUserListProps) {
  const dispatch = useAppDispatch();

  const { rows, count, currentPage, limit } = useSelector(
    (state: RootState) => state.user,
  );

  // Handle page change for pagination
  const handlePageChange = (page: number) => {
    fetchData(page, limit);
  };

  const hasRows = rows && rows.length > 0;

  const HandleUpdateStatus = async (user: any) => {
    dispatch(
      openDialog({
        config: DialogComponents.CONFIRMATION_MODAL,
        options: {
          title: "User Status Update Confirmation",
          message: (
            <div className="mt-2 mb-2">
              Are you sure you want to{" "}
              {user.status == 1 ? "deactivate" : "activate"} this user?
            </div>
          ),
          onConfirm: () => updateStatus(user),
        },
      }),
    );
  };

  const updateStatus = async (user: any) => {
    await dispatch(setLoader(true));
    await dispatch(updateUserStatus(user.user_id, user.status === 1 ? 0 : 1));
    await dispatch(setLoader(false));
  };

  return (
    <>
      {hasRows ? (
        <div
          className={`table-responsive job-request-list ${hasRows ? "" : "no-records"}`}>
          <table
            className="table table-hover dataTable"
            style={{ width: "100%" }}>
            <thead>
              <tr role="row">
                <th className="mw-50px">Sr. No</th>
                <th className="mw-100px">Name</th>
                <th className="mw-100px">Email</th>
                <th className="mw-100px">Account Verified</th>
                <th className="mw-100px">Total Business</th>
                <th className="mw-100px">Verified Business</th>
                <th className="mw-100px">Billable Business</th>
                <th className="mw-100px">Created At</th>
                <th className="mw-80px">Active</th>
              </tr>
            </thead>
            <tbody>
              {rows.map((user: any, index: number) => (
                <tr key={index}>
                  <th>{index + 1 + (currentPage - 1) * limit}</th>
                  <td>
                    {user.first_name} {user.last_name}
                  </td>
                  <td>{user.email}</td>
                  <td>{user.email_verified ? "Yes" : "No"}</td>
                  <td>{user.total_business}</td>
                  <td>{user.verfied_business}</td> {/* Fix Typo if necessary */}
                  <td>{user.billable_business}</td>
                  <td>
                    {user.created_at?.strftime("%B %d, %Y %I:%M %p") ?? "N/A"}
                  </td>
                  <td>
                    <div className="d-flex gap-2 align-items-center">
                      <Switch
                        checked={user.status === 1}
                        className="switch-theme"
                        onChange={() => HandleUpdateStatus(user)}
                      />
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      ) : (
        <div className="d-flex flex-column align-items-center justify-content-center bg-light py-5 px-3 text-center">
          {/* SVG Icon for user Management */}
          <Image
            src="/images/icons/user-icon.svg"
            height={64}
            width={64}
            className="mb-4"
            alt="icon"
          />

          {/* Heading */}
          <h5 className="fw-bold text-dark">User Management</h5>
        </div>
      )}

      <Pagination
        className="mt-4"
        current={currentPage}
        total={count}
        pageSize={limit}
        showSizeChanger={false}
        hideOnSinglePage
        onChange={handlePageChange}
      />
    </>
  );
}
