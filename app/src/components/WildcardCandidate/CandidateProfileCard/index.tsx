import React from "react";
import { Card, ListGroup } from "react-bootstrap";
import Call from "@mui/icons-material/Call";
import Image from "next/image";
import Link from "next/link";
import { Skeleton } from "antd";
import { CandidateInterface } from "@src/redux/interfaces";

type CandidateProfileCardType = {
  candidate: CandidateInterface | null;
};

export const CandidateProfileCard: React.FC<CandidateProfileCardType> = ({
  candidate,
}) => {
  if (!candidate) {
    return (
      <Card className="p-3 mb-4 candidate-self-card">
        <Card.Body className="p-0">
          <Skeleton avatar active paragraph={{ rows: 3 }} />
        </Card.Body>
      </Card>
    );
  }

  return (
    <>
      <Card className="p-3 mb-4 candidate-self-card schedule-interview-self-card">
        <Card.Body className="p-0">
          <div className="candidate-profile-card d-flex gap-3">
            <Image
              src={"/images/auth/undraw_profile.svg"}
              className="img-profile rounded-circle"
              width={60}
              height={60}
              alt=""
            />
            <div className="candidate-brief w-100">
              <h4 className="candidate-name ">{candidate.name}</h4>
              <p className="text-clr p-0 mb-0 ">{candidate.email}</p>
              <div className="flex-box">
                <div className="casndi-contact">
                  <span>
                    <Call className="material-icons" fontSize={"small"} />
                  </span>
                  <div className="contact-data">
                    <p className="mb-0 heading-clr fw-medium">
                      {candidate?.contact ?? "N/A"}
                    </p>
                  </div>
                </div>
                {candidate &&
                  (candidate.is_fresher ? (
                    <p>
                      <span>Fresher</span>
                    </p>
                  ) : (
                    candidate.total_experience && (
                      <p>
                        Experience:{" "}
                        <span>{candidate.total_experience} years</span>
                      </p>
                    )
                  ))}
              </div>
            </div>
          </div>

          {candidate.website || candidate.github || candidate.linkedin ? (
            <div className="candi-other-links">
              <div className="justify-content-center d-flex gap-2 align-items-center">
                <label>Social-Links:</label>
                <ListGroup horizontal className="list-inline mb-0">
                  {candidate.website && (
                    <ListGroup.Item className="list-inline-item">
                      <Link href={candidate.website} target="_blank">
                        <Image
                          src="/images/website.svg"
                          width={30}
                          height={20}
                          alt="icon"
                        />
                      </Link>
                    </ListGroup.Item>
                  )}
                  {candidate.github && (
                    <ListGroup.Item className="list-inline-item">
                      <Link href={candidate.github} target="_blank">
                        <Image
                          src="/images/icons/github.svg"
                          width={30}
                          height={20}
                          alt="icon"
                        />
                      </Link>
                    </ListGroup.Item>
                  )}
                  {candidate.linkedin && (
                    <ListGroup.Item className="list-inline-item">
                      <Link href={candidate.linkedin} target="_blank">
                        <Image
                          src="/images/icons/linkedin.svg"
                          width={30}
                          height={20}
                          alt="icon"
                        />
                      </Link>
                    </ListGroup.Item>
                  )}
                </ListGroup>
              </div>
            </div>
          ) : (
            <div className="mb-2" />
          )}
        </Card.Body>
      </Card>
    </>
  );
};
