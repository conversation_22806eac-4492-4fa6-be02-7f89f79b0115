import React from "react";
import Image from "next/image";
import Link from "next/link";
import { APP_ROUTE } from "@src/constants";
import { Button } from "react-bootstrap";

interface DisqualificationNoticeProps {
  warningMessages: string[];
  openFeedbackModal: () => void;
}

export const DisqualificationNotice: React.FC<DisqualificationNoticeProps> = ({
  warningMessages,
  openFeedbackModal,
}) => {
  return (
    <div className="card p-4 m-0">
      <div className="submission-confirmation text-center">
        <Image
          src="/images/thank-you-img.svg"
          alt="Disqualified"
          width={252}
          height={286}
        />
        <h2 className="mb-2 heading-clr font-semibold">
          You have been disqualified
        </h2>
        <p className="m-0">
          Unfortunately, due to multiple warnings, you have been disqualified
          from the process.
        </p>

        {/* Warnings Section */}
        <div className="warnings mt-4">
          <h5 className="text-danger">Warnings:</h5>
          <ul className="list-unstyled text-start">
            {warningMessages.length > 0 ? (
              warningMessages.map((message, index) => (
                <li
                  key={index}
                  className="text-danger"
                  dangerouslySetInnerHTML={{
                    __html: message.replace(/\n/g, "<br />"),
                  }}
                />
              ))
            ) : (
              <p className="text-muted">No warnings recorded.</p>
            )}
          </ul>
        </div>

        <div className="d-flex gap-3 justify-content-center mt-5">
          <Link href={APP_ROUTE.CANDIDATE_DASHBOARD}>
            <Button className="btn btn-primary">Go to dashboard</Button>
          </Link>
          <Button
            className="btn btn-primary-outline"
            onClick={openFeedbackModal}>
            Feedback
          </Button>
        </div>
      </div>
    </div>
  );
};
