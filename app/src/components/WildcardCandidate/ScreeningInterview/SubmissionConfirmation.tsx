import React from "react";
import Image from "next/image";
import Link from "next/link";
import { APP_ROUTE } from "@src/constants";
import { Button } from "react-bootstrap";

interface SubmissionConfirmationProps {
  openFeedbackModal: () => void;
}

export const SubmissionConfirmation: React.FC<SubmissionConfirmationProps> = ({
  openFeedbackModal,
}) => {
  return (
    <div className="card p-4 m-0">
      <div className="submission-confirmation text-center">
        <Image
          src="/images/thank-you-img.svg"
          alt="thank-you"
          width={252}
          height={286}
        />
        <h2 className="mb-2 heading-clr font-semibold">
          Thank you for your submission!
        </h2>
        <p className="m-0">We appreciate your participation in this process.</p>
        <p className="m-0">
          Your feedback is valuable to us, and we will review your answers
          thoroughly.
        </p>
        <p className="m-0">
          Rest assured, we will keep you updated on the next steps soon.
        </p>

        <div className="d-flex gap-3 justify-content-center mt-5">
          <Link href={APP_ROUTE.CANDIDATE_DASHBOARD}>
            <Button className="btn btn-primary">Go to dashboard</Button>
          </Link>
          <Button
            className="btn btn-primary-outline"
            onClick={openFeedbackModal}>
            Feedback
          </Button>
        </div>
      </div>
    </div>
  );
};
