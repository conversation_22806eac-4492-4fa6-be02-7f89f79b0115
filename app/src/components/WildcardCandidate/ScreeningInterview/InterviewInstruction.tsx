import React from "react";

interface InterviewInstructionsProps {
  testMinutes: number;
}

export const InterviewInstructions: React.FC<InterviewInstructionsProps> = ({
  testMinutes,
}) => {
  return (
    <div className="interview-instructions card p-4 m-0 h-100">
      <div className="card-header common-heading p-0 mb-3 border-0 bg-white">
        <h4 className="mb-2 heading-clr">
          Instructions for Your Interview Round
        </h4>
        <h6 className="mt-2">Welcome to your interview!</h6>

        <ul>
          <li>
            <strong>
              Click the &quot;Start&quot; button to begin your interview round.
            </strong>
          </li>
          <li>
            You will be presented with 20 questions and have a total of{" "}
            {testMinutes} minutes to complete the test.
          </li>
          <li>
            After {testMinutes} minutes, the test will be automatically
            submitted.
          </li>
          <li>
            You will receive updates about your interview within a few minutes
            after submission.
          </li>
        </ul>

        <h6 className="mb-2 mt-2">Important Notes:</h6>
        <ul>
          <li>
            <strong>
              Please do not open any other tabs or applications while taking
              this test.
            </strong>{" "}
            This may disrupt your recording and affect your performance.
          </li>
          <li>
            Make sure you have a stable internet connection and are in a quiet
            environment for the best experience.
          </li>
          <li>
            <strong>You are required to enable full screen sharing.</strong>{" "}
            This allows us to monitor your screen activity to ensure test
            integrity. Without screen sharing, you may not be allowed to
            proceed.
          </li>
          <li>
            <strong>
              Your webcam and screen activity are being continuously recorded.
            </strong>{" "}
            If any suspicious activity is detected — such as switching tabs,
            hiding your face, or looking away repeatedly — your exam may be
            immediately disqualified.
          </li>
          <li>
            <strong>
              We will provide you with 5 warnings during the exam.
            </strong>{" "}
            If you fail to comply with the instructions or continue suspicious
            behavior, your exam will be disqualified after the 5th warning.
          </li>
          <li>
            <strong>Suspicious activities include:</strong> Looking away from
            the camera, closing eyes, excessive blinking, or breaking eye
            contact with the screen.
          </li>
          <li>
            Ensure your face is clearly visible, and stay focused on the screen
            throughout the test.
          </li>
          <li>
            Multiple instances of suspicious behavior will trigger warnings and
            may result in disqualification.
          </li>
        </ul>
      </div>
    </div>
  );
};
