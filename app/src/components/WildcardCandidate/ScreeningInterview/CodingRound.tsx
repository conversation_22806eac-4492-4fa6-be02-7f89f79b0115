import React, { useRef, useState, useEffect } from "react";
import Editor from "@monaco-editor/react";
import { useAppDispatch } from "@src/redux/store";
import { setLoader } from "@src/redux/actions";
import { interviewsApi } from "@src/apis/wildcardCandidateApis";
import { Button } from "react-bootstrap";
import {
  InterviewAnswer,
  ScreeningQuestionInterface,
} from "@src/redux/interfaces";

interface CodingRoundProps {
  questionNumber: number;
  currentQuestionIndex: number;
  questionId: number;
  questions: ScreeningQuestionInterface[];
  answers: InterviewAnswer[];
  onSetAnswer: (answer: InterviewAnswer) => void;
  disable?: boolean;
  goToPreviousQuestion: () => void;
  goToNextQuestion: () => void;
  submitted?: boolean;
  handleSubmit: () => void;
}

export const CodingRound: React.FC<CodingRoundProps> = ({
  questionNumber,
  currentQuestionIndex,
  questionId,
  questions,
  answers,
  onSetAnswer,
  goToPreviousQuestion,
  goToNextQuestion,
  submitted = false,
  handleSubmit,
}) => {
  const dispatch = useAppDispatch();
  const editorRef = useRef<any>(null);
  const [language, setLanguage] = useState("javascript");
  const [code, setCode] = useState("// some comment");
  const [output, setOutput] = useState<string | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [testCases, setTestCases] = useState<any[]>([]);
  const [showOutputPanel, setShowOutputPanel] = useState(false);

  useEffect(() => {
    const currentQuestion = questions[currentQuestionIndex];
    if (currentQuestion) {
      setLanguage((currentQuestion.language ?? "").toLowerCase());
      setTestCases(currentQuestion.test_cases);
    } else {
      setLanguage("javascript");
      setTestCases([]);
    }
  }, [questions, questionId]);

  useEffect(() => {
    if (answers?.length) {
      const answer: string | null =
        answers?.find((ans) => ans.questionId === questionId)?.answer || null;
      setCode(answer || "");
    } else {
      setCode("");
    }
  }, [answers, questionId]);

  const handleEditorChange = (value: any) => {
    setCode(value || "");
    onSetAnswer({ questionId: questionId, answer: value });
  };

  function handleEditorDidMount(editor: any, monaco: any) {
    editorRef.current = editor;

    // Disable copy, paste, cut using commands
    editor.addCommand(monaco.KeyMod.CtrlCmd | monaco.KeyCode.KeyC, () => {});
    editor.addCommand(monaco.KeyMod.CtrlCmd | monaco.KeyCode.KeyV, () => {});
    editor.addCommand(monaco.KeyMod.CtrlCmd | monaco.KeyCode.KeyX, () => {});

    // Disable context menu for right click copy/paste
    editor.updateOptions({ contextmenu: false });

    // Disable suggestions/autocomplete
    monaco.languages.typescript.javascriptDefaults.setCompilerOptions({
      noLib: true,
      allowNonTsExtensions: true,
    });
  }

  function handleEditorWillMount(monaco: any) {
    // Globally disable autocomplete for JS
    monaco.languages.registerCompletionItemProvider("javascript", {
      provideCompletionItems: () => {
        return { suggestions: [] };
      },
    });
  }

  function handleEditorValidation() {
    return null;
  }

  const handleRun = async () => {
    setError(null);
    setOutput(null);
    setShowOutputPanel(true);

    await dispatch(setLoader(true));
    await HandleVerifyCode();
    await dispatch(setLoader(false));
  };

  const HandleVerifyCode = async () => {
    const { success, ...response } = await interviewsApi.runTestCases(
      questionId,
      {
        code: code,
        language,
      },
    );

    if (success) {
      setOutput(response.message);
      setTestCases(response.data);
    } else {
      setError(response.message);
    }
  };

  const formatTime = (seconds: number) => {
    const minutes = Math.floor(seconds / 60);
    const sec = seconds % 60;
    return `${minutes.toString().padStart(2, "0")}:${sec
      .toString()
      .padStart(2, "0")}`;
  };

  const isLastQuestion = questionNumber === questions.length;
  const currentQuestion = questions[currentQuestionIndex];

  if (!currentQuestion) {
    return <></>;
  }

  const codingLanguage = (currentQuestion.language ?? "").toLowerCase();
  const question = currentQuestion.question;

  return (
    <>
      <div className="row">
        <div className="col-md-6 col-sm-12">
          <div className="card bg-white custom-card card-left mb-0 h-100">
            <div className="card-body d-flex flex-column">
              <div className="question-box-outer">
                <h3 className="mb-3 d-flex align-items-center gap-2">
                  <span
                    className="rounded-circle d-inline-flex justify-content-center align-items-center"
                    style={{
                      width: "30px",
                      height: "30px",
                      fontSize: "14px",
                      backgroundColor: "#ebebeb",
                      color: "#8c8c8c",
                    }}>
                    {questionNumber}
                  </span>
                  Question
                </h3>
                {/* <p className="text-muted">Questions: <span className="badge bg-primary">02 out of 05</span></p> */}
                <div className="question-box">
                  <pre className="interview-question">{question}</pre>
                </div>

                <div className="test-cases-box">
                  <h5>Test Cases</h5>
                  {showOutputPanel && (
                    <p
                      className={`test-cases-result text-${error ? "danger" : "success"}`}>
                      {error ? error : output}
                    </p>
                  )}
                  <TestCasesResult testCases={testCases} />
                </div>
              </div>
              <div className="d-flex justify-content-between gap-2 mt-auto position-sticky bottom-0 bg-white">
                <Button
                  className="btn btn-primary"
                  disabled={currentQuestionIndex === 0}
                  onClick={goToPreviousQuestion}>
                  Previous
                </Button>
                <Button
                  className="btn btn-primary"
                  disabled={currentQuestionIndex === questions.length - 1}
                  onClick={goToNextQuestion}>
                  Next
                </Button>
                {isLastQuestion && !submitted && (
                  <Button
                    className="btn btn-primary border-0 d-flex gap-2 align-items-center"
                    onClick={() => handleSubmit()}>
                    Submit
                  </Button>
                )}
              </div>
            </div>
          </div>
        </div>
        <div className="col-md-6 col-sm-12">
          <div className="card bg-white custom-card mb-0 h-100">
            <div className="card-body d-flex flex-column">
              <div className="code-editor-head gap-2">
                <h4>Code Editor</h4>
              </div>
              <div className="code-editor mb-3">
                <Editor
                  key={questionId}
                  defaultLanguage={codingLanguage}
                  language={codingLanguage}
                  defaultValue={currentQuestion.starter_code}
                  value={code}
                  onChange={handleEditorChange}
                  onMount={handleEditorDidMount}
                  beforeMount={handleEditorWillMount}
                  onValidate={handleEditorValidation}
                  theme="vs-dark"
                  options={{
                    contextmenu: false,
                    suggestOnTriggerCharacters: false,
                    quickSuggestions: false,
                    parameterHints: { enabled: false },
                    copyWithSyntaxHighlighting: false,
                    minimap: { enabled: false },
                    acceptSuggestionOnEnter: "off",
                    acceptSuggestionOnCommitCharacter: false,
                  }}
                />
              </div>

              <div className="text-end  position-sticky bottom-0 bg-white mt-auto">
                <button className="btn btn-primary" onClick={handleRun}>
                  Run
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

const TestCasesResult = ({ testCases }: any) => {
  return (testCases ?? []).map((caseItem: any, index: number) => (
    <div
      className={`test-case test-${(caseItem.status ?? "pending").toLowerCase()}`}
      key={index}>
      <p>
        <strong>Input</strong>: {caseItem.input}
      </p>
      <p>
        <strong>Expected Output</strong>: {caseItem.expected_output.toString()}
      </p>
      {caseItem.actual_output !== undefined && (
        <p>
          <strong>Output</strong>: {caseItem.actual_output.toString()}{" "}
          {caseItem.actual_output.toString() ===
          caseItem.expected_output.toString() ? (
            <span style={{ color: "green" }}>✅</span>
          ) : (
            <span style={{ color: "red" }}>❌</span>
          )}
        </p>
      )}
    </div>
  ));
};
