import React from "react";
import { Pagination, Switch } from "antd";
import { useEmployeeSelectedPagePermissions } from "@src/helper/pagePermissions";
import { useAppDispatch } from "@src/redux/store";
import { openDialog, setLoader } from "@src/redux/actions";
import { Button } from "react-bootstrap";
import Image from "next/image";
import DialogComponents from "@src/components/DialogComponents";
import { useSelector } from "react-redux";
import { RootState } from "@src/redux/reducers";
import { updateEmployeeSignatureStatus } from "@src/redux/slices/signature";

type WildcardEsignatureListProps = {
  fetchData: (page: number, limit: number) => void;
  subdomain: string;
  addNewButton?: React.ReactNode;
};

export function WildcardEsignatureList({
  fetchData,
  addNewButton,
}: WildcardEsignatureListProps) {
  const dispatch = useAppDispatch();
  const candidatesPermissions =
    useEmployeeSelectedPagePermissions("candidates");

  const { rows, count, currentPage, limit } = useSelector(
    (state: RootState) => state.signature,
  );

  // Handle page change for pagination
  const handlePageChange = (page: number) => {
    fetchData(page, limit);
  };

  const hasRows = rows && rows.length > 0;

  const hasCandidateWritePermission = candidatesPermissions.includes("write");

  const HandleUpdateStatus = async (employee: any) => {
    dispatch(
      openDialog({
        config: DialogComponents.CONFIRMATION_MODAL,
        options: {
          title: "Staff Signature Status Update Confirmation",
          message: (
            <div className="mt-2 mb-2">
              Are you sure you want to{" "}
              {employee.status == 1 ? "deactivate" : "activate"} this staff
              signature? This action may affect the visibility of the signature
              in your business operations.
            </div>
          ),
          onConfirm: () => updateStatus(employee),
        },
      }),
    );
  };

  const updateStatus = async (employee: any) => {
    await dispatch(setLoader(true));
    await dispatch(
      updateEmployeeSignatureStatus(
        employee.employee_id,
        employee.status === 1 ? 0 : 1,
      ),
    );
    await dispatch(setLoader(false));
  };

  const onViewSignature = (employeeSign: any) => {
    let message = <></>,
      title = "Error";
    if (employeeSign.signature_url) {
      message = (
        <>
          <div className="container d-flex justify-content-center">
            <div className="border border-secondary rounded-lg overflow-hidden p-4 bg-white shadow-sm">
              <div
                className="position-relative w-100 mx-auto"
                style={{ maxWidth: "350px" }}>
                <Image
                  src={employeeSign.signature_url}
                  alt="Employee Signature"
                  width={350}
                  height={0}
                  className="object-contain rounded-md h-auto w-[350px]"
                  style={{ height: "auto" }}
                />
              </div>
            </div>
          </div>
        </>
      );
      title = `${employeeSign.employee_name?.titleize()} - Signature `;
    } else {
      message = (
        <div className="text-center text-gray-500 py-6">
          No signature uploaded for this staff
        </div>
      );
    }

    dispatch(
      openDialog({
        config: DialogComponents.SUCCESS_MODAL,
        options: {
          title: title,
          message: message,
          closeTitle: "Close",
        },
      }),
    );
  };

  return (
    <>
      {hasRows ? (
        <div
          className={`table-responsive job-request-list ${hasRows ? "" : "no-records"}`}>
          <table
            className="table table-hover dataTable"
            style={{ width: "100%" }}>
            <thead>
              <tr role="row">
                <th className="mw-50px">Sr. No</th>
                <th className="mw-100px">Employee</th>
                <th className="mw-100px">Email</th>
                <th className="mw-100px">Created At</th>
                <th className="mw-80px">Signature</th>
                <th className="mw-80px">Active</th>
              </tr>
            </thead>
            <tbody>
              {rows.map((employee: any, index: number) => (
                <tr key={index}>
                  <th>{index + 1 + (currentPage - 1) * limit}</th>
                  <td>{employee?.employee_name}</td>
                  <td>{employee?.employee_email}</td>
                  <td>
                    {employee.created_at?.strftime("%B %d, %Y %I:%M %p") ??
                      "N/A"}
                  </td>
                  <td>
                    <Button
                      onClick={() => onViewSignature(employee)}
                      disabled={!employee.signature_url}>
                      View Signature
                    </Button>
                  </td>
                  <td>
                    <div className="d-flex gap-2 align-items-center">
                      <Switch
                        checked={employee.status === 1}
                        className="switch-theme"
                        onChange={() => HandleUpdateStatus(employee)}
                        disabled={!hasCandidateWritePermission}
                      />
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      ) : (
        <div className="d-flex flex-column align-items-center justify-content-center bg-light py-5 px-3 text-center">
          {/* SVG Icon for Signature Management */}
          <Image
            src="/images/icons/signature-pencil-management-icon.svg"
            height={64}
            width={64}
            className="mb-4"
            alt="icon"
          />

          {/* Heading */}
          <h5 className="fw-bold text-dark">Signature Management</h5>

          {/* Description */}
          {addNewButton && (
            <div className="text-center">
              <p className="text-muted mb-3">
                Manage and track digital signatures. Add or review signature
                workflows and ensure compliance with your document signing
                processes.
              </p>
              <br />
              {addNewButton}
            </div>
          )}
        </div>
      )}

      <Pagination
        className="mt-4"
        current={currentPage}
        total={count}
        pageSize={limit}
        hideOnSinglePage
        onChange={handlePageChange}
      />
    </>
  );
}
