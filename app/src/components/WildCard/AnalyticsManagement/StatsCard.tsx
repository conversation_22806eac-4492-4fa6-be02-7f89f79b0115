import React, { useState, useEffect } from "react";
import { analyticApi } from "@src/apis/wildcardApis";
import WorkOutlineIcon from "@mui/icons-material/WorkOutline";
import PlayCircleOutlineIcon from "@mui/icons-material/PlayCircleOutline";
import GroupIcon from "@mui/icons-material/Group";
import HowToRegIcon from "@mui/icons-material/HowToReg";

type StatMeta = {
  label: string;
  key: string;
  color: string;
  trailColor: string;
  boxClass: string;
  icon: React.ElementType;
};

const statConfig: StatMeta[] = [
  {
    label: "Total Jobs",
    key: "totalJobs",
    color: "#2ed1a5", // your --primary (fresh green)
    trailColor: "#ccf4ea", // light transparent green background
    boxClass: "box-1",
    icon: WorkOutlineIcon,
  },
  {
    label: "Active Jobs",
    key: "activeJobs",
    color: "#26a88b", // your --primary-hover (darker green)
    trailColor: "#b2ded4", // softer teal background
    boxClass: "box-2",
    icon: PlayCircleOutlineIcon,
  },
  {
    label: "Total Candidates",
    key: "totalCandidates",
    color: "#02263b", // your --secondary (dark blue)
    trailColor: "#d7e4f2", // pale blue background
    boxClass: "box-3",
    icon: GroupIcon,
  },
  {
    label: "Total Hiring",
    key: "totalHiring",
    color: "#0b1541", // your --secondary-dark (darker navy)
    trailColor: "#c1cce4", // light navy/gray background
    boxClass: "box-4",
    icon: HowToRegIcon,
  },
];

export function StatsCard() {
  const [statValues, setStatValues] = useState<Record<string, number>>({
    totalJobs: 0,
    activeJobs: 0,
    totalCandidates: 0,
    totalHiring: 0,
  });

  useEffect(() => {
    const fetchStats = async () => {
      const { success, data } =
        await analyticApi.hiringManagementDashboardStats();
      if (success) {
        setStatValues((prev) => ({ ...prev, ...data }));
      }
    };

    fetchStats();
  }, []);

  return (
    <div
      className="stats-container"
      style={{
        display: "flex",
        gap: 20,
        justifyContent: "space-between",
        flexWrap: "wrap",
        padding: 16,
      }}>
      {statConfig.map(
        ({ label, key, color, trailColor, boxClass, icon: Icon }) => (
          <div
            key={key}
            className={`stat-box ${boxClass}`}
            style={{
              background: `linear-gradient(135deg, ${trailColor} 0%, #fff 100%)`,
              flex: "1 1 180px",
              minWidth: 180,
              borderRadius: 14,
              padding: 16,
              display: "flex",
              alignItems: "center",
              gap: 16,
              boxShadow: "0 6px 12px rgba(0, 0, 0, 0.08)",
              cursor: "pointer",
              transition: "transform 0.2s ease, box-shadow 0.2s ease",
            }}
            onMouseEnter={(e) => {
              (e.currentTarget as HTMLElement).style.transform =
                "translateY(-5px)";
              (e.currentTarget as HTMLElement).style.boxShadow =
                "0 12px 20px rgba(0,0,0,0.15)";
            }}
            onMouseLeave={(e) => {
              (e.currentTarget as HTMLElement).style.transform =
                "translateY(0)";
              (e.currentTarget as HTMLElement).style.boxShadow =
                "0 6px 12px rgba(0, 0, 0, 0.08)";
            }}>
            <div
              style={{
                backgroundColor: color,
                borderRadius: "50%",
                width: 44,
                height: 44,
                display: "flex",
                justifyContent: "center",
                alignItems: "center",
                boxShadow: `0 0 8px ${color}44`,
                flexShrink: 0,
              }}>
              <Icon style={{ fontSize: 28, color: "#fff" }} />
            </div>
            <div
              style={{
                flex: 1,
                display: "flex",
                justifyContent: "space-between",
                alignItems: "center",
                color,
                fontWeight: 700,
                fontSize: 15,
                userSelect: "none",
              }}>
              <span>{label}</span>
              <span style={{ fontSize: 24 }}>{statValues[key] ?? 0}</span>
            </div>
          </div>
        ),
      )}
    </div>
  );
}
