import React, { useEffect, useState } from "react";
import { analyticApi } from "@src/apis/wildcardApis";
import { Skeleton } from "antd";
import {
  LineChart,
  lineElementClasses,
  markElementClasses,
} from "@mui/x-charts";

type LineChartDataType = {
  data: number[];
  label: string[];
  title: string;
  xLabel?: string;
  yLabel?: string;
  has_data: boolean;
};

type StatsState = {
  hired_candidates_stats: LineChartDataType;
  stats_type: string;
};

const defaultStats: StatsState = {
  hired_candidates_stats: {
    data: Array(12).fill(0),
    label: Array(12).fill(""),
    title: "Hiring Analytics",
    xLabel: "Months",
    yLabel: "Values",
    has_data: false,
  },
  stats_type: "monthly",
};

export const HiringAnalyticsCard: React.FC = () => {
  const [loading, setLoading] = useState<boolean>(false);
  const [state, setStats] = useState<StatsState>(defaultStats);

  useEffect(() => {
    fetchCandidateAnalyticStats("monthly");
  }, []);

  const fetchCandidateAnalyticStats = async (type: string) => {
    try {
      setLoading(true);
      const response = await analyticApi.candidateAnalyticStats({
        filter: type,
      });

      if (response.success && response.data) {
        setStats({
          hired_candidates_stats: response.data.hired_candidates_stats,
          stats_type: type,
        });
      }
    } catch (error) {
      console.error("Failed to fetch line chart stats:", error);
    } finally {
      setLoading(false);
    }
  };

  const { hired_candidates_stats } = state;

  return (
    <div
      className="card flex-grow-1"
      style={{
        borderRadius: 12,
        boxShadow: "0 6px 15px rgba(0, 0, 0, 0.1)",
        overflow: "hidden",
        backgroundColor: "#fff",
        width: "100%",
      }}>
      {/* Accent Bar */}
      <div
        style={{
          height: 5,
          backgroundColor: "#2ed1a5",
          borderTopLeftRadius: 12,
          borderTopRightRadius: 12,
        }}
      />

      {/* Card Header */}
      <div
        style={{
          padding: "16px 24px",
          borderBottom: "1px solid #f0f0f0",
          fontWeight: 600,
          fontSize: 18,
          color: "#02263b",
          backgroundColor: "#f9fdfa",
        }}>
        {hired_candidates_stats?.title || "Hiring Analytics"}
      </div>

      {/* Chart Body */}
      <div style={{ padding: 24, width: "100%" }}>
        {loading ? (
          <Skeleton active paragraph={{ rows: 4 }} />
        ) : (
          <LineChart
            height={300}
            xAxis={[
              {
                scaleType: "point",
                data: hired_candidates_stats.label,
                label: hired_candidates_stats.xLabel,
              },
            ]}
            series={[
              {
                data: hired_candidates_stats.data,
                color: "#2ed1a5",
              },
            ]}
            sx={{
              [`& .${lineElementClasses.root}`]: {
                strokeWidth: 3,
                strokeLinecap: "round",
              },
              [`& .${markElementClasses.root}`]: {
                stroke: "#fff",
                strokeWidth: 2,
                fill: "#2ed1a5",
              },
            }}
          />
        )}
      </div>
    </div>
  );
};
