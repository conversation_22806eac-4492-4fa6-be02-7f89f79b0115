import React, { useState, useRef, useEffect } from "react";
import { Button, Form } from "react-bootstrap";
import { analyticApi } from "@src/apis/wildcardApis";
import { setLoader } from "@src/redux/actions";
import { useAppDispatch } from "@src/redux/store";
import flashMessage from "@src/components/FlashMessage";

export const ExportAnalytics = () => {
  const [showContainer, setShowContainer] = useState(false);
  const [startDate, setStartDate] = useState("");
  const [endDate, setEndDate] = useState("");
  const dispatch = useAppDispatch();
  const containerRef = useRef<HTMLDivElement>(null);

  const toggleContainer = () => {
    setShowContainer(!showContainer);
  };

  // Click outside handler
  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (
        containerRef.current &&
        !containerRef.current.contains(event.target as Node)
      ) {
        setShowContainer(false);
      }
    }

    if (showContainer) {
      document.addEventListener("mousedown", handleClickOutside);
    } else {
      document.removeEventListener("mousedown", handleClickOutside);
    }

    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [showContainer]);

  const handleExport = async () => {
    if (!startDate || !endDate) {
      flashMessage("Please select both start and end dates.");
      return;
    }
    try {
      const params = {
        start_date: startDate,
        end_date: endDate,
      };
      dispatch(setLoader(true));
      const response = await analyticApi.exportAnalyticsCsv(params);

      const url = window.URL.createObjectURL(
        new Blob([response.data], {
          type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
        }),
      );
      const link = document.createElement("a");
      link.href = url;
      link.setAttribute("download", "analytic-report.xlsx");
      document.body.appendChild(link);
      link.click();

      setShowContainer(false);
    } catch (error) {
      console.error("Failed to fetch line chart stats:", error);
    } finally {
      dispatch(setLoader(false));
    }
  };

  return (
    <div className="export-wrapper">
      <Button onClick={toggleContainer}>Export</Button>

      {showContainer && (
        <div className="export-container" ref={containerRef}>
          <Form>
            <Form.Group className="mb-2">
              <Form.Label className="font-weight-bold">Start Date</Form.Label>
              <Form.Control
                type="date"
                value={startDate}
                onChange={(e) => setStartDate(e.target.value)}
              />
            </Form.Group>
            <Form.Group className="mb-2">
              <Form.Label className="font-weight-bold">End Date</Form.Label>
              <Form.Control
                type="date"
                value={endDate}
                onChange={(e) => setEndDate(e.target.value)}
              />
            </Form.Group>
          </Form>
          <div className="d-flex justify-content-end mt-2">
            <Button onClick={handleExport}>Download</Button>
          </div>
        </div>
      )}
    </div>
  );
};
