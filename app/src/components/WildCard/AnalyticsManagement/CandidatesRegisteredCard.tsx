import React, { useEffect } from "react";
import { <PERSON><PERSON> } from "react-bootstrap";
import { useDispatch, useSelector } from "react-redux";
import { RootState } from "@src/redux/reducers";
import { fetchRegisteredCandidates } from "@src/redux/slices/analyticsManagement";
import { downloadFile } from "@src/helper/downloadFile";
import { DateFilterType } from "@src/redux/interfaces";
import Image from "next/image";

export function CandidatesRegisteredCard() {
  const dispatch = useDispatch();
  const {
    rows: candidates,
    count: totalCount,
    filter,
  } = useSelector(
    (state: RootState) => state.analyticsManagement.registeredCandidates,
  );

  const currentDateTime = new Date();

  useEffect(() => {
    callApi(currentDateTime, "week", 1, 10);
  }, []);

  const callApi = async (
    date: Date,
    filter: "week" | "month" | "year",
    page: number,
    limit: number,
  ) => {
    await dispatch<any>(fetchRegisteredCandidates(date, filter, page, limit));
  };

  const handleFilterChange = async (newFilter: DateFilterType) => {
    if (newFilter === filter) return;
    await callApi(currentDateTime, newFilter, 1, 10);
  };

  return (
    <div
      className="card flex-grow-1"
      style={{
        borderRadius: 12,
        boxShadow: "0 6px 15px rgba(0, 0, 0, 0.1)",
        overflow: "hidden",
        backgroundColor: "#fff",
        width: "100%",
      }}>
      {/* Accent */}
      <div
        style={{
          height: 5,
          backgroundColor: "#2ed1a5",
          borderTopLeftRadius: 12,
          borderTopRightRadius: 12,
        }}
      />

      {/* Header */}
      <div
        style={{
          padding: "16px 24px",
          fontWeight: 600,
          fontSize: 18,
          color: "#02263b",
          backgroundColor: "#f9fdfa",
          borderBottom: "1px solid #f0f0f0",
        }}>
        Candidates Registered
      </div>

      {/* Filter Tabs */}
      <div className="card-body pt-3">
        <div className="gradient-toggle-group mb-3">
          {["week", "month", "year"].map((period, index, arr) => {
            const isActive = filter === period;
            const label =
              period === "week"
                ? "This Week"
                : period === "month"
                  ? "Current Month"
                  : "Current Year";

            return (
              <button
                key={period}
                onClick={() => handleFilterChange(period as DateFilterType)}
                className={`gradient-toggle-btn ${isActive ? "active" : ""}`}
                style={{
                  borderTopLeftRadius: index === 0 ? 20 : 0,
                  borderBottomLeftRadius: index === 0 ? 20 : 0,
                  borderTopRightRadius: index === arr.length - 1 ? 20 : 0,
                  borderBottomRightRadius: index === arr.length - 1 ? 20 : 0,
                }}>
                {label}
              </button>
            );
          })}
        </div>

        {/* Summary */}
        <div className="mb-3" style={{ fontSize: 15 }}>
          Resume Reg: <strong>{totalCount}</strong>
        </div>

        {/* Candidates List */}
        {candidates.length > 0 ? (
          <ul className="list-unstyled">
            {candidates.map((candidate) => (
              <li
                key={candidate.id}
                className="d-flex align-items-center justify-content-between mb-3 p-2 border rounded">
                {/* Profile Image */}
                <div className="d-flex align-items-center gap-3">
                  <Image
                    src="/images/auth/undraw_profile.svg"
                    width={48}
                    height={48}
                    alt={candidate.name}
                    className="rounded-circle"
                  />
                  <div>
                    <h6 className="mb-1">{candidate.name}</h6>
                    <small style={{ color: "#555" }}>{candidate.email}</small>
                  </div>
                </div>

                {/* Resume Download */}
                <Button
                  size="sm"
                  variant="outline-primary"
                  onClick={() => downloadFile(candidate.resume_url)}
                  disabled={!candidate.resume_url}>
                  Download Resume
                </Button>
              </li>
            ))}
          </ul>
        ) : (
          <div className="text-center text-muted py-4">No Records Found</div>
        )}
      </div>
    </div>
  );
}
