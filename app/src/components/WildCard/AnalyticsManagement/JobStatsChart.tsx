import React, { useEffect, useState } from "react";
import { Pie<PERSON><PERSON> } from "@mui/x-charts";
import { analyticApi } from "@src/apis/wildcardApis";
import { Skeleton } from "antd";

type PieChartDataType = {
  id: number;
  value: number;
  label: string;
};

type PieChartState = {
  data: PieChartDataType[];
  title: string;
  colors?: string[];
};

export const JobStatsChartCard: React.FC = () => {
  const [loading, setLoading] = useState<boolean>(false);
  const [state, setStats] = useState<PieChartState>({
    data: [{ id: 0, value: 1, label: "No Data" }],
    title: "Candidate Status Distribution Stats",
    colors: [
      "#2ed1a5", // Primary
      "#26a88b", // Primary Hover
      "#02263b", // Secondary
      "#0b1541", // Secondary Dark
      "#f4b400", // Yellow
      "#db4437", // Red
    ],
  });

  useEffect(() => {
    fetchCandidateAnalyticStats();
  }, []);

  const fetchCandidateAnalyticStats = async () => {
    try {
      setLoading(true);
      const { success, data } = await analyticApi.statusDistibutionStats();
      if (success) {
        setStats({
          ...data,
        });
      }
    } catch (error) {
      console.error("Failed to fetch pie chart stats:", error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div
      className="card flex-grow-1"
      style={{
        borderRadius: 12,
        boxShadow: "0 6px 15px rgba(0, 0, 0, 0.1)",
        overflow: "hidden",
        backgroundColor: "#fff",
        width: "100%",
      }}>
      {/* Accent Bar */}
      <div
        style={{
          height: 5,
          backgroundColor: "#2ed1a5",
          borderTopLeftRadius: 12,
          borderTopRightRadius: 12,
        }}
      />

      {/* Card Header */}
      <div
        style={{
          padding: "16px 24px",
          borderBottom: "1px solid #f0f0f0",
          fontWeight: 600,
          fontSize: 18,
          color: "#02263b",
          backgroundColor: "#f9fdfa",
        }}>
        {state.title}
      </div>

      {/* Chart Body */}
      <div style={{ padding: 24, width: "100%" }}>
        {loading ? (
          <Skeleton
            active
            avatar={{
              style: {
                width: 280,
                height: 280,
                aspectRatio: 1,
                borderRadius: "50%",
                margin: "0 auto",
              },
            }}
            paragraph={false}
            title={false}
          />
        ) : (
          <PieChart
            series={[{ data: state.data }]}
            colors={state.colors}
            height={300}
            margin={{ top: 0, bottom: 120, left: 10, right: 10 }}
            slotProps={{
              legend: {
                direction: "row",
                position: { vertical: "bottom", horizontal: "middle" },
                padding: 12,
                itemMarkWidth: 16,
                itemMarkHeight: 16,
                itemGap: 16,
                labelStyle: {
                  fontSize: 14,
                  fill: "#555",
                },
              },
            }}
          />
        )}
      </div>
    </div>
  );
};
