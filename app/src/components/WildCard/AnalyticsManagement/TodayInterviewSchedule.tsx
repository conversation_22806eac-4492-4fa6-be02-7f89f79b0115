import React, { useEffect, useState } from "react";
import { But<PERSON> } from "react-bootstrap";
import { useDispatch } from "react-redux";
import { interviewApi } from "@src/apis/wildcardApis";
import { NewInterviewInterface } from "@src/redux/interfaces";
import { setLoader } from "@src/redux/actions";
import Link from "next/link";
import { APP_ROUTE } from "@src/constants";
import Image from "next/image";

export function TodayInterviewSchedule() {
  const [interviews, setInterviews] = useState<NewInterviewInterface[]>([]);
  const dispatch = useDispatch();

  useEffect(() => {
    const fetchTodayInterviews = async () => {
      dispatch(setLoader(true));
      try {
        const params = {
          page: 1,
          limit: 10,
          filter: "today",
        };
        const { success, data } = await interviewApi.getInterviewsList(params);

        if (success) {
          setInterviews(data.rows || []);
        } else {
          setInterviews([]);
        }
      } catch (error) {
        console.error("Error fetching interviews:", error);
        setInterviews([]);
      } finally {
        dispatch(setLoader(false));
      }
    };

    fetchTodayInterviews();
  }, [dispatch]);

  const hasInterviews = interviews.length > 0;

  // Helper to format date/time
  const formatDateTime = (dateStr: string | undefined) => {
    if (!dateStr) return "N/A";
    const date = new Date(dateStr);
    return date.toLocaleString(undefined, {
      month: "long",
      day: "numeric",
      year: "numeric",
      hour: "numeric",
      minute: "numeric",
      hour12: true,
    });
  };

  return (
    <div
      className="card flex-grow-1"
      style={{
        borderRadius: 12,
        boxShadow: "0 6px 15px rgba(0,0,0,0.1)",
        overflow: "hidden",
        backgroundColor: "#fff",
        width: "100%",
      }}>
      {/* Accent Bar */}
      <div
        style={{
          height: 5,
          backgroundColor: "#2ed1a5",
          borderTopLeftRadius: 12,
          borderTopRightRadius: 12,
        }}
      />

      {/* Header */}
      <div
        style={{
          padding: "16px 24px",
          fontWeight: 600,
          fontSize: 18,
          color: "#02263b",
          backgroundColor: "#f9fdfa",
          borderBottom: "1px solid #f0f0f0",
          display: "flex",
          justifyContent: "space-between",
          alignItems: "center",
        }}>
        <span>Today&apos;s Interview Schedule</span>
        {hasInterviews && (
          <Link href={APP_ROUTE.INTERVIEW_MANAGEMENT} passHref>
            <Button
              size="sm"
              className="btn-primary"
              style={{ fontWeight: 600 }}>
              View All Schedule
            </Button>
          </Link>
        )}
      </div>

      {/* Body */}
      <div className="card-body" style={{ padding: "16px 24px" }}>
        {hasInterviews ? (
          <ul
            className="list-unstyled m-0"
            style={{ maxHeight: 400, overflowY: "auto" }}>
            {interviews.map((interview) => (
              <li
                key={interview.candidate_id}
                className="d-flex align-items-center justify-content-between mb-3 p-3 border rounded"
                style={{ gap: 16 }}>
                {/* Profile */}
                <div
                  style={{
                    width: 70,
                    height: 70,
                    position: "relative",
                    flexShrink: 0,
                  }}>
                  <Image
                    src="/images/auth/undraw_profile.svg"
                    alt="Profile"
                    layout="fill"
                    objectFit="cover"
                    className="rounded-circle"
                  />
                </div>

                {/* Details */}
                <div className="d-flex flex-column flex-grow-1 gap-2">
                  <div
                    className="d-flex justify-content-between align-items-start"
                    style={{ gap: 12 }}>
                    <div>
                      <h6 className="mb-1" style={{ fontWeight: 600 }}>
                        {interview.candidate_name || "N/A"}
                      </h6>
                      <p
                        className="text-muted m-0"
                        style={{ fontSize: 14, wordBreak: "break-word" }}>
                        {interview.candidate_email || "N/A"}
                      </p>
                    </div>
                    <div
                      className="d-flex gap-3 align-items-center"
                      style={{ fontSize: 14 }}>
                      <span
                        className="rounded-pill px-2 py-1"
                        style={{
                          backgroundColor: "#e1f5f1",
                          color: "#2ed1a5",
                          fontWeight: 600,
                        }}>
                        Round: {interview.interview_round || "N/A"}
                      </span>
                      <span style={{ color: "#02263b", fontWeight: 600 }}>
                        {interview.interview_mode_name || "N/A"}
                      </span>
                    </div>
                  </div>

                  <div
                    className="d-flex gap-4 flex-wrap"
                    style={{ fontSize: 14 }}>
                    <div>
                      <strong>Date & Time:</strong>{" "}
                      <span>{formatDateTime(interview.interview_at)}</span>
                    </div>
                    <div>
                      <strong>Interviewer:</strong>{" "}
                      <span>{interview.interviewer_name || "N/A"}</span>
                    </div>
                  </div>
                </div>
              </li>
            ))}
          </ul>
        ) : (
          <p className="text-center text-muted py-4">
            No interviews scheduled for today.
          </p>
        )}
      </div>
    </div>
  );
}
