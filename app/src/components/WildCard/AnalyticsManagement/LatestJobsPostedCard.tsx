import React, { useCallback, useEffect, useState } from "react";
import Slider from "react-slick";
import { useAppDispatch } from "@src/redux/store";
import WorkIcon from "@mui/icons-material/Work";
import { OpportunityInterface } from "@src/redux/interfaces";
import Link from "next/link";
import { APP_ROUTE } from "@src/constants";
import { opportunityApi } from "@src/apis/wildcardApis";

type LastestJobsPostedCardProps = {
  settings: {
    dots: boolean;
    infinite: boolean;
    speed: number;
    slidesToShow: number;
    slidesToScroll: number;
  };
  addNewButton?: React.ReactNode;
};

export function LastestJobsPostedCard({
  settings,
}: LastestJobsPostedCardProps) {
  const [loader, setLoader] = useState<boolean>(false);
  const [rows, setRows] = useState<OpportunityInterface[]>([]);
  const dispatch = useAppDispatch();

  const fetchData = useCallback(async () => {
    try {
      setLoader(true);
      const { success, ...response } =
        await opportunityApi.getOpportunitiesList({
          page: 1,
          limit: 10,
        });
      if (success) {
        const { rows } = response.data;
        setRows(rows);
      }
      setLoader(false);
    } catch (err) {
      console.error("Error fetching departments data:", err);
    } finally {
      setLoader(false);
    }
  }, [dispatch]);

  useEffect(() => {
    fetchData();
  }, []);

  const hasRows = rows && rows.length > 0;

  return (
    <div className="card jobs-card">
      {/* Accent Bar */}
      <div className="accent-bar" />

      {/* Header */}
      <div className="card-header d-flex align-items-center justify-content-between">
        <h5 className="heading-clr mb-0">Latest Jobs Posted</h5>
        <Link
          href={APP_ROUTE.OPPORTUNITY_MANAGEMENT}
          aria-disabled={!hasRows}
          className={`btn btn-primary ${!hasRows ? "disabled" : ""}`}>
          View All Jobs
        </Link>
      </div>

      <div className="card-body">
        {loader ? (
          <div className="loader-wrapper">
            <div className="spinner-border text-primary" role="status">
              <span className="visually-hidden">Loading...</span>
            </div>
          </div>
        ) : hasRows ? (
          <Slider {...settings}>
            {rows.map((opportunity: OpportunityInterface) => (
              <div key={opportunity.id}>
                <div className="job-card p-3 rounded shadow-sm bg-white">
                  <h5 className="job-title mb-2">
                    {opportunity.designation || "Designation not available"}
                  </h5>
                  <p className="job-description mb-3 text-muted">
                    {opportunity.description || "No description available"}
                  </p>
                  <ul className="job-details list-unstyled d-flex flex-wrap gap-3 mb-0">
                    <li>
                      Experience:{" "}
                      <strong>{opportunity.experience || "N/A"} yrs</strong>
                    </li>
                    <li>
                      Salary:{" "}
                      <strong>
                        {opportunity.salary
                          ? `INR ${opportunity.salary}`
                          : "N/A"}
                      </strong>
                    </li>
                    <li className="job-type d-flex align-items-center gap-1">
                      <WorkIcon
                        style={{ fill: "var(--theme-primary-color)" }}
                        fontSize="small"
                      />
                      {opportunity.job_type || "Full-time"}
                    </li>
                  </ul>
                </div>
              </div>
            ))}
          </Slider>
        ) : (
          <div className="no-records text-center py-5">No Records Found</div>
        )}
      </div>
    </div>
  );
}
