import React from "react";
import { Pagination } from "antd";
import { useSelector } from "react-redux";
import { RootState } from "@src/redux/reducers";
import { CandidateInterface } from "@src/redux/interfaces";
import { CandidateCardView } from "./CandidateDetail";

type WildcardCandidateListProps = {
  fetchData: (page: number, limit: number) => void;
  subdomain: string;
  sendEmail: (cadidate: CandidateInterface) => void;
  openCandidateDeleteModal: (cadidate: CandidateInterface) => void;
  openCandidateWhitelistModal: (cadidate: CandidateInterface) => void;
  currentPagePermissions: string[];
  currentEmployeeRole: string;
  currentEmployeeId: number;
  addNewButton?: React.ReactNode;
};

export function WildcardCandidateList({
  fetchData,
  addNewButton,
  ...props
}: WildcardCandidateListProps) {
  const { rows, count, currentPage, limit } = useSelector(
    (state: RootState) => state.candidate,
  );

  // Handle page change for pagination
  const handlePageChange = async (page: number) => {
    await fetchData(page, limit);
  };

  const hasRows = rows && rows.length > 0;
  return (
    <>
      <div className="candidate-list-wrap p-3">
        <div className="row row-gap-3 candidate-row-list">
          {hasRows ? (
            rows.map((candidate: CandidateInterface, index: number) => {
              return (
                <div
                  className="col-12 col-md-12 col-xl-4 candidate-column"
                  key={candidate.id}>
                  <CandidateCardView
                    candidate={candidate}
                    {...props}
                    showShortist={true}
                  />
                </div>
              );
            })
          ) : (
            <>
              <div className="d-flex flex-column align-items-center justify-content-center py-5 px-3 bg-light text-center">
                {/* SVG Icon (Users) */}
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width={64}
                  height={64}
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                  strokeWidth={2}
                  className="mb-3 text-secondary">
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    d="M17 20h5v-2a4 4 0 00-3-3.87M9 20H4v-2a4 4 0 013-3.87m3-4a4 4 0 110-8 4 4 0 010 8z"
                  />
                </svg>
                {/* Heading */}
                <h5 className="fw-bold text-dark">No Candidates Found</h5>
                {/* Description + Optional Button */}
                {addNewButton && (
                  <div className="text-center">
                    <p className="text-muted mb-3">
                      You haven&apos;t added any candidates yet. Click below to
                      add one.
                    </p>
                    <br />
                    {addNewButton}
                  </div>
                )}
              </div>
            </>
          )}
        </div>
      </div>
      <Pagination
        className="mt-4"
        current={currentPage}
        total={count}
        pageSize={limit}
        hideOnSinglePage
        onChange={handlePageChange}
      />
    </>
  );
}
