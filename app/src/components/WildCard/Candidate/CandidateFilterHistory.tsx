import React, { useEffect, useState } from "react";
import { KeyPairInterface } from "@src/redux/interfaces";
import { useAppDispatch } from "@src/redux/store";
// import { Form, Button } from "antd";
import { candidateApi } from "@src/apis/wildcardApis";
import { CandidateSearchFilters } from "@src/redux/interfaces";
import { Badge, Card, Button } from "react-bootstrap";
import CheckCircle from "@mui/icons-material/CheckCircle";

type CandidateFilterHistoryProps = {
  filters: KeyPairInterface;
  onConfirm: (state: KeyPairInterface) => void;
  close: Function;
};

export const CandidateFilterHistory = ({
  filters,
  close,
  onConfirm,
}: CandidateFilterHistoryProps) => {
  const dispatch = useAppDispatch();
  const [state, setState] = useState<KeyPairInterface>(filters);
  const [searchHistory, setSearchHistory] = useState<CandidateSearchFilters[]>(
    [],
  );
  const [selectedFilter, setSelectedFilter] = useState<KeyPairInterface | null>(
    null,
  );

  const handleSubmit = () => {
    if (selectedFilter) {
      onConfirm(selectedFilter);
    }
    close();
  };

  const fetchFilters = async () => {
    const response = await candidateApi.getCandidateSavedFilters({});
    setSearchHistory(response.data);
  };

  const applyFilter = (filter: CandidateSearchFilters) => {
    if (filter.resume_uploaded_after === "None") {
      delete (filter as any).resume_uploaded_after;
    }
    setSelectedFilter(filter);
  };

  useEffect(() => {
    fetchFilters();
  }, []);

  return (
    <>
      {searchHistory && searchHistory.length > 0 ? (
        searchHistory.map((val: CandidateSearchFilters, index: number) => (
          <Card
            className="history-card shadow-none border-0 overflow-visible"
            key={index}>
            <Card.Body
              className={`box p-2 border rounded-2 w-100 ${selectedFilter === val ? "active" : null}`}>
              <div
                onClick={() => applyFilter(val)}
                className="w-full d-flex flex-wrap gap-2">
                {selectedFilter === val ? (
                  <CheckCircle
                    className="material-icons"
                    style={{ color: "#0a58ca" }}
                  />
                ) : (
                  <CheckCircle className="material-icons" />
                )}

                <div className="d-flex options align-items-center gap-2">
                  <span className="text-light-clr">Opportunity:</span>
                  <Badge className="heading-clr">{val.opportunity}</Badge>
                </div>
                <div className="d-flex options align-items-center gap-2">
                  <span className="text-light-clr">Qualification:</span>
                  <Badge className="heading-clr">{val.qualification}</Badge>
                </div>
                <div className="d-flex options align-items-center gap-2">
                  <span className="text-light-clr">Exp:</span>
                  <Badge className="heading-clr">
                    {val.experience > 0 ? val.experience : 0} yrs max
                  </Badge>
                </div>
                <div className="d-flex options align-items-center gap-2">
                  <span className="text-light-clr">Status:</span>
                  <Badge className="heading-clr">{val.status}</Badge>
                </div>
                <div className="d-flex options align-items-center gap-2">
                  <span className="text-light-clr">Resume Uploaded After:</span>
                  <Badge className="heading-clr">
                    {val.resume_uploaded_after
                      ? val.resume_uploaded_after
                      : "None"}
                  </Badge>
                </div>
              </div>
            </Card.Body>
          </Card>
        ))
      ) : (
        <h6>No History Available</h6>
      )}

      <div className="ant-modal-footer">
        <Button className=" mr-1" onClick={handleSubmit}>
          Apply Filters
        </Button>
        {close && (
          <Button
            key="cancel"
            className="btn cancel-btn"
            onClick={() => close()}>
            Cancel
          </Button>
        )}
      </div>
    </>
  );
};
