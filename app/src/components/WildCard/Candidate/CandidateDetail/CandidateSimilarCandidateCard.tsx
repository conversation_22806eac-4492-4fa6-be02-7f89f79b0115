import { useState, useCallback, useEffect } from "react";
import { Card } from "react-bootstrap";
import { useAppDispatch } from "@src/redux/store";
import { setLoader } from "@src/redux/actions";
import { CandidateBasicInterface } from "@src/redux/interfaces";
import { candidateApi } from "@src/apis/wildcardApis";
import { APP_ROUTE } from "@src/constants";
import { encrypt } from "@src/helper/encryption";
import Link from "next/link";
import Image from "next/image";

interface SimilarCandidateCardProps {
  designation: string;
  candidateId: number;
}

export const SimilarCandidateCard: React.FC<SimilarCandidateCardProps> = ({
  designation,
  candidateId,
}) => {
  const dispatch = useAppDispatch();
  const [similarCandidates, setSimilarCandidates] = useState<
    CandidateBasicInterface[]
  >([]);

  const fetchSimilarCandidates = useCallback(async () => {
    if (!candidateId) {
      console.error("Candidate ID is required");
      return;
    }

    dispatch(setLoader(true));
    try {
      const { success, data } = await candidateApi.getSimilarCandidate(
        candidateId,
        {
          designation,
        },
      );

      if (success) {
        setSimilarCandidates(data?.rows || []);
      } else {
        setSimilarCandidates([]);
      }
    } catch (error) {
      console.error("Error fetching similar candidates:", error);
      setSimilarCandidates([]);
    } finally {
      dispatch(setLoader(false));
    }
  }, [designation, candidateId, dispatch]);

  useEffect(() => {
    if (designation && candidateId) {
      fetchSimilarCandidates();
    }
  }, [designation, candidateId, fetchSimilarCandidates]);

  const hasRows = similarCandidates.length > 0;

  return (
    <>
      <Card>
        <div className="p-3">
          <div className="d-flex justify-content-between border-bottom pb-3 mb-3">
            <h5
              className="fw-semibold heading-clr mb-0 card-title"
              style={{ lineHeight: "normal" }}>
              Similar Candidates
            </h5>
            {hasRows && (
              <Link
                href={APP_ROUTE.CANDIDATE_MANAGEMENT}
                className="primary-clr text-decoration-underline">
                View All
              </Link>
            )}
          </div>
          {hasRows ? (
            similarCandidates.map((candidate, index) => (
              <div
                key={index}
                className="same-candidate-wrap d-flex gap-3 mb-3">
                <Image
                  className=""
                  src="/images/auth/undraw_profile.svg"
                  alt="image"
                  width={40}
                  height={40}
                />
                <div className="">
                  <Link
                    className="text-decoration-none"
                    href={`${APP_ROUTE.CANDIDATE_MANAGEMENT}/${encrypt(candidate.id.toString())}`}>
                    <p className="fw-medium heading-clr mb-0">
                      {candidate.name}
                    </p>
                    <span className="text-clr">{candidate.designation}</span>
                  </Link>
                </div>
              </div>
            ))
          ) : (
            <div className="same-candidate-wrap d-flex gap-3 mb-3">
              <p className="fw-medium heading-clr mb-0">No Records Found</p>
            </div>
          )}
        </div>
      </Card>
    </>
  );
};
