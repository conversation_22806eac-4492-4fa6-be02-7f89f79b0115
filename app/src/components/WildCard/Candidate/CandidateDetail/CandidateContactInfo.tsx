import React from "react";
import { CandidateInterface } from "@src/redux/interfaces";
import { Card, Button, ListGroup } from "react-bootstrap";
import Call from "@mui/icons-material/Call";
import Mail from "@mui/icons-material/Mail";
import Image from "next/image";
import Link from "next/link";
import DialogComponents from "@src/components/DialogComponents";
import { openDialog } from "@src/redux/actions";
import { useAppDispatch } from "@src/redux/store";
import { useSelector } from "react-redux";
import { RootState } from "@src/redux/reducers";

interface CandidateContactInfoProps {
  candidate: CandidateInterface;
}

export const CandidateContactInfo: React.FC<CandidateContactInfoProps> = ({
  candidate,
}) => {
  const dispatch = useAppDispatch();

  const SendEmailModal = () => {
    dispatch(
      openDialog({
        config: DialogComponents.CUSTOM_EMAIL_MODAL,
        options: {},
      }),
    );
  };
  const currentEmployee = useSelector(
    (state: RootState) => state.auth.employee,
  );
  const currentEmployeeRole: string = currentEmployee?.employee_role ?? "";
  const currentEmployeeId: number = currentEmployee?.id ?? 0;
  let interviewerOrAdminCandidate =
    (currentEmployeeRole == "Interviewer" &&
      currentEmployeeId == candidate.created_by_id) ||
    currentEmployeeRole != "Interviewer";

  return (
    <Card className="p-3 mb-4">
      <Card.Body className="p-0">
        <Card.Title className="border-bottom pb-3 fw-semibold heading-clr">
          Contact Details
        </Card.Title>
        <div className="casndi-contact">
          <span>
            <Call className="material-icons" />
          </span>
          <div className="contact-data">
            <p className="mb-0 heading-clr fw-medium">{candidate?.contact}</p>
            <p className="mb-0 text-light-clr">Phone Number</p>
          </div>
        </div>
        <div className="casndi-contact">
          <span>
            <Mail className="material-icons" />
          </span>

          <div className="email-data w-100">
            <p className="mb-0 heading-clr fw-medium">{candidate?.email}</p>
            <p className="mb-0 text-light-clr">Email Address</p>
          </div>
        </div>
        {interviewerOrAdminCandidate && (
          <Button
            variant="primary"
            onClick={() => SendEmailModal()}
            className="w-100">
            Send Email
          </Button>
        )}
        <div className="candi-other-links">
          <div className="justify-content-center d-flex gap-2 align-items-center">
            {candidate.website || candidate.github || candidate.linkedin ? (
              <label className="text-light-clr">Social-Links:</label>
            ) : null}
            <ListGroup horizontal className="list-inline mb-0">
              {candidate.website ? (
                <ListGroup.Item className="list-inline-item">
                  <Link href={candidate.website} target="_blank">
                    <Image
                      src="/images/website.svg"
                      width={30}
                      height={20}
                      alt="icon"
                    />
                  </Link>
                </ListGroup.Item>
              ) : null}
              {candidate.github ? (
                <ListGroup.Item className="list-inline-item">
                  <Link href={candidate.github} target="_blank">
                    <Image
                      src="/images/icons/github.svg"
                      width={30}
                      height={20}
                      alt="icon"
                    />
                  </Link>
                </ListGroup.Item>
              ) : null}
              {candidate.linkedin ? (
                <ListGroup.Item className="list-inline-item">
                  <Link href={candidate.linkedin} target="_blank">
                    <Image
                      src="/images/icons/linkedin.svg"
                      width={30}
                      height={20}
                      alt="icon"
                    />
                  </Link>
                </ListGroup.Item>
              ) : null}
            </ListGroup>
          </div>
        </div>
      </Card.Body>
    </Card>
  );
};
