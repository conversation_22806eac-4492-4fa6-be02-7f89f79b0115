import React, { useEffect, useState } from "react";
import { dashboardApi } from "@src/apis/wildcardApis";
import { Pie<PERSON><PERSON> } from "@mui/x-charts";
import { Skeleton } from "antd";

type PieChartDataType = { id: number; value: number; label: string };

type StatsState = {
  pie_stats: {
    data: Array<PieChartDataType>;
    title: string;
    colors: undefined | string[];
  };
  stats_type: string;
};

const defaultStats: StatsState = {
  pie_stats: { data: [], title: "", colors: undefined },
  stats_type: "monthly",
};

export const WildcardApiUsageStats = () => {
  // State for current labels and values
  const [loading, setLoading] = useState<boolean>(false);
  const [state, setStats] = useState<StatsState>(defaultStats);

  useEffect(() => {
    getApiKeyUsageStats();
  }, []);

  const getApiKeyUsageStats = async () => {
    setLoading(true);
    const { success, data } = await dashboardApi.apiKeyUsageStats();
    if (success) {
      setStats((prev) => ({ ...prev, ...data }));
    }
    setLoading(false);
  };

  return (
    <div className="card p-3">
      <div className="card-header common-heading p-0 mb-3 border-0 bg-white">
        <h4 className="heading-clr text-center">{state.pie_stats.title}</h4>
      </div>
      {loading ? (
        <Skeleton
          active
          avatar={{
            style: {
              width: 250,
              margin: 50,
              height: 250,
              aspectRatio: 1,
              borderRadius: "50%",
            }, // Customize size and shape
          }}
          paragraph={false}
          title={false}
          style={{
            width: "100%",
            height: 250,
            maxWidth: 250,
            borderRadius: "50%",
          }}
        />
      ) : (
        <PieChart
          series={[{ data: state.pie_stats.data }]}
          colors={state.pie_stats.colors}
          className="pie-chart"
          height={300}
          margin={{ top: 0, bottom: 50, left: 10, right: 10 }}
          slotProps={{
            legend: {
              direction: "row",
              position: { vertical: "bottom", horizontal: "middle" },
              padding: 0,
            },
          }}
        />
      )}
    </div>
  );
};
