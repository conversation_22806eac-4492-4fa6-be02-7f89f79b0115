import React, { useEffect, useState } from "react";
import { Button, Skeleton } from "antd";
import { candidateA<PERSON> } from "@src/apis/wildcardApis";
import Link from "next/link";
import { APP_ROUTE } from "@src/constants";

interface CandidateStats {
  candidate_name: string;
  candidate_email: string;
  candidate_created_at: string;
  opportunity_title: string;
  interview_status: string;
}

export const WildcardCandidateStats = () => {
  const [rows, setRows] = useState<CandidateStats[]>([]);
  const [loading, setLoading] = useState<boolean>(false);

  useEffect(() => {
    getStats();
  }, []);

  const getStats = async () => {
    setLoading(true);
    const { success, data } = await candidateApi.getReports();
    if (success) {
      setRows(data.rows);
    }
    setLoading(false);
  };

  return (
    <div className="card p-4 m-0">
      <div className="card-header common-heading p-0 mb-3 border-0 bg-white d-flex gap-3 align-items-center">
        <div className="w-100">
          <h4 className="mb-2 heading-clr">Candidate Statistics</h4>
          <p className="mb-0 text-light-clr">
            Check the number of candidates who have applied for the job posting.
            You can view their names, email, job titles for which they have
            applied, and interview status.
            {/* Provides a comprehensive Statistics of candidates&apos; profiles,
          including their application and interview statuses. */}
          </p>
        </div>
        {rows.length > 0 && (
          <Link href={APP_ROUTE.CANDIDATE_MANAGEMENT}>
            <Button className="btn btn-theme ms-auto">View All</Button>
          </Link>
        )}
      </div>
      <div className="table-responsive">
        <table className="table table-hover interview">
          <thead>
            <tr>
              <th scope="col">Candidate Name</th>
              <th scope="col">Candidate Email</th>
              <th scope="col">Created Date</th>
              <th scope="col">Job Title</th>
              <th scope="col">Interview Status</th>
            </tr>
          </thead>
          <tbody>
            {loading ? (
              <td colSpan={5}>
                <Skeleton active paragraph={{ rows: 6 }} />
              </td>
            ) : rows.length > 0 ? (
              rows.map((val: any, index: number) => (
                <tr key={index}>
                  <th>{val.candidate_name}</th>
                  <td>{val.candidate_email}</td>
                  <td>{val.candidate_created_at.strftime("%B %d, %Y")}</td>
                  <td>{val.opportunity_title}</td>
                  <td>
                    <span
                      className={`interview-status ${val.interview_status.toLowerCase()}`}>
                      {val.interview_status}
                    </span>
                  </td>
                </tr>
              ))
            ) : (
              <td colSpan={5}>
                <p className="text-center m-0">No Stats Found</p>
              </td>
            )}
          </tbody>
        </table>
      </div>
    </div>
  );
};
