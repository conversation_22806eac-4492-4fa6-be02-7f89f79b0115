import React from "react";
import { useState } from "react";
import { Pagination, Switch, Tooltip } from "antd";
import { useSelector } from "react-redux";
import { RootState } from "@src/redux/reducers";
import { useAppDispatch } from "@src/redux/store";
import { EmployeeInterface, KeyPairInterface } from "@src/redux/interfaces";
import {
  openDialog,
  setLoader,
  updateEmployeeDetail,
} from "@src/redux/actions";
import DialogComponents from "@src/components/DialogComponents";
import { employeeManagementApi } from "@src/apis/wildcardApis";
import Dropdown from "react-bootstrap/Dropdown";
import MoreVertIcon from "@mui/icons-material/MoreVert";
import flashMessage from "@src/components/FlashMessage";
import Image from "next/image";
import Collapse from "react-bootstrap/Collapse";

type WildcardEmployeeListProps = {
  fetchData: (page: number, limit: number) => void;
  subdomain: string;
  currentPagePermissions: string[];
  pageDetail: KeyPairInterface;
  addNewButton?: React.ReactNode;
};

export function WildcardEmployeeList({
  fetchData,
  currentPagePermissions,
  pageDetail,
  addNewButton,
}: WildcardEmployeeListProps) {
  const dispatch = useAppDispatch();
  const [openStates, setOpenStates] = useState<Record<number, boolean>>({});

  const currentEmployee = useSelector(
    (state: RootState) => state.auth.employee,
  );
  const { rows, count, currentPage, limit } = useSelector(
    (state: RootState) => state.employee,
  );

  // Handle page change for pagination
  const handlePageChange = async (page: number) => {
    await fetchData(page, limit);
  };

  // Update employee status (active/inactive)
  const updateEmployeeStatus = async (employee: EmployeeInterface) => {
    await dispatch(setLoader(true));
    const payload = { status: employee.status == 1 ? 0 : 1 };
    const { success, ...response } =
      await employeeManagementApi.updateEmployeeStatus(employee.id, payload);
    if (success) {
      reflectEmployeeDetail(employee, response.data);
    }
    await dispatch(setLoader(false));
    flashMessage(response.message, success ? "success" : "error");
  };

  // Reflect updated employee details
  const reflectEmployeeDetail = async (
    employee: EmployeeInterface,
    data: EmployeeInterface,
  ) => {
    dispatch(
      updateEmployeeDetail({
        id: employee.id,
        data: { ...employee, ...data },
      }),
    );
  };

  // Open modal to confirm status change
  const openStatusChangeModal = (employee: EmployeeInterface) => {
    dispatch(
      openDialog({
        config: DialogComponents.CONFIRMATION_MODAL,
        options: {
          title: `${pageDetail.singular_name ?? "employee"} Status Update Confirmation`,
          message: (
            <div className="mt-2 mb-2">
              Are you sure you want to update the status of the{" "}
              {pageDetail.singular_name ?? "employee"}? This action may affect
              your current running functionality.
            </div>
          ),
          onConfirm: () => updateEmployeeStatus(employee),
        },
      }),
    );
  };

  // Open modal to edit employee details
  const openEditModal = (employee: EmployeeInterface) => {
    dispatch(
      openDialog({
        config: DialogComponents.EDIT_EMPLOYEE_MODAL,
        options: {
          title: `Edit ${pageDetail.singular_name ?? "Employee"} Detail`,
          employee: employee,
          onEmployeeUpdate: reflectEmployeeDetail,
        },
      }),
    );
  };

  const deleteSelectedEmployee = async (employee: EmployeeInterface) => {
    await dispatch(setLoader(true));
    const { success, message } = await employeeManagementApi.deleteEmployee(
      employee.id,
    );
    await dispatch(setLoader(false));
    if (success) {
      let page = currentPage;
      if (rows.length == 1) {
        page = page - 1;
        page = page > 1 ? page : 1;
      }
      await fetchData(page, limit);
    }
    flashMessage(message, success ? "success" : "error");
  };

  const fetchAndCheckStaffDeleteable = async (employee: EmployeeInterface) => {
    await dispatch(setLoader(true));
    const { success, ...response } =
      await employeeManagementApi.checkEmployeeDependency(employee.id);
    if (success) {
      const { has_dependency, message } = response.data;
      if (has_dependency) {
        dispatch(
          openDialog({
            config: DialogComponents.CONFIRMATION_MODAL,
            options: {
              title: `Delete ${pageDetail.singular_name ?? "Employee"}`,
              message: (
                <div
                  className="mt-2 mb-2"
                  dangerouslySetInnerHTML={{ __html: message }}
                />
              ),
            },
          }),
        );
      } else {
        openDeleteConfirmationModal(employee);
      }
    } else {
      flashMessage(response.message, "error");
    }
    await dispatch(setLoader(false));
  };

  // Open modal to delete employee
  const openDeleteConfirmationModal = (employee: EmployeeInterface) => {
    dispatch(
      openDialog({
        config: DialogComponents.CONFIRMATION_MODAL,
        options: {
          title: `Delete ${pageDetail.singular_name ?? "Employee"}`,
          message: (
            <div className="mt-2 mb-2">
              Are you certain you wish to delete the record for{" "}
              {employee.first_name} ({employee.email})? This action will
              permanently remove their details from the system.?
            </div>
          ),
          onConfirm: () => deleteSelectedEmployee(employee),
        },
      }),
    );
  };

  const hasRows = rows && rows.length > 0;
  const hasEditPermission = currentPagePermissions.includes("edit");
  const hasDeletePermission = currentPagePermissions.includes("delete");
  const toggleCard = (id: number) => {
    setOpenStates((prev) => ({
      ...prev,
      [id]: !prev[id], // Toggle the open state for the specific card
    }));
  };
  return (
    <>
      <div className="candidate-list-wrap p-3 staffManagement">
        <div className="row row-gap-4">
          {hasRows ? (
            rows.map((employee: EmployeeInterface) => (
              <div
                key={employee.id}
                className="col-md-12 col-lg-6 col-xl-4 col-sm-12">
                <div className="candidate-list-card bg-white border rounded-3 overflow-hidden">
                  <div className="p-3 pb-0">
                    <div className="d-flex gap-3 align-items-start position-relative">
                      <span className="avatar-box">
                        <Image
                          src={"/images/auth/undraw_profile.svg"}
                          className="img-profile rounded-circle"
                          width={80}
                          height={80}
                          alt=""
                        />
                      </span>
                      <div className="candidate-des d-flex gap-3 flex-wrap flex-lg-nowrap w-100">
                        <div className="brief w-100">
                          <div className="d-flex gap-3 mb-1 align-items-center">
                            <h4 className="heading-clr m-0">
                              {[employee.first_name, employee.last_name].join(
                                " ",
                              )}
                            </h4>

                            {(hasEditPermission || hasDeletePermission) && (
                              <div className="employee-action no-arrow ms-auto">
                                <Dropdown>
                                  <Dropdown.Toggle
                                    id={`employee-dropdown-${employee.id}}`}
                                    className="text-decoration-none"
                                    as="span"
                                    type="button"
                                    role="button"
                                    aria-haspopup="true"
                                    aria-expanded="false">
                                    <span className="text-black small">
                                      <MoreVertIcon />
                                    </span>
                                  </Dropdown.Toggle>

                                  <Dropdown.Menu
                                    className="dropdown-menu-right shadow animated--grow-in"
                                    aria-labelledby={`employee-dropdown-${employee.id}}`}>
                                    {hasEditPermission &&
                                      (employee.id == currentEmployee?.id ? (
                                        <>
                                          <Tooltip
                                            title={
                                              "You cannot edit your own details."
                                            }>
                                            <Dropdown.Item
                                              eventKey="1"
                                              role="button">
                                              Edit Detail
                                            </Dropdown.Item>
                                          </Tooltip>
                                        </>
                                      ) : (
                                        <Dropdown.Item
                                          eventKey="1"
                                          role="button"
                                          onClick={() =>
                                            openEditModal(employee)
                                          }>
                                          Edit Detail
                                        </Dropdown.Item>
                                      ))}
                                    {hasDeletePermission &&
                                      (employee.id == currentEmployee?.id ? (
                                        <Tooltip
                                          title={
                                            "You cannot delet your own account."
                                          }>
                                          <Dropdown.Item
                                            eventKey="1"
                                            role="button">
                                            Delete
                                          </Dropdown.Item>
                                        </Tooltip>
                                      ) : (
                                        <Dropdown.Item
                                          eventKey="2"
                                          role="button"
                                          onClick={() =>
                                            fetchAndCheckStaffDeleteable(
                                              employee,
                                            )
                                          }>
                                          Delete
                                        </Dropdown.Item>
                                      ))}
                                  </Dropdown.Menu>
                                </Dropdown>
                              </div>
                            )}
                          </div>
                          <p className="text-clr m-0">{employee.email}</p>

                          <div className="d-flex gap-3 mt-2 align-items-center">
                            <span
                              className="d-block heading-clr m-0"
                              style={{ fontSize: "14px" }}>
                              {employee.employee_role_name}
                            </span>
                            <span
                              className={`badge ms-auto ${
                                employee.status === 1 ? "active" : "inactive"
                              }`}>
                              {employee.status === 1 ? "Active" : "Inactive"}
                            </span>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                  <Collapse in={openStates[employee.id]}>
                    <div
                      id={`example-collapse-text-${employee.id}`}
                      className="collapse-staff">
                      <ul>
                        <li>
                          Contact Number{" "}
                          <div>
                            <b>{employee.contact_number ?? "N/A"}</b>
                          </div>
                        </li>
                        <li>
                          Created by{" "}
                          <div>
                            <b>{employee.created_by_name}</b>
                          </div>
                        </li>
                        <li>
                          Date & Time{" "}
                          <div>
                            <b>
                              {employee.created_at?.strftime(
                                "%B %d, %Y %I:%M %p",
                              ) ?? "N/A"}
                            </b>
                          </div>
                        </li>
                        <li>
                          Status
                          <div className="d-flex gap-2 align-items-center">
                            {employee.id === currentEmployee?.id ? (
                              <Tooltip
                                title={"You cannot change your own status."}>
                                <Switch
                                  value={employee.status === 1}
                                  className="switch-theme"
                                  disabled
                                />
                              </Tooltip>
                            ) : (
                              <Switch
                                value={employee.status === 1}
                                className="switch-theme"
                                onChange={() => openStatusChangeModal(employee)}
                              />
                            )}
                            <b>
                              {employee.status === 1 ? "Active" : "Inactive"}
                            </b>
                          </div>
                        </li>
                      </ul>
                    </div>
                  </Collapse>
                  <div className="d-flex mt-auto gap-3 flex-xl-wrap flex-xxl-nowrap">
                    <a
                      type="button"
                      className="btn btn-light w-100 rounded-0"
                      onClick={() => toggleCard(employee.id)}
                      aria-controls={`example-collapse-text-${employee.id}`}
                      aria-expanded={openStates[employee.id] || false}>
                      {openStates[employee.id] ? "View Less" : "View More"}
                    </a>
                  </div>
                </div>
              </div>
            ))
          ) : (
            <>
              <div className="d-flex flex-column align-items-center justify-content-center py-5 px-3 text-center">
                {/* SVG Icon */}
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width={64}
                  height={64}
                  fill="currentColor"
                  className="bi bi-person-plus mb-3 text-secondary"
                  viewBox="0 0 16 16">
                  <path d="M8 8a3 3 0 1 0 0-6 3 3 0 0 0 0 6z" />
                  <path d="M8 9a5 5 0 0 0-5 5v.5a.5.5 0 0 0 .5.5h5.707a6.47 6.47 0 0 1-.574-1H3.5v-.5a4 4 0 0 1 8 0c0 .327-.04.644-.116.947a5.48 5.48 0 0 1 1.03.416c.056-.24.086-.49.086-.763a5 5 0 0 0-5-5z" />
                  <path
                    fillRule="evenodd"
                    d="M13.5 5a.5.5 0 0 1 .5.5V7h1.5a.5.5 0 0 1 0 1H14v1.5a.5.5 0 0 1-1 0V8h-1.5a.5.5 0 0 1 0-1H13V5.5a.5.5 0 0 1 .5-.5z"
                  />
                </svg>

                {/* Heading */}
                <h5 className="fw-bold text-dark">No Staff Found</h5>

                {/* Description */}

                {addNewButton && (
                  <div className="text-center">
                    <p className="text-muted mb-3">
                      You haven&apos;t added any staff members yet. Click below
                      to get started.
                    </p>

                    {addNewButton}
                  </div>
                )}
              </div>
            </>
          )}
        </div>
        <Pagination
          className="mt-4"
          current={currentPage}
          total={count}
          pageSize={limit}
          hideOnSinglePage
          onChange={handlePageChange}
        />
      </div>
    </>
  );
}
