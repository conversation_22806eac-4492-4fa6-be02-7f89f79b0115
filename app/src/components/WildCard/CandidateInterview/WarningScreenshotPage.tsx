import DialogComponents from "@src/components/DialogComponents";
import { openDialog } from "@src/redux/actions";
import { InterviewWarningInterface } from "@src/redux/interfaces";
import { useAppDispatch } from "@src/redux/store";
import Image from "next/image";
import React from "react";
import { Row, Col } from "react-bootstrap";

type WarningScreenshotPageType = {
  interviewWarnings: InterviewWarningInterface[];
};

export const WarningScreenshotPage: React.FC<WarningScreenshotPageType> = ({
  interviewWarnings,
}) => {
  const dispatch = useAppDispatch();

  const onViewImage = (warning: InterviewWarningInterface) => {
    const message = (
      <>
        <h6 className="warning-image-text mb-2">
          <strong>Warning Message: </strong> {warning.warning_text}
        </h6>
        <div className="d-flex justify-content-center">
          <div
            className="position-relative w-100 mx-auto"
            style={{ maxWidth: "1050px" }}>
            <Image
              src={warning.screenshot_url}
              alt="Employee Signature"
              layout="responsive"
              width={100}
              height={100}
              className="w-100 rounded"
              style={{ height: "auto" }}
            />
          </div>
        </div>
      </>
    );

    dispatch(
      openDialog({
        config: DialogComponents.SUCCESS_MODAL,
        options: {
          title: `Warning Image`,
          message: message,
          closeTitle: "Close",
          width: "70%",
        },
      }),
    );
  };

  return (
    <>
      <Row className="g-4">
        {/* Loop through each warning and screenshot */}
        {interviewWarnings.map((warning, index) => (
          <Col xs={12} sm={6} md={4} lg={3} key={index}>
            <div
              className="warning-card position-relative cursor-pointer"
              onClick={() => onViewImage(warning)}>
              <Image
                src={warning.screenshot_url}
                alt="Warning Screenshot"
                layout="responsive"
                width={100}
                height={100}
                className="w-100 rounded"
              />
              <div className="warning-text">
                <p>{warning.warning_text}</p>
              </div>
            </div>
          </Col>
        ))}
      </Row>
    </>
  );
};
