import React from "react";
import Form from "react-bootstrap/Form";

type CandidateAnswerSheetType = {
  answerSheet: any[];
  scheduleInterview: any;
};

export const CandidateAnswerSheet: React.FC<CandidateAnswerSheetType> = ({
  answerSheet,
}) => {
  function AnswerStatusIcon(isCorrect: boolean) {
    return <span className="ml-2">{isCorrect ? "✅" : "❌"}</span>;
  }

  return (
    <div className="schedule-interview-review">
      <h5 className="mb-4">Answer Sheet</h5>
      {answerSheet.map((answer: any, index: number) => {
        return (
          <div key={`answer_${answer.id}`} className="review-div mb-2">
            <Form.Group className={`m-0 group-relative`}>
              <Form.Label>
                <pre className="interview-question">
                  <strong>{index + 1}</strong> {answer.question}
                  <>{AnswerStatusIcon(answer.is_correct)}</>
                </pre>
              </Form.Label>
              {(answer.options && answer.options.length > 0) ||
              answer.question_type == 0 ? (
                <Form className="ml-2">
                  {answer.options.map((opt: string, idx: number) => {
                    let className = "";
                    if (opt.trim() === answer.actual_answer.trim()) {
                      className = "question-correct";
                    } else if (
                      opt === answer.answer &&
                      opt !== answer.correct
                    ) {
                      className = "question-incorrect";
                    }
                    return (
                      <div
                        className="interview-answer-option mb-1"
                        key={`option_${idx}`}>
                        <pre className={className}>
                          {String.fromCharCode(97 + idx)}. {opt}
                        </pre>
                      </div>
                    );
                  })}
                </Form>
              ) : (
                <Form>
                  <div className="interview-answer">
                    <pre>
                      <strong>Answer:</strong> {answer.answer}{" "}
                    </pre>
                  </div>
                  {!answer.is_correct && answer.answer_explanation && (
                    <div className="interview-answer">
                      <pre>
                        <strong>Explanation:</strong>{" "}
                        {answer.answer_explanation}{" "}
                      </pre>
                    </div>
                  )}
                </Form>
              )}
            </Form.Group>
          </div>
        );
      })}
    </div>
  );
};
