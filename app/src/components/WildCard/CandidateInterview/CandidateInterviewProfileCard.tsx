import { APP_ROUTE } from "@src/constants";
import { encrypt } from "@src/helper/encryption";
import { useEmployeeSelectedPagePermissions } from "@src/helper/pagePermissions";
import { CandidateBasicInterface } from "@src/redux/interfaces";
import Image from "next/image";
import Link from "next/link";

type CandidateInterviewProfileCardType = {
  candidate: CandidateBasicInterface;
  customRender?: React.ReactNode;
};

export const CandidateInterviewProfileCard: React.FC<
  CandidateInterviewProfileCardType
> = ({ candidate, customRender }) => {
  const candidatePermissions = useEmployeeSelectedPagePermissions("candidates");

  return (
    <div className="schedule-interview-candidate-head d-flex align-items-center justify-content-between  mb-3">
      <div className="d-flex">
        <span>
          <Image
            src={"/images/auth/undraw_profile.svg"}
            className="img-profile rounded-circle mr-2"
            width={44}
            height={44}
            alt=""
          />
        </span>
        <div>
          <h5>{candidate.name}</h5>
          <p className="m-0">{candidate.email}</p>
          {candidate.contact && <p className="m-0">{candidate.contact}</p>}
        </div>
      </div>

      <div className="ml-auto">
        <span className="ml-auto job-id mb-2  ">
          Candidate ID:{" "}
          {candidatePermissions.includes("read") ? (
            <Link
              href={`${APP_ROUTE.CANDIDATE_MANAGEMENT}/${encrypt(candidate.id.toString())}`}>
              #CAN-{candidate.id.toString().padStart(4, "0")}
            </Link>
          ) : (
            `#CAN-${candidate.id.toString().padStart(4, "0")}`
          )}
        </span>
        {customRender}
      </div>
    </div>
  );
};
