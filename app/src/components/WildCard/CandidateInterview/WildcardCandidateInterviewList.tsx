import React from "react";
import { Pagination } from "antd";
import { useSelector } from "react-redux";
import { RootState } from "@src/redux/reducers";
import {
  CandidateInterviewInterface,
  PageDetailInterface,
} from "@src/redux/interfaces";
import { APP_ROUTE } from "@src/constants";
import Link from "next/link";
import Image from "next/image";

type WildcardCandidateInterviewListProps = {
  fetchData: (page: number, limit: number) => void;
  subdomain: string;
  currentPagePermissions: string[];
  pageDetail: PageDetailInterface;
};

export function WildcardCandidateInterviewList({
  subdomain,
  fetchData,
}: WildcardCandidateInterviewListProps) {
  const { rows, count, currentPage, limit } = useSelector(
    (state: RootState) => state.candidateInterview,
  );

  // Handle page change for pagination
  const handlePageChange = async (page: number) => {
    await fetchData(page, limit);
  };

  const hasRows = rows && rows.length > 0;

  return (
    <>
      <div className="candidate-list-wrap p-3 schedule-interview-list">
        <div className="row row-gap-3 candidate-row-list">
          {hasRows ? (
            rows.map(
              (interview: CandidateInterviewInterface, index: number) => {
                const scheduleInterview =
                  interview.schedule_interviews &&
                  interview.schedule_interviews[0];
                return (
                  <div
                    key={index}
                    className="col-12 col-md-12 col-lg-12 col-xl-6 candidate-column">
                    <div className="candidate-list-card bg-white p-3 border rounded-3">
                      <div className="d-flex time-slot gap-2 mb-3 flex-wrap justify-content-between">
                        <span>
                          {" "}
                          Time Slot:{" "}
                          <b>
                            {scheduleInterview.interview_at?.strftime(
                              "%B %d, %Y %I:%M %p",
                            ) ?? "N/A"}
                          </b>
                        </span>
                        <div className="d-flex gap-2">
                          <span className="badge-round ms-auto">
                            Round: <b>0{scheduleInterview.interview_round}</b>
                          </span>
                          <span className="badge-round ms-auto">
                            Status:{" "}
                            <b
                              className={`${interview.status_name.toLowerCase()}-text-color`}>
                              {interview.status_name}
                            </b>
                          </span>
                        </div>
                      </div>
                      <div className="content-box">
                        <h4 className="heading-clr">
                          {interview.opportunity_title}
                        </h4>
                        <span>
                          Interview By:{" "}
                          <b>{scheduleInterview.interviewer_name}</b>
                        </span>
                      </div>
                      <hr className="my-3" />
                      <div className="d-flex gap-4">
                        <div className="bottom-detail">
                          <div className="d-flex gap-3 align-items-center">
                            <span>
                              <Image
                                src={"/images/auth/undraw_profile.svg"}
                                className="img-profile rounded-circle mr-2"
                                width={24}
                                height={24}
                                alt=""
                              />
                            </span>
                            <div className="content">
                              <h4>{interview.candidate_name}</h4>
                              <p className="m-0">
                                {interview.candidate_designation}
                              </p>
                            </div>
                          </div>

                          <Link
                            className="btn btn-primary"
                            href={`${APP_ROUTE.INTERVIEW_MANAGEMENT}/${interview.id}`.subdomainLink(
                              subdomain,
                            )}>
                            View Details
                          </Link>
                        </div>
                      </div>
                    </div>
                  </div>
                );
              },
            )
          ) : (
            <div className="d-flex flex-column align-items-center justify-content-center py-5 px-3 text-center">
              {/* SVG Icon (Calendar with Clock) */}
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width={64}
                height={64}
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
                strokeWidth={2}
                className="mb-3 text-secondary">
                <rect
                  x={3}
                  y={4}
                  width={18}
                  height={18}
                  rx={2}
                  ry={2}
                  strokeLinecap="round"
                  strokeLinejoin="round"
                />
                <line
                  x1={16}
                  y1={2}
                  x2={16}
                  y2={6}
                  strokeLinecap="round"
                  strokeLinejoin="round"
                />
                <line
                  x1={8}
                  y1={2}
                  x2={8}
                  y2={6}
                  strokeLinecap="round"
                  strokeLinejoin="round"
                />
                <line
                  x1={3}
                  y1={10}
                  x2={21}
                  y2={10}
                  strokeLinecap="round"
                  strokeLinejoin="round"
                />
                <circle
                  cx={12}
                  cy={16}
                  r={3}
                  strokeLinecap="round"
                  strokeLinejoin="round"
                />
                <polyline
                  points="12 14 12 16 13.5 17.25"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                />
              </svg>
              {/* Heading */}
              <h5 className="fw-bold text-dark">No Scheduled Interviews</h5>
            </div>
          )}
        </div>
      </div>

      <Pagination
        className="mt-4"
        current={currentPage}
        total={count}
        pageSize={limit}
        hideOnSinglePage
        onChange={handlePageChange}
      />
    </>
  );
}
