import React from "react";
import { Pagination } from "antd";
import { useSelector } from "react-redux";
import { RootState } from "@src/redux/reducers";
import { LocationInterface } from "@src/redux/interfaces";
import {
  openDialog,
  setLoader,
  updateLocationDetail,
} from "@src/redux/actions";
import DialogComponents from "@src/components/DialogComponents";
import Dropdown from "react-bootstrap/Dropdown";
import MoreVertIcon from "@mui/icons-material/MoreVert";
import { useAppDispatch } from "@src/redux/store";
import { locationApi } from "@src/apis/wildcardApis";
import flashMessage from "@src/components/FlashMessage";
import Link from "next/link";

type WildcardLocationListProps = {
  fetchData: (page: number, limit: number) => void;
  subdomain: string;
  locationPagePermssions: string[];
  addNewButton?: React.ReactNode;
}; // Add default value to handle undefined

// open confirmation modal to confirm delete a location

export function WildcardLocationList({
  fetchData,
  addNewButton,
  locationPagePermssions = [], // Default to an empty array if not provided
}: WildcardLocationListProps) {
  const dispatch = useAppDispatch();

  const { rows, count, currentPage, limit } = useSelector(
    (state: RootState) => state.location, // Ensure this is the correct state slice
  );

  const handlePageChange = async (page: number) => {
    await fetchData(page, limit);
  };

  const reflectLocationDetail = async (
    location: LocationInterface,
    data: LocationInterface,
  ) => {
    dispatch(
      updateLocationDetail({
        id: location.id,
        data: { ...location, ...data },
      }),
    );
  };

  const openViewModal = (location: LocationInterface) => {
    dispatch(
      openDialog({
        config: DialogComponents.VIEW_LOCATION_MODAL,
        options: {
          location: location,
        },
      }),
    );
  };

  const openEditModal = (location: LocationInterface) => {
    dispatch(
      openDialog({
        config: DialogComponents.EDIT_LOCATION_MODAL,
        options: {
          location: location,
          onLocationUpdate: reflectLocationDetail,
        },
      }),
    );
  };

  const openLocationDeleteModal = (location: LocationInterface) => {
    dispatch(
      openDialog({
        config: DialogComponents.CONFIRMATION_MODAL,
        options: {
          title: "Delete Location",
          buttonTitle: "Delete",
          message: (
            <div className="mt-2 mb-2">
              Are you sure you want to delete location{" "}
              <strong>({location.address})</strong>?
            </div>
          ),
          onConfirm: () => deleteLocation(location),
        },
      }),
    );
  };

  // request document
  const deleteLocation = async (location: LocationInterface) => {
    await dispatch(setLoader(true));
    const { success, message } = await locationApi.deleteLocation(location.id);
    if (success) {
      await fetchData(
        currentPage - (currentPage > 1 && rows.length == 1 ? 1 : 0),
        limit,
      );
    }
    flashMessage(message, success ? "success" : "error");
    await dispatch(setLoader(false));
  };

  const hasRows = rows && rows.length > 0;
  const hasEditPermission = locationPagePermssions.includes("edit");

  return (
    <>
      {hasRows ? (
        <div className={`table-responsive location-list`}>
          <table
            className="table table-hover dataTable"
            style={{ width: "100%" }}>
            <thead>
              <tr role="row">
                <th className="mw-50px">Sr. No</th>
                <th className="mw-80px">Address</th>
                <th className="mw-100px">City</th>
                <th className="mw-100px">State</th>
                <th className="mw-80px">Country</th>
                <th className="mw-80px">Pincode</th>
                <th className="mw-80px">Actions</th>
              </tr>
            </thead>
            <tbody>
              {rows.map((location: LocationInterface, index: number) => (
                <tr key={index}>
                  <th>{index + 1 + (currentPage - 1) * limit}</th>
                  <td>
                    <Link href={""} onClick={() => openViewModal(location)}>
                      {location.address}
                    </Link>
                  </td>
                  <td>{location.city}</td>
                  <td>{location.state}</td>
                  <td>{location.country}</td>
                  <td>{location.pincode}</td>
                  <td>
                    <div className="location-action no-arrow">
                      <Dropdown>
                        <Dropdown.Toggle
                          id={`location-dropdown-${location.id}`}
                          className="text-decoration-none"
                          as="span"
                          type="button"
                          role="button"
                          aria-haspopup="true"
                          aria-expanded="false">
                          <span className="text-black small">
                            <MoreVertIcon />
                          </span>
                        </Dropdown.Toggle>

                        <Dropdown.Menu
                          className="dropdown-menu-right shadow animated--grow-in"
                          aria-labelledby={`location-dropdown-${location.id}`}>
                          <Dropdown.Item
                            eventKey="1"
                            role="button"
                            onClick={() => openViewModal(location)}>
                            View Detail
                          </Dropdown.Item>
                          {hasEditPermission && (
                            <Dropdown.Item
                              eventKey="2"
                              role="button"
                              onClick={() => openEditModal(location)}>
                              Edit Detail
                            </Dropdown.Item>
                          )}
                          {/* <Dropdown.Item
                              eventKey="4"
                              onClick={() => openLocationDeleteModal(location)}
                              className="no-decoration">
                              Remove
                            </Dropdown.Item> */}
                        </Dropdown.Menu>
                      </Dropdown>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      ) : (
        <div className="d-flex flex-column align-items-center justify-content-center bg-light py-5 px-3 text-center">
          {/* SVG Icon */}
          <svg
            xmlns="http://www.w3.org/2000/svg"
            width={64}
            height={64}
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
            strokeWidth={2}
            className="mb-3 text-secondary">
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              d="M12 21c4.418 0 8-5.373 8-10a8 8 0 1 0-16 0c0 4.627 3.582 10 8 10z"
            />
            <circle
              cx={12}
              cy={11}
              r={3}
              stroke="currentColor"
              strokeWidth={2}
            />
          </svg>
          {/* Heading */}
          <h5 className="fw-bold text-dark">No Business Location Found</h5>
          {/* Description */}
          {addNewButton && (
            <div className="text-center">
              <p className="text-muted mb-3">
                You haven&apos;t added any business locations yet. Click below
                to add one.
              </p>
              <br />
              {addNewButton}
            </div>
          )}
        </div>
      )}

      <Pagination
        className="mt-4"
        current={currentPage}
        total={count}
        pageSize={limit}
        hideOnSinglePage
        onChange={handlePageChange}
      />
    </>
  );
}
