import React from "react";

function AllWarningsShow(props: any) {
  const { warnings } = props;
  return (
    <>
      <div>
        <h3>All Warnings ({warnings?.length || 0})</h3>
        <ul>
          {true ? (
            <div className="round-steps schedule-interview-round-steps">
              <ul>
                {warnings?.map((detail: any, index: number) => (
                  <li className="active" key={index}>
                    <div className="box">
                      <div className="sub-head">
                        <div className="round-head ">warning {index + 1}</div>
                      </div>
                      <div className="head">
                        <h4>{detail}</h4>
                      </div>
                    </div>
                  </li>
                ))}
              </ul>
            </div>
          ) : (
            <li>No warnings found.</li>
          )}
        </ul>
      </div>
    </>
  );
}

export default AllWarningsShow;
