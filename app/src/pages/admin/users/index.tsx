import React, { useCallback, useEffect } from "react";
import { PrivateLayout } from "@src/components/Layout";
import { GetServerSideProps } from "next";
import { extractSubdomainAndDomain } from "@src/helper/domain_helper";
import { useSelector } from "react-redux";
import { RootState } from "@src/redux/reducers";
import { useAppDispatch } from "@src/redux/store";
import { getAllUserList, setLoader } from "@src/redux/actions";
import { AdminUserList } from "@src/components/Admin";
import { KeyPairInterface } from "@src/redux/interfaces";
import PageHeader from "@src/components/PageHeader/PageHeader";
import { useRouter } from "next/router";
import { APP_ROUTE } from "@src/constants";

type UserPageProps = {
  subdomain: string;
  pageDetail: KeyPairInterface;
};

export default function UserPage({ subdomain, pageDetail }: UserPageProps) {
  const dispatch = useAppDispatch();
  const router = useRouter();

  const currentUser = useSelector((state: RootState) => state.auth.admin);

  const { currentPage, limit } = useSelector((state: RootState) => state.user);

  // Fetch the user data with pagination
  const fetchData = useCallback(
    async (currentPage: number, limit: number) => {
      await dispatch(setLoader(true));
      await dispatch(getAllUserList({ page: currentPage, limit: limit }));
      await dispatch(setLoader(false));
    },
    [dispatch],
  );

  useEffect(() => {
    if (currentUser && currentUser.user_type != "Super Admin") {
      router.push(APP_ROUTE.DASHBOARD);
    }
  }, [currentUser]);

  useEffect(() => {
    if (subdomain) {
      fetchData(currentPage ?? 1, limit ?? 10);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [subdomain]);

  return (
    <>
      <section className="user">
        <PageHeader
          pageTitle={pageDetail?.title ?? "User Management"}
          pageDescription={
            pageDetail?.description ?? "User Management Description"
          }>
          <div className="box-content">
            <AdminUserList fetchData={fetchData} />
          </div>
        </PageHeader>
      </section>
    </>
  );
}

// Get server-side props to validate and extract subdomain
export const getServerSideProps: GetServerSideProps = async ({ req, res }) => {
  const { redirectUrl, validate, subdomain, pageDetail } =
    await extractSubdomainAndDomain(
      req,
      {
        admin: true,
      },
      "user",
    );
  if (!validate) {
    return {
      redirect: {
        destination: redirectUrl,
        permanent: false,
      },
    };
  }

  return {
    props: {
      subdomain: subdomain,
      pageDetail: pageDetail,
    },
  };
};

UserPage.layout = PrivateLayout;
