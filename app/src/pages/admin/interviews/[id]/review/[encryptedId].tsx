import React, { useEffect, useState } from "react";
import { PrivateLayout } from "@src/components/Layout";
import { GetServerSideProps } from "next";
import { extractSubdomainAndDomain } from "@src/helper/domain_helper";
import { useAppDispatch } from "@src/redux/store";
import { openDialog, setLoader } from "@src/redux/actions";
import { APP_ROUTE } from "@src/constants";
import {
  InterviewWarningInterface,
  PageDetailInterface,
} from "@src/redux/interfaces";
import { useEmployeeSelectedPagePermissions } from "@src/helper/pagePermissions";
import PageHeader from "@src/components/PageHeader/PageHeader";
import { useRouter } from "next/router";
import flashMessage from "@src/components/FlashMessage";
import Link from "next/link";
import { decryptString } from "@src/helper/encryption";
import { candidateInterviewApi } from "@src/apis/wildcardApis";
import {
  CandidateAnswerSheet,
  CandidateInterviewProfileCard,
} from "@src/components/WildCard";
import { Button, Tabs, Tab, Row, Col } from "react-bootstrap";
import DialogComponents from "@src/components/DialogComponents";
import { Skeleton } from "antd";
import { WarningScreenshotPage } from "@src/components/WildCard/CandidateInterview/WarningScreenshotPage";

type InterviewPageProps = {
  subdomain: string;
  pageDetail: PageDetailInterface;
  id: number;
  interview_id: number;
};

export default function InterviewPage({
  id,
  pageDetail,
  interview_id,
}: InterviewPageProps) {
  const dispatch = useAppDispatch();
  const router = useRouter();
  const jobPermissions = useEmployeeSelectedPagePermissions("opportunities");
  const [interview, setInterview] = useState<any>(null);
  const [scheduleInterview, setScheduleInterview] = useState<any>(null);
  const [interviewWarnings, setInterviewWarnings] = useState<
    InterviewWarningInterface[]
  >([]);
  const [answerSheet, setAnswerSheet] = useState<any[]>([]);
  const [candidate, setCandidate] = useState<any>(null);

  const fetchAndSetDetail = async () => {
    await dispatch(setLoader(true));
    const { success, ...response } =
      await candidateInterviewApi.getInterviewReviewDetail(id, interview_id);
    await dispatch(setLoader(false));
    if (success) {
      const {
        interview,
        answer_sheet,
        schedule_interview,
        candidate,
        interview_warnings,
      } = response.data;
      setInterview(interview);
      setScheduleInterview(schedule_interview);
      setInterview(interview);
      setAnswerSheet(answer_sheet);
      setInterviewWarnings(interview_warnings ?? []);
      setCandidate(candidate);
    } else {
      flashMessage(response.message, "error");
      router.push(APP_ROUTE.INTERVIEW_MANAGEMENT);
    }
  };

  const openScreeningVideoModal = (videoUrl: string) => {
    dispatch(
      openDialog({
        config: DialogComponents.SUCCESS_MODAL,
        options: {
          closeButtonTitle: "Close",
          title: "Screening Video",
          message: (
            <div className="mt-2 mb-2">
              <video width="100%" controls>
                <source src={videoUrl} type="video/mp4" />
                Your browser does not support the video tag.
              </video>{" "}
            </div>
          ),
        },
      }),
    );
  };

  useEffect(() => {
    fetchAndSetDetail();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [id]);

  const hasJobReadPermission = jobPermissions.includes("read");

  return (
    <>
      <section className="interview">
        <PageHeader
          pageTitle={pageDetail.title ?? "Interview Management"}
          breadcrumb={["Interview Review"]}
          pageDescription={`Interview Review`}
          showBackButton
          buttonComponent={
            interview && (
              <span className="ml-auto job-id">
                Job ID{" "}
                {hasJobReadPermission ? (
                  <Link
                    target="_blank"
                    href={`${APP_ROUTE.OPPORTUNITY_MANAGEMENT}/${interview.opportunity_id}`}>
                    #J0B-{interview.opportunity_id.toString().padStart(4, "0")}
                  </Link>
                ) : (
                  `#J0B-${interview.opportunity_id.toString().padStart(4, "0")}`
                )}
              </span>
            )
          }>
          <div className="box-content">
            {interview && candidate && (
              <CandidateInterviewProfileCard
                candidate={candidate}
                customRender={
                  scheduleInterview &&
                  scheduleInterview.screening_video_url && (
                    <Button
                      className="ml-auto"
                      onClick={() =>
                        openScreeningVideoModal(
                          scheduleInterview.screening_video_url,
                        )
                      }>
                      View Screening Video
                    </Button>
                  )
                }
              />
            )}
            {/* {
              interview &&
            } */}

            <Tabs
              defaultActiveKey="answer-sheet"
              id="uncontrolled-tab-example"
              className="candidate-profile-tabs mb-3">
              <Tab eventKey="answer-sheet" title="Answer Sheet">
                <div className="tab-content">
                  <div className="tab-pane fade show active candi-details-tab">
                    {scheduleInterview && answerSheet && candidate ? (
                      <CandidateAnswerSheet
                        scheduleInterview={scheduleInterview}
                        answerSheet={answerSheet}
                      />
                    ) : (
                      <div>
                        <Skeleton active paragraph={{ rows: 15 }} />
                      </div>
                    )}
                  </div>
                </div>
              </Tab>
              <Tab eventKey="wanings" title="Interview Warnings">
                <div className="tab-content">
                  <div className="tab-pane fade show active candi-details-tab"></div>
                  {scheduleInterview &&
                  interviewWarnings.length > 0 &&
                  candidate ? (
                    <WarningScreenshotPage
                      interviewWarnings={interviewWarnings}
                    />
                  ) : (
                    <div className="interview-warning-gallery">
                      {/* Skeleton for the gallery images */}
                      <Row className="w-100">
                        {/* Create 5 skeleton loaders */}
                        {Array.from({ length: 12 }).map((_, index) => (
                          <Col
                            key={index}
                            sm={12}
                            md={6}
                            lg={3}
                            className="mb-4">
                            <div className="gallery-item position-relative">
                              {/* Skeleton loader with aspect ratio */}
                              <Skeleton.Image
                                active
                                className="w-100 h-100 warning-image-aspect-ratio"
                              />
                            </div>
                          </Col>
                        ))}
                      </Row>
                    </div>
                  )}
                </div>
              </Tab>
            </Tabs>
          </div>
        </PageHeader>
      </section>
    </>
  );
}

// Get server-side props to validate and extract subdomain
export const getServerSideProps: GetServerSideProps = async ({
  req,
  res,
  query,
}) => {
  const {
    redirectUrl,
    validate,
    subdomain,
    allowedRoles,
    pagePermissions,
    pageDetail,
  } = await extractSubdomainAndDomain(
    req,
    {
      wildcard: true,
    },
    "interviews",
  );
  if (!validate) {
    return {
      redirect: {
        destination: redirectUrl,
        permanent: false,
      },
    };
  }

  const encryptedId = String(query.encryptedId);
  const encryptedParam = decryptString(encryptedId);

  if (!encryptedParam) {
    return {
      redirect: {
        destination: redirectUrl,
        permanent: false,
      },
    };
  }

  const params = new URLSearchParams(encryptedParam);
  const interview_id = params.get("interview_id");
  const interview_type = params.get("interview_type");

  if (!interview_type || interview_type != "screening") {
    return {
      redirect: {
        destination: redirectUrl,
        permanent: false,
      },
    };
  }

  const id = Number(query.id) as Number;

  return {
    props: {
      subdomain: subdomain,
      id: id,
      interview_id: interview_id,
      allowedRoles: allowedRoles,
      pagePermissions: pagePermissions,
      requiredPermission: ["read"],
      pageDetail: pageDetail,
    },
  };
};

InterviewPage.layout = PrivateLayout;
