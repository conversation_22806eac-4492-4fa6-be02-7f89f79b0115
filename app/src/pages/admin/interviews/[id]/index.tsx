import React, { useEffect } from "react";
import { PrivateLayout } from "@src/components/Layout";
import { GetServerSideProps } from "next";
import { extractSubdomainAndDomain } from "@src/helper/domain_helper";
import { useSelector } from "react-redux";
import { RootState } from "@src/redux/reducers";
import { useAppDispatch } from "@src/redux/store";
import {
  getCandidateInterviewDetail,
  openDialog,
  setLoader,
  updateCandidateInterviewStatus,
} from "@src/redux/actions";
import { APP_ROUTE } from "@src/constants";
import {
  AuthEmployeeInterface,
  InterviewInterface,
  PageDetailInterface,
  PagePermissionInterface,
} from "@src/redux/interfaces";
import PageHeader from "@src/components/PageHeader/PageHeader";
import { useRouter } from "next/router";
import { ScheduleInterviewCard } from "@src/components/WildCard";
import flashMessage from "@src/components/FlashMessage";
import DialogComponents from "@src/components/DialogComponents";
import { Button } from "react-bootstrap";
import Image from "next/image";
import Link from "next/link";
import { useEmployeeSelectedPagePermissions } from "@src/helper/pagePermissions";
import { encrypt } from "@src/helper/encryption";
import { Skeleton, Tooltip } from "antd";

interface InterviewSearchParams {
  page: number;
  search?: string;
}

type InterviewPageProps = {
  subdomain: string;
  filters: InterviewSearchParams;
  pageDetail: PageDetailInterface;
  pagePermissions: PagePermissionInterface;
  id: number;
};

export default function InterviewPage({
  subdomain,
  id,
  pageDetail,
}: InterviewPageProps) {
  const dispatch = useAppDispatch();
  const candidatePermissions = useEmployeeSelectedPagePermissions("candidates");
  const interviewsPermissions =
    useEmployeeSelectedPagePermissions("interviews");
  const jobPermissions = useEmployeeSelectedPagePermissions("opportunities");

  const router = useRouter();
  const interviewDetail = useSelector(
    (state: RootState) => state.candidateInterview.detail,
  );

  const currentEmployee = useSelector(
    (state: RootState) => state.auth.employee,
  );

  const fetchAndSetInterviewDetail = async () => {
    await dispatch(setLoader(true));
    await dispatch(
      getCandidateInterviewDetail(id, async (success, response) => {
        await dispatch(setLoader(false));
        if (!success) {
          flashMessage(response.message, "error");
          router.push(APP_ROUTE.INTERVIEW_MANAGEMENT);
        }
      }),
    );
    await dispatch(setLoader(false));
  };

  useEffect(() => {
    fetchAndSetInterviewDetail();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [id]);

  const onUpdateCandidateInterview = async (status: number) => {
    const currentInterview = interviewDetail?.interview;
    if (currentInterview) {
      dispatch(setLoader(true));
      dispatch(
        updateCandidateInterviewStatus(
          currentInterview?.id,
          status,
          (success, response) => {
            if (success) {
              flashMessage(response.message, "success");
              fetchAndSetInterviewDetail();
            }
          },
        ),
      );
      dispatch(setLoader(false));
    }
  };

  const openStatusChangeModal = (status: string, statusId: number) => {
    dispatch(
      openDialog({
        config: DialogComponents.CONFIRMATION_MODAL,
        options: {
          title: "Candidate Interview Status Update",
          message: (
            <div className="mt-2 mb-2">
              Are you sure you want to candidate status to{" "}
              <strong>{status}</strong>?
              <br />
              This action cannot be reverted, and you will not be able to
              schedule a new round afterward
            </div>
          ),
          onConfirm: () => onUpdateCandidateInterview(statusId),
        },
      }),
    );
  };

  const hasJobReadPermission = jobPermissions.includes("read");
  const hasCandidateReadPermission = candidatePermissions.includes("read");
  const hasEditPermission = interviewsPermissions.includes("edit");

  const currentInterview = interviewDetail?.interview;
  const interviewRecords = interviewDetail?.records ?? [];
  const lastInterview =
    interviewRecords.length > 0
      ? interviewRecords[interviewRecords.length - 1]
      : null;

  return (
    <>
      <section className="interview">
        <PageHeader
          pageTitle={pageDetail.title ?? "Interview Management"}
          breadcrumb={[`${pageDetail.singular_name ?? "Interview"} Detail`]}
          pageDescription={`${pageDetail.singular_name ?? "Interview"} detail`}
          showBackButton
          buttonComponent={
            interviewDetail && (
              <span className="ml-auto job-id">
                Job ID{" "}
                {hasJobReadPermission ? (
                  <Link
                    target="_blank"
                    href={`${APP_ROUTE.OPPORTUNITY_MANAGEMENT}/${interviewDetail.interview.opportunity_id}`}>
                    #J0B-
                    {interviewDetail.interview.opportunity_id
                      .toString()
                      .padStart(4, "0")}
                  </Link>
                ) : (
                  `#J0B-${interviewDetail.interview.opportunity_id.toString().padStart(4, "0")}`
                )}
              </span>
            )
          }>
          <div className="box-content">
            <div className="row row-gap-mobile row-gap-3">
              <div className="col-xl-12 col-lg-12">
                <div className="card m-0">
                  <div className="p-3 border-bottom">
                    <div className="schedule-interview-candidate-head">
                      {currentInterview ? (
                        <>
                          <span>
                            <Image
                              src={"/images/auth/undraw_profile.svg"}
                              className="img-profile rounded-circle"
                              width={44}
                              height={44}
                              alt=""
                            />
                          </span>
                          <div className="content-box">
                            <div className="data">
                              <h5>
                                {currentInterview.candidate_name}{" "}
                                {currentInterview.offer_sent && (
                                  <span>Offer Sent</span>
                                )}
                              </h5>
                              <p>{currentInterview.candidate_designation}</p>
                              {currentInterview.status_name == "Finalize" && (
                                <span className="finalize">
                                  <Image
                                    src="/images/thumb_up.svg"
                                    alt="thumb_up"
                                    height={21}
                                    width={16}
                                  />
                                  finalize
                                </span>
                              )}
                            </div>
                          </div>
                          {hasCandidateReadPermission &&
                            interviewDetail?.interview && (
                              <Link
                                href={`${APP_ROUTE.CANDIDATE_MANAGEMENT}/${encrypt(interviewDetail.interview.candidate_id.toString())}`}>
                                <Button className="btn btn-primary border-0">
                                  View Profile
                                </Button>
                              </Link>
                            )}
                        </>
                      ) : (
                        <Skeleton active avatar paragraph={{ rows: 1 }} />
                      )}
                    </div>
                    {currentInterview ? (
                      <div className="job-title-id">
                        <h3 className="m-0">
                          {interviewDetail?.interview?.opportunity_title}
                          {/* <span>
                            Job ID{" "}
                            <b>
                              #JOB-
                              {interviewDetail?.interview?.opportunity_id
                                .toString()
                                .padStart(4, "0")}
                            </b>
                          </span> */}
                        </h3>
                      </div>
                    ) : (
                      <Skeleton active paragraph={false} />
                    )}
                  </div>
                  <div className="p-3">
                    <h6 className="mb-3 fw-semibold heading-clr">
                      Track Interviews Rounds
                    </h6>
                    <div className="round-steps schedule-interview-round-steps">
                      <ul>
                        {currentInterview &&
                          interviewRecords.map(
                            (detail: InterviewInterface, index: number) => (
                              <ScheduleInterviewCard
                                interview={detail}
                                key={index}
                                isLast={index + 1 == interviewRecords.length}
                                currentEmployee={
                                  currentEmployee as AuthEmployeeInterface
                                }
                                currentInterview={currentInterview}
                                hasEditPermission={hasEditPermission}
                              />
                            ),
                          )}
                      </ul>
                    </div>
                  </div>
                  {hasEditPermission &&
                  currentInterview?.status_name == "Finalize" &&
                  !currentInterview?.offer_sent &&
                  lastInterview ? (
                    <>
                      <div className="p-3 schedule-footer">
                        <Link
                          href={`${APP_ROUTE.INTERVIEW_MANAGEMENT}/${currentInterview.id}/offer-letter`}>
                          <Button className="btn btn-outline-primary">
                            + Generate offer letter
                          </Button>
                        </Link>
                      </div>
                    </>
                  ) : null}
                  {hasEditPermission &&
                    currentInterview?.status_id == 1 &&
                    lastInterview &&
                    !["Disqualified", "Rejected"].includes(
                      lastInterview.status_name,
                    ) && (
                      <>
                        <div className="p-3 schedule-footer">
                          {lastInterview.status_name == "Scheduled" ? (
                            <Tooltip
                              placement="top"
                              title={
                                "You can schedule next round only after complete the last round"
                              }
                              trigger={"hover"}>
                              <>
                                <Button
                                  className="btn btn-outline-primary"
                                  disabled>
                                  + Schedule Another Round
                                </Button>
                              </>
                            </Tooltip>
                          ) : (
                            <>
                              <Link
                                href={`${APP_ROUTE.INTERVIEW_MANAGEMENT}/${currentInterview.id}/new-round`}>
                                <Button className="btn btn-outline-primary">
                                  + Schedule Another Round
                                </Button>
                              </Link>
                            </>
                          )}
                          <div className="ml-auto flex-box">
                            {lastInterview.status_name == "Scheduled" ? (
                              <Tooltip
                                placement="top"
                                title={
                                  "You can shortlist only after updating last interview status"
                                }
                                trigger={"hover"}>
                                <>
                                  <Button
                                    className="btn btn-outline-success"
                                    disabled>
                                    <Image
                                      src="/images/check-green.svg"
                                      alt="check-green"
                                      width={10}
                                      height={9}
                                    />{" "}
                                    Shortlist
                                  </Button>
                                </>
                              </Tooltip>
                            ) : (
                              <>
                                <Button
                                  className="btn btn-outline-success"
                                  onClick={() =>
                                    openStatusChangeModal("Finalize", 3)
                                  }>
                                  <Image
                                    src="/images/check-green.svg"
                                    alt="check-green"
                                    width={10}
                                    height={9}
                                  />{" "}
                                  Shortlist
                                </Button>
                              </>
                            )}
                            <Button
                              className="btn btn-outline-reject"
                              onClick={() =>
                                openStatusChangeModal("Rejected", 2)
                              }>
                              <Image
                                src="/images/cross-red.svg"
                                alt="cross-red"
                                width={11}
                                height={10}
                              />{" "}
                              Reject
                            </Button>
                          </div>
                        </div>
                      </>
                    )}
                </div>
              </div>
            </div>
          </div>
        </PageHeader>
      </section>
    </>
  );
}

// Get server-side props to validate and extract subdomain
export const getServerSideProps: GetServerSideProps = async ({
  req,
  res,
  query,
}) => {
  const {
    redirectUrl,
    validate,
    subdomain,
    allowedRoles,
    pagePermissions,
    pageDetail,
  } = await extractSubdomainAndDomain(
    req,
    {
      wildcard: true,
    },
    "interviews",
  );
  if (!validate) {
    return {
      redirect: {
        destination: redirectUrl,
        permanent: false,
      },
    };
  }

  const id = Number(query.id) as Number;
  return {
    props: {
      subdomain: subdomain,
      id: id,
      allowedRoles: allowedRoles,
      pagePermissions: pagePermissions,
      requiredPermission: ["read"],
      pageDetail: pageDetail,
    },
  };
};

InterviewPage.layout = PrivateLayout;
