import React, { useCallback, useEffect, useState } from "react";
import { PrivateLayout } from "@src/components/Layout";
import { GetServerSideProps } from "next";
import { extractSubdomainAndDomain } from "@src/helper/domain_helper";
import { useSelector } from "react-redux";
import { RootState } from "@src/redux/reducers";
import { useAppDispatch } from "@src/redux/store";
import {
  getAllCandidateInterviewList,
  openDialog,
  setLoader,
} from "@src/redux/actions";
import { Button } from "react-bootstrap";
// import { WildcardInterviewList } from "@src/components/WildCard";
import { APP_ROUTE } from "@src/constants";
import {
  KeyPairInterface,
  PageDetailInterface,
  PagePermissionInterface,
} from "@src/redux/interfaces";
import { useEmployeePagePermissions } from "@src/helper/pagePermissions";
import PageHeader from "@src/components/PageHeader/PageHeader";
import { useRouter } from "next/router";
import { WildcardCandidateInterviewList } from "@src/components/WildCard";
import DialogComponents from "@src/components/DialogComponents";
import { Badge } from "antd";
import { Tune, Search } from "@mui/icons-material";

interface InterviewSearchParams {
  page: number;
  search?: string;
}

type InterviewPageProps = {
  subdomain: string;
  allowedRoles?: string[];
  pagePermissions?: PagePermissionInterface;
  requiredPermission?: string[];
  filters: InterviewSearchParams;
  pageDetail: PageDetailInterface;
};

export default function InterviewPage({
  subdomain,
  pagePermissions,
  filters,
  pageDetail,
}: InterviewPageProps) {
  const { search: searchString, page, ...remainingFilter } = filters;
  const dispatch = useAppDispatch();
  const router = useRouter();
  const [search, setSearch] = useState<string>("");
  const [searchText, setSearchText] = useState<string>(searchString ?? "");
  const [filterCount, setFilterCount] = useState<number>(0);
  const [filterState, setFilterState] = useState<KeyPairInterface>(
    remainingFilter as KeyPairInterface,
  );

  const { currentPage, limit } = useSelector(
    (state: RootState) => state.candidateInterview,
  );
  const interviewPagePermssions = useEmployeePagePermissions({
    pagePermissions,
  });

  // Fetch the interview data with pagination
  const fetchData = useCallback(
    async (
      currentPage: number,
      limit: number,
      q: string = search,
      filters: KeyPairInterface = filterState,
    ) => {
      try {
        await dispatch(setLoader(true));
        await dispatch(
          getAllCandidateInterviewList({
            page: currentPage,
            limit: limit,
            search: q,
            ...filters,
          }),
        );
        await dispatch(setLoader(false));
      } catch (err) {
        console.error("Error fetching candidates data:", err);
      } finally {
        await dispatch(setLoader(false));
      }
    },
    [dispatch, filterState, search],
  );

  useEffect(() => {
    if (subdomain) {
      fetchData(filters?.page ?? 1, limit ?? 10, search);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [subdomain]);

  // function use to callback function to get updated records
  const fetchRecords = async (page: number, limit: number) => {
    fetchData(page, limit, search);
  };

  useEffect(() => {
    let queryParams: InterviewSearchParams = { page: currentPage };
    if (search) {
      queryParams = { ...queryParams, search: search };
    }
    if (filterState) {
      queryParams = { ...queryParams, ...filterState };
    }
    router.push(
      { pathname: APP_ROUTE.INTERVIEW_MANAGEMENT, query: queryParams as any },
      undefined,
      { shallow: true },
    );
    if (filterState) {
      const filteredPairs = Object.entries(filterState).filter(
        ([key, value]) => value != null,
      );
      setFilterCount(filteredPairs.length);
    } else {
      setFilterCount(0);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [filterState, search, currentPage]);

  const submitSearch = () => {
    setSearch(searchText);
    fetchData(1, 10, searchText);
  };

  const setFilters = async (filters: KeyPairInterface, q: string = search) => {
    await setFilterState(filters);
    await setSearch(q);
    await fetchData(currentPage ?? 1, limit ?? 10, q, filters);
  };

  // Open modal to change filters
  const openFilterModal = () => {
    dispatch(
      openDialog({
        config: DialogComponents.INTERVIEW_FILTER_MODAL,
        options: {
          title: "Candidates Filters",
          filters: filterState,
          onConfirm: setFilters,
        },
      }),
    );
  };

  return (
    <>
      <section className="interview">
        <PageHeader
          pageTitle={pageDetail.title ?? "Interview Management"}
          pageDescription={
            pageDetail.description ?? "Interview Management Description"
          }>
          <div className="box-content">
            <div className="brief-filter-wrap gap-3 d-flex flex-wrap justify-content-between align-items-center mb-4">
              <div className="d-flex gap-3 flex-wrap">
                <div className="search position-relative">
                  <div className="input-wrapper">
                    <Search className="search-icon material-icons" />
                    <input
                      value={searchText}
                      onChange={(event) => setSearchText(event.target.value)}
                      className="form-control input-with-icon"
                      placeholder="Search by name or email"
                    />
                  </div>
                </div>
                <div className="position-relative theme-select">
                  <Button onClick={() => submitSearch()}>Search</Button>
                </div>
              </div>

              <div className="d-flex gap-3 flex-wrap">
                <div className="d-flex align-items-center gap-3">
                  <Button
                    onClick={openFilterModal}
                    className="btn btn-border d-flex align-items-center justify-content-between px-3 group">
                    <Tune className="me-1 " />
                    <span>Filters</span>
                    <Badge count={filterCount} showZero={false} dot={true} />
                  </Button>
                </div>
              </div>
            </div>

            <WildcardCandidateInterviewList
              fetchData={fetchRecords}
              subdomain={subdomain}
              currentPagePermissions={interviewPagePermssions}
              pageDetail={pageDetail}
            />
          </div>
        </PageHeader>
      </section>
    </>
  );
}

// Get server-side props to validate and extract subdomain
export const getServerSideProps: GetServerSideProps = async ({
  req,
  res,
  query,
}) => {
  const {
    redirectUrl,
    validate,
    subdomain,
    allowedRoles,
    pagePermissions,
    pageDetail,
  } = await extractSubdomainAndDomain(
    req,
    {
      wildcard: true,
    },
    "interviews",
  );
  if (!validate) {
    return {
      redirect: {
        destination: redirectUrl,
        permanent: false,
      },
    };
  }

  let filters: InterviewSearchParams = {
    page: 1,
  };

  if (query) {
    const { search, opportunityId, date_filter, interview_status } = query;
    const page = query.page ? Number(query.page) : 1;
    // `search` should be a string or undefined
    const searchString = typeof search === "string" ? search : undefined;

    // Construct filters object with default values or undefined
    filters = {
      ...filters, // Spread the existing properties (if any)
      ...(searchString ? { search: searchString } : {}), // Conditionally add `search`
      ...(page && Number.isNaN(page) ? {} : { page }), // Conditionally add `page` if it's valid
      ...(opportunityId && !Number.isNaN(opportunityId)
        ? { opportunity_id: opportunityId }
        : {}),
      ...(interview_status && !Number.isNaN(interview_status)
        ? { interview_status: interview_status }
        : {}),
      ...(date_filter ? { date_filter: date_filter } : {}),
    };
  }

  return {
    props: {
      subdomain: subdomain,
      allowedRoles: allowedRoles,
      pagePermissions: pagePermissions,
      requiredPermission: ["read"],
      filters: filters,
      pageDetail: pageDetail,
    },
  };
};

InterviewPage.layout = PrivateLayout;
