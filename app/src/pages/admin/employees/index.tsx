import React, { useCallback, useEffect, useState } from "react";
import { PrivateLayout } from "@src/components/Layout";
import { GetServerSideProps } from "next";
import { extractSubdomainAndDomain } from "@src/helper/domain_helper";
import { useSelector } from "react-redux";
import { RootState } from "@src/redux/reducers";
import { useAppDispatch } from "@src/redux/store";
import {
  getAllWildcardEmployeeList,
  openDialog,
  setLoader,
} from "@src/redux/actions";
import { Button } from "react-bootstrap";
import DialogComponents from "@src/components/DialogComponents";
import { WildcardEmployeeList } from "@src/components/WildCard";
import {
  KeyPairInterface,
  PagePermissionInterface,
} from "@src/redux/interfaces";
import { useEmployeePagePermissions } from "@src/helper/pagePermissions";
import PageHeader from "@src/components/PageHeader/PageHeader";
import { useRouter } from "next/router";
import { APP_ROUTE } from "@src/constants";

interface EmployeeSearchParams {
  page: number;
  search?: string;
}

type EmployeePageProps = {
  id: number;
  subdomain: string;
  allowedRoles?: string[];
  pagePermissions?: PagePermissionInterface;
  requiredPermission?: string[];
  filters: EmployeeSearchParams;
  pageDetail: KeyPairInterface;
};

export default function EmployeePage({
  subdomain,
  pagePermissions,
  filters,
  pageDetail,
}: EmployeePageProps) {
  const dispatch = useAppDispatch();
  const router = useRouter();

  const [search, setSearch] = useState<string>("");
  const [searched, setSearched] = useState<string>("");
  const { currentPage, limit, rows } = useSelector(
    (state: RootState) => state.employee,
  );
  const currentPagePermissions: string[] = useEmployeePagePermissions({
    pagePermissions,
  });

  // Fetch the employee data with pagination
  const fetchData = useCallback(
    async (currentPage: number, limit: number, search?: string) => {
      try {
        await dispatch(setLoader(true));
        await dispatch(
          getAllWildcardEmployeeList({
            page: currentPage,
            limit: limit,
            search: search,
          }),
        );
        setSearched(search ? search.trim() : "");
        await dispatch(setLoader(false));
      } catch (err) {
        console.error("Error fetching departments data:", err);
      } finally {
        await dispatch(setLoader(false));
      }
    },
    [dispatch],
  );

  useEffect(() => {
    if (subdomain) {
      fetchData(filters?.page ?? 1, limit ?? 9, search);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [subdomain]);

  useEffect(() => {
    let queryParams: EmployeeSearchParams = { page: currentPage };
    if (search) {
      queryParams = { ...queryParams, search: search };
    }
    router.push(
      { pathname: APP_ROUTE.EMPLOYEE_MANAGEMENT, query: queryParams as any },
      undefined,
      { shallow: true },
    );
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [search, currentPage]);

  // Open the modal to add a new employee
  const openNewEmployeeModal = () => {
    dispatch(
      openDialog({
        config: DialogComponents.ADD_NEW_EMPLOYEE,
        options: {
          title: `Add New ${pageDetail.singular_name ?? "Employee"}`,
          onEmployeeCreate: onEmployeeCreate,
        },
      }),
    );
  };

  // Handle employee creation and refetch data
  const onEmployeeCreate = async () => {
    fetchData(currentPage, limit);
  };

  // function use to callback function to get updated records
  const fetchRecords = async (page: number, limit: number) => {
    fetchData(page, limit, search);
  };

  const addNewButton = currentPagePermissions.includes("write") ? (
    <Button onClick={openNewEmployeeModal} className="ms-auto">
      + Add {pageDetail.singular_name ?? "Employee"}
    </Button>
  ) : null;

  return (
    <>
      <section className="employee">
        <PageHeader
          pageTitle={pageDetail?.title ?? "Employee Management"}
          pageDescription={
            pageDetail?.description ?? "Employee Management Description"
          }
          buttonComponent={(rows.length > 0 || searched != "") && addNewButton}>
          <div className="box-content">
            {rows.length > 0 && (
              <div className="search-box mb-3">
                <div className="col-lg-6 d-flex gap-3">
                  <input
                    value={search}
                    onChange={(event) => setSearch(event.target.value)}
                    className="search-input"
                    placeholder="Search by first name , last name or email"
                  />
                  <Button onClick={() => fetchRecords(1, 9)}>Search</Button>
                </div>
              </div>
            )}

            <WildcardEmployeeList
              fetchData={fetchRecords}
              subdomain={subdomain}
              addNewButton={searched == "" && addNewButton}
              currentPagePermissions={currentPagePermissions}
              pageDetail={pageDetail}
            />
          </div>
        </PageHeader>
      </section>
    </>
  );
}

// Get server-side props to validate and extract subdomain
export const getServerSideProps: GetServerSideProps = async ({
  req,
  res,
  query,
}) => {
  const {
    redirectUrl,
    validate,
    subdomain,
    allowedRoles,
    pagePermissions,
    pageDetail,
  } = await extractSubdomainAndDomain(
    req,
    {
      wildcard: true,
    },
    "employees",
  );
  if (!validate) {
    return {
      redirect: {
        destination: redirectUrl,
        permanent: false,
      },
    };
  }

  let filters: EmployeeSearchParams = {
    page: 1,
  };

  if (query) {
    const { search } = query;
    const page = query.page ? Number(query.page) : 1;
    // `search` should be a string or undefined
    const searchString = typeof search === "string" ? search : undefined;

    // Construct filters object with default values or undefined
    filters = {
      ...filters, // Spread the existing properties (if any)
      ...(searchString ? { search: searchString } : {}), // Conditionally add `search`
      ...(page && Number.isNaN(page) ? {} : { page }), // Conditionally add `page` if it's valid
    };
  }

  return {
    props: {
      subdomain: subdomain,
      allowedRoles: allowedRoles,
      pagePermissions: pagePermissions,
      requiredPermission: ["read"],
      filters: filters,
      pageDetail: pageDetail,
    },
  };
};

EmployeePage.layout = PrivateLayout;
