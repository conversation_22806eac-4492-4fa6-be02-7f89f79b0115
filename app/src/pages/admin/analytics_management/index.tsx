import React from "react";
import { PrivateLayout } from "@src/components/Layout";
import {
  CandidatesRegisteredCard,
  StatsCard,
  HiringAnalyticsCard,
  LastestJobsPostedCard,
  TodayInterviewSchedule,
  JobStatsChartCard,
} from "@src/components/WildCard/AnalyticsManagement";
import { extractSubdomainAndDomain } from "@src/helper/domain_helper";
import { GetServerSideProps } from "next";
import { useEmployeeSelectedPagePermissions } from "@src/helper/pagePermissions";
import PageHeader from "@src/components/PageHeader/PageHeader";
import { KeyPairInterface } from "@src/redux/interfaces";
import { ExportAnalytics } from "@src/components/WildCard/AnalyticsManagement";

const settings = {
  dots: true,
  infinite: false,
  speed: 500,
  slidesToShow: 2,
  slidesToScroll: 1,
  responsive: [
    {
      breakpoint: 1024,
      settings: {
        slidesToShow: 2,
        slidesToScroll: 1,
      },
    },
    {
      breakpoint: 820,
      settings: {
        slidesToShow: 1,
        slidesToScroll: 1,
      },
    },
  ],
};

type AnalyticsManagementPageProps = {
  pageDetail: KeyPairInterface;
};

export default function AnalyticsManagementPage({
  pageDetail,
}: AnalyticsManagementPageProps) {
  const jobPermissions = useEmployeeSelectedPagePermissions("opportunities");
  const interviewsPermissions =
    useEmployeeSelectedPagePermissions("interviews");

  return (
    <section className="hiring">
      <PageHeader
        pageTitle={pageDetail?.title ?? "Analytics Management"}
        buttonComponent={<ExportAnalytics />}
        insideCard={false}>
        {/*Stats Card*/}
        <div className="card mb-3 Analytics-Management">
          <StatsCard />
        </div>

        {/* Hiring Analytics */}
        <div className="row row-gap-4 d-flex align-items-stretch">
          <div className="col-xl-7 col-12 d-flex flex-column">
            <HiringAnalyticsCard />
          </div>
          <div className="col-xl-5 col-12 d-flex flex-column">
            <CandidatesRegisteredCard />
          </div>

          {/*Lastest Job Posted Card*/}
          {jobPermissions && jobPermissions.includes("read") && (
            <div className="col-lg-12">
              <LastestJobsPostedCard settings={settings} />
            </div>
          )}
          {interviewsPermissions && interviewsPermissions.includes("read") ? (
            <div className="row d-flex align-items-stretch">
              <div className="col-xl-5 col-12 d-flex flex-column">
                <JobStatsChartCard />
              </div>
              {/* Today's Interview Schedule */}
              <div className="col-xl-7 col-12 d-flex flex-column">
                <TodayInterviewSchedule />
              </div>
            </div>
          ) : (
            <div className="col-lg-12 col-12">
              <JobStatsChartCard />
            </div>
          )}
        </div>
      </PageHeader>
    </section>
  );
}

export const getServerSideProps: GetServerSideProps = async ({
  req,
  res,
  query,
}) => {
  const {
    redirectUrl,
    validate,
    subdomain,
    allowedRoles,
    pagePermissions,
    pageDetail,
  } = await extractSubdomainAndDomain(
    req,
    { wildcard: true },
    "analytics_management",
  );
  if (!validate) {
    return {
      redirect: {
        destination: redirectUrl,
        permanent: false,
      },
    };
  }

  return {
    props: {
      subdomain: subdomain,
      allowedRoles: allowedRoles,
      pagePermissions: pagePermissions,
      requiredPermission: ["read"],
      pageDetail: pageDetail,
    },
  };
};

AnalyticsManagementPage.layout = PrivateLayout;
