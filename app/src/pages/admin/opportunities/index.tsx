import React, { useCallback, useEffect, useState } from "react";
import { PrivateLayout } from "@src/components/Layout";
import { GetServerSideProps } from "next";
import { extractSubdomainAndDomain } from "@src/helper/domain_helper";
import { useSelector } from "react-redux";
import { RootState } from "@src/redux/reducers";
import { useAppDispatch } from "@src/redux/store";
import { getAllOpportunityList, setLoader } from "@src/redux/actions";
import { Button } from "react-bootstrap";
import { WildcardOpportunityList } from "@src/components/WildCard";
import Link from "next/link";
import { APP_ROUTE } from "@src/constants";
import {
  KeyPairInterface,
  PagePermissionInterface,
} from "@src/redux/interfaces";
import { useEmployeePagePermissions } from "@src/helper/pagePermissions";
import PageHeader from "@src/components/PageHeader/PageHeader";
import { useRouter } from "next/router";

interface OpportunitySearchParams {
  page: number;
  search?: string;
}

type OpportunityPageProps = {
  subdomain: string;
  allowedRoles?: string[];
  pagePermissions?: PagePermissionInterface;
  requiredPermission?: string[];
  filters: OpportunitySearchParams;
  pageDetail: KeyPairInterface;
};

export default function OpportunityPage({
  subdomain,
  pagePermissions,
  filters,
  pageDetail,
}: OpportunityPageProps) {
  const dispatch = useAppDispatch();
  const router = useRouter();
  const [search, setSearch] = useState<string>("");
  const { currentPage, limit, rows } = useSelector(
    (state: RootState) => state.opportunity,
  );
  const opportunityPagePermssions = useEmployeePagePermissions({
    pagePermissions,
  });

  // Fetch the opportunity data with pagination
  const fetchData = useCallback(
    async (currentPage: number, limit: number, search?: string) => {
      try {
        await dispatch(setLoader(true));
        await dispatch(
          getAllOpportunityList({
            page: currentPage,
            limit: limit,
            search: search,
          }),
        );
        await dispatch(setLoader(false));
      } catch (err) {
        console.error("Error fetching departments data:", err);
      } finally {
        await dispatch(setLoader(false));
      }
    },
    [dispatch],
  );

  useEffect(() => {
    if (subdomain) {
      fetchData(filters?.page ?? 1, limit ?? 10, search);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [subdomain]);

  // function use to callback function to get updated records
  const fetchRecords = async (page: number, limit: number) => {
    fetchData(page, limit, search);
  };

  useEffect(() => {
    let queryParams: OpportunitySearchParams = { page: currentPage };
    if (search) {
      queryParams = { ...queryParams, search: search };
    }
    router.push(
      { pathname: APP_ROUTE.OPPORTUNITY_MANAGEMENT, query: queryParams as any },
      undefined,
      { shallow: true },
    );
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [search, currentPage]);

  const addNewButton = opportunityPagePermssions.includes("write") ? (
    <Link
      href={`${APP_ROUTE.OPPORTUNITY_MANAGEMENT}/new`.subdomainLink(subdomain)}
      className="no-decoration ms-auto">
      <Button className="ms-auto">
        + Add {pageDetail?.singular_name ?? "Opportunity"}
      </Button>
    </Link>
  ) : null;

  return (
    <>
      <section className="opportunity">
        <PageHeader
          pageTitle={pageDetail?.title ?? "Opportunity Management"}
          pageDescription={
            pageDetail?.description ?? "Opportunity Management Description"
          }
          buttonComponent={rows.length > 0 && addNewButton}>
          <div className="box-content">
            <div className="search-box mb-3">
              <div className="col-lg-6 d-flex gap-3">
                <input
                  value={search}
                  onChange={(event) => setSearch(event.target.value)}
                  className="search-input"
                  placeholder="Search by job title"
                />
                <Button onClick={() => fetchRecords(1, 10)}>Search</Button>
              </div>
            </div>
            <WildcardOpportunityList
              fetchData={fetchRecords}
              subdomain={subdomain}
              addNewButton={addNewButton}
              opportunityPagePermssions={opportunityPagePermssions}
            />
          </div>
        </PageHeader>
      </section>
    </>
  );
}

// Get server-side props to validate and extract subdomain
export const getServerSideProps: GetServerSideProps = async ({
  req,
  res,
  query,
}) => {
  const {
    redirectUrl,
    validate,
    subdomain,
    allowedRoles,
    pagePermissions,
    pageDetail,
  } = await extractSubdomainAndDomain(
    req,
    {
      wildcard: true,
    },
    "opportunities",
  );
  if (!validate) {
    return {
      redirect: {
        destination: redirectUrl,
        permanent: false,
      },
    };
  }

  let filters: OpportunitySearchParams = {
    page: 1,
  };

  if (query) {
    const { search } = query;
    const page = query.page ? Number(query.page) : 1;
    // `search` should be a string or undefined
    const searchString = typeof search === "string" ? search : undefined;

    // Construct filters object with default values or undefined
    filters = {
      ...filters, // Spread the existing properties (if any)
      ...(searchString ? { search: searchString } : {}), // Conditionally add `search`
      ...(page && Number.isNaN(page) ? {} : { page }), // Conditionally add `page` if it's valid
    };
  }

  return {
    props: {
      subdomain: subdomain,
      allowedRoles: allowedRoles,
      pagePermissions: pagePermissions,
      requiredPermission: ["read"],
      filters: filters,
      pageDetail: pageDetail,
    },
  };
};

OpportunityPage.layout = PrivateLayout;
