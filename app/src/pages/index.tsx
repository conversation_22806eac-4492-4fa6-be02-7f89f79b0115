import {
  FAQSection,
  HowItWorkSection,
  IntroSection,
  CtaSechdule,
  FeatureNew,
  MainBannerSection,
} from "@src/components/PublicPage";
import { PublicLayout } from "@src/components/Layout";
import { extractSubdomainAndDomain } from "@src/helper/domain_helper";
import { GetServerSideProps } from "next";
import { PlanSection } from "../components/PublicPage/Home";

export default function Home() {
  return (
    <>
      <MainBannerSection />
      <IntroSection />
      <CtaSechdule />
      <FeatureNew />
      <HowItWorkSection />
      <PlanSection />
      <FAQSection />
    </>
  );
}

export const getServerSideProps: GetServerSideProps = async ({ req, res }) => {
  const { redirectUrl, validate } = await extractSubdomainAndDomain(
    req,
    {
      www: true,
    },
    "",
    true,
  );
  if (!validate) {
    return {
      redirect: {
        destination: redirectUrl,
        permanent: false,
      },
    };
  }

  return {
    props: {},
  };
};

Home.layout = PublicLayout;
