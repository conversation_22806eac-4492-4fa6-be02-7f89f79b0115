import React, { useCallback, useEffect, useState } from "react";
import { CandidateLayout } from "@src/components/Layout";
import CandidatePageHeader from "@src/components/PageHeader/CandidatePageHeader";
import { JobCard } from "@src/components/Opportunity";
import { OpportunityInterface } from "@src/redux/interfaces";
import { useAppDispatch } from "@src/redux/store";
import { extractSubdomainAndDomain } from "@src/helper/domain_helper";
import { GetServerSideProps } from "next";
import { Pagination } from "antd";
import { Row, Col, Button } from "react-bootstrap";
import { Card } from "react-bootstrap";
import { setLoader } from "@src/redux/actions";
import { jobsApi } from "@src/apis/wildcardCandidateApis";
import SearchIcon from "@mui/icons-material/Search";

export default function MatchingJobs() {
  const dispatch = useAppDispatch();
  const [{ count, currentPage, limit, rows }, setState] = useState<{
    currentPage: number;
    limit: number;
    count: number;
    rows: OpportunityInterface[];
  }>({
    currentPage: 1,
    limit: 10,
    count: 0,
    rows: [],
  });
  const [search, setSearch] = useState<string>("");
  const [searchText, setSearchText] = useState<string>("");

  const fetchData = useCallback(
    async (currentPage: number, limit: number, q: string = search) => {
      await dispatch(setLoader(true));
      const { success, ...response } = await jobsApi.getJobsList({
        page: currentPage,
        limit: limit,
        search: q,
      });
      if (success) {
        const { page, rows, count, limit } = response.data;
        setState({
          rows: rows,
          limit: limit,
          count: count,
          currentPage: page,
        });
      }
      await dispatch(setLoader(false));
    },
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [dispatch, currentPage, limit],
  );

  useEffect(() => {
    fetchData(1, 10);
  }, [fetchData]);

  const handlePageChange = async (page: number) => {
    await fetchData(page, limit);
  };

  const submitSearch = () => {
    setSearch(searchText);
    fetchData(1, 10, searchText);
  };

  return (
    <>
      <CandidatePageHeader pageTitle="Matching Jobs">
        <div className="card bg-transparent shadow-none">
          <Row className="row-gap-mobile row-gap-3">
            <Col lg={12}>
              <Card className="p-0 mb-0 candidate-self-card schedule-interview-self-card my-profile">
                <div className="card-header common-heading border-bottom p-3">
                  <div className="d-flex align-items-center flex-wrap flex-lg-nowrap gap-3 w-100">
                    <h4 className="mb-0 heading-clr page-description">
                      Matching Jobs
                    </h4>
                  </div>
                </div>
                <Card.Body className="p-3">
                  {/* <!--filter--> */}
                  <div className="brief-filter-wrap gap-3 d-flex flex-wrap justify-content-between align-items-center mb-4">
                    <div className="d-flex gap-3 flex-wrap">
                      <div className="search position-relative">
                        <div className="input-wrapper">
                          <SearchIcon className="search-icon material-icons" />
                          <input
                            value={searchText}
                            onChange={(event) =>
                              setSearchText(event.target.value)
                            }
                            className="form-control input-with-icon"
                            placeholder="Search by name or email"
                          />
                        </div>
                      </div>
                      <div className="position-relative theme-select">
                        <Button onClick={() => submitSearch()}>Search</Button>
                      </div>
                    </div>
                  </div>
                  {/* <!--filter-end--> */}

                  {/* <!--list--> */}
                  {count > 0 ? (
                    <div className="candidate-list-wrap p-3 matching-jobs">
                      <div className="row">
                        {rows.map((row, index) => (
                          <JobCard
                            jobDetail={row}
                            key={index}
                            // applyJob={applyJob}
                          />
                        ))}
                      </div>
                    </div>
                  ) : (
                    <>
                      <div className="text-center">
                        <h6>No Data Available</h6>
                      </div>
                    </>
                  )}

                  <Pagination
                    className="mt-4"
                    current={currentPage}
                    total={count}
                    pageSize={limit}
                    hideOnSinglePage
                    onChange={handlePageChange}
                  />
                </Card.Body>
              </Card>
            </Col>
          </Row>
        </div>
      </CandidatePageHeader>
    </>
  );
}

// Get server-side props to validate and extract subdomain
export const getServerSideProps: GetServerSideProps = async ({
  req,
  res,
  query,
}) => {
  const { redirectUrl, validate, subdomain } = await extractSubdomainAndDomain(
    req,
    { wildcardCandidate: true },
  );
  if (!validate) {
    return {
      redirect: {
        destination: redirectUrl,
        permanent: false,
      },
    };
  }

  return {
    props: {
      subdomain: subdomain,
    },
  };
};

MatchingJobs.layout = CandidateLayout;
