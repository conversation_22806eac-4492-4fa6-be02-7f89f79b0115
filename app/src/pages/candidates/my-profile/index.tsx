import React from "react";
import { CandidateLayout } from "@src/components/Layout";
import { GetServerSideProps } from "next";
import { extractSubdomainAndDomain } from "@src/helper/domain_helper";
import { Row, Col, Tab, Tabs } from "react-bootstrap";

import { Card, ListGroup, Image } from "react-bootstrap";
import { Call } from "@mui/icons-material";
import Link from "next/link";
import {
  CandidateDocuments,
  CandidateEducation,
  CandidateExperience,
  CandidateProfessionalSummary,
  CandidateRatingReview,
  CandidateSkills,
} from "@src/components/WildCard";
import CandidatePageHeader from "@src/components/PageHeader/CandidatePageHeader";
import { useCurrentCandidate } from "@src/helper/candidate";
import { Skeleton } from "antd";
import { profileApi } from "@src/apis/wildcardCandidateApis";

export default function MyProfile() {
  const candidate = useCurrentCandidate();

  if (!candidate) {
    return (
      <CandidatePageHeader pageTitle="My Profile">
        <Row className="row-gap-mobile row-gap-3">
          <Col lg={12}>
            <Card className="p-3 mb-0 candidate-self-card schedule-interview-self-card my-profile">
              <Card.Body className="p-0">
                <Skeleton avatar active paragraph={{ rows: 2 }} />
              </Card.Body>
            </Card>
          </Col>
          <Col lg={12}>
            <Card>
              <Card.Body>
                <Skeleton active paragraph={{ rows: 10 }} />
              </Card.Body>
            </Card>
          </Col>
        </Row>
      </CandidatePageHeader>
    );
  }

  return (
    <>
      <CandidatePageHeader pageTitle="My Profile">
        <div className="card bg-transparent shadow-none">
          <Row className="row-gap-mobile row-gap-3">
            <Col lg={12}>
              <Card className="p-3 mb-0 candidate-self-card schedule-interview-self-card my-profile">
                <Card.Body className="p-0">
                  <div className="candidate-profile-card d-flex gap-3">
                    <Image
                      src={"/images/auth/undraw_profile.svg"}
                      className="img-profile rounded-circle"
                      width={60}
                      height={60}
                      alt=""
                    />
                    <div className="candidate-brief w-100">
                      <div className="detail">
                        <h4 className="candidate-name ">{candidate.name}</h4>
                        <p className="text-clr p-0 mb-0 ">{candidate.email}</p>
                        {!candidate.is_fresher && (
                          <div className="flex-box">
                            <p>
                              Experience:{" "}
                              <span>{candidate.total_experience} years</span>
                            </p>
                          </div>
                        )}
                      </div>
                      <div className="right-detail">
                        <div className="casndi-contact">
                          {candidate.contact && (
                            <>
                              <span>
                                <Call
                                  className="material-icons"
                                  fontSize={"small"}
                                />
                              </span>
                              <div className="contact-data">
                                <p className="mb-0 heading-clr fw-medium">
                                  {candidate.contact}
                                </p>
                              </div>
                            </>
                          )}
                        </div>

                        <div className="candi-other-links">
                          {candidate.website ||
                          candidate.github ||
                          candidate.linkedin ? (
                            <div className="justify-content-center d-flex gap-2 align-items-center">
                              <label>Social-Links:</label>
                              <ListGroup
                                horizontal
                                className="list-inline mb-0">
                                {candidate.website && (
                                  <ListGroup.Item className="list-inline-item">
                                    <Link
                                      href={candidate.website}
                                      target="_blank">
                                      <Image
                                        src="/images/website.svg"
                                        width={30}
                                        height={20}
                                        alt="icon"
                                      />
                                    </Link>
                                  </ListGroup.Item>
                                )}
                                {candidate.github && (
                                  <ListGroup.Item className="list-inline-item">
                                    <Link
                                      href={candidate.github}
                                      target="_blank">
                                      <Image
                                        src="/images/icons/github.svg"
                                        width={30}
                                        height={20}
                                        alt="icon"
                                      />
                                    </Link>
                                  </ListGroup.Item>
                                )}
                                {candidate.linkedin && (
                                  <ListGroup.Item className="list-inline-item">
                                    <Link
                                      href={candidate.linkedin}
                                      target="_blank">
                                      <Image
                                        src="/images/icons/linkedin.svg"
                                        width={30}
                                        height={20}
                                        alt="icon"
                                      />
                                    </Link>
                                  </ListGroup.Item>
                                )}
                              </ListGroup>
                            </div>
                          ) : null}
                        </div>
                      </div>
                    </div>
                  </div>
                </Card.Body>
              </Card>
            </Col>
            <Col lg={12}>
              <Card>
                <Card.Body>
                  <Tabs
                    defaultActiveKey="candidate-detail"
                    id="uncontrolled-tab-example"
                    className="candidate-profile-tabs mb-3">
                    <Tab
                      eventKey="candidate-detail"
                      title="Professional Details">
                      <div className="tab-content">
                        <div className="tab-pane fade show active candi-details-tab">
                          <CandidateProfessionalSummary candidate={candidate} />

                          <CandidateExperience
                            experiences={candidate.experiences}
                          />

                          <CandidateEducation
                            educations={candidate.educations_informations}
                          />
                          <CandidateSkills skills={candidate.skills} />
                        </div>
                      </div>
                    </Tab>
                    <Tab eventKey="candidate-feedback" title="Rating & Review">
                      <div className="tab-content">
                        <div className="tab-pane fade show active candi-details-tab">
                          <CandidateRatingReview
                            id={candidate.id}
                            showJob
                            fetchFunction={profileApi.getFeedbacks as any}
                          />
                        </div>
                      </div>
                    </Tab>
                    <Tab eventKey="candidate-documents" title="Documents">
                      <div className="tab-content">
                        <div className="tab-pane fade show active candi-details-tab">
                          <CandidateDocuments
                            id={candidate.id}
                            fetchFunction={profileApi.getDocuments as any}
                          />
                        </div>
                      </div>
                    </Tab>
                  </Tabs>
                </Card.Body>
              </Card>
            </Col>
          </Row>
        </div>
      </CandidatePageHeader>
    </>
  );
}

// Get server-side props to validate and extract subdomain
export const getServerSideProps: GetServerSideProps = async ({
  req,
  res,
  query,
}) => {
  const { redirectUrl, validate, subdomain } = await extractSubdomainAndDomain(
    req,
    { wildcardCandidate: true },
  );
  if (!validate) {
    return {
      redirect: {
        destination: redirectUrl,
        permanent: false,
      },
    };
  }

  return {
    props: {
      subdomain: subdomain,
    },
  };
};

MyProfile.layout = CandidateLayout;
