import "@src/assets/css/root.css";

import "@src/assets/css/public-layout.css";

import "@src/assets/css/common.css";
import "@src/assets/css/auth.css";
import "@src/assets/css/loader.css";
import "@src/assets/css/private-layout.css";
import "@src/assets/css/candidate-layout.css";
import "@src/assets/css/antd.css";

import "react-datepicker/dist/react-datepicker.css";
import "react-phone-number-input/style.css";
import "@src/assets/css/input.css";

import "@src/assets/css/app.css";
import "@src/assets/css/custom.css";
import "@src/assets/css/responsive.css";
import "@src/assets/css/offer-letter.css";
import "@src/assets/css/wildcard-public-layout.css";

// helper functions
import "@src/utils/string";
import "react-quill/dist/quill.snow.css"; // Import styles

import "slick-carousel/slick/slick.css";
import "slick-carousel/slick/slick-theme.css";
import "@src/helper/global_declare";

import React from "react";
import Head from "next/head";

import type { AppProps } from "next/app";

import { Provider } from "react-redux";
import store, { wrapper } from "@src/redux/store";

import { PersistGate } from "redux-persist/integration/react";
import { persistStore } from "redux-persist";
const persistor = persistStore(store);

type PageProps = {
  layout?: React.ComponentType;
};

const App: React.FC<AppProps & PageProps> = ({ Component, pageProps }) => {
  const Layout =
    (Component as any).layout || (({ children }: any) => <>{children}</>);
  const { preventPreserveState, ...restProps } = pageProps;

  return (
    <React.Fragment>
      <Head>
        <meta
          name="viewport"
          content="width=device-width, initial-scale=1, shrink-to-fit=no"
        />
        <title>Recruitease Pro</title>
      </Head>
      {preventPreserveState ? (
        <Layout>
          <Component {...restProps} />
        </Layout>
      ) : (
        <Provider store={store}>
          <PersistGate loading={null} persistor={persistor}>
            <Layout {...restProps}>
              <Component {...restProps} />
            </Layout>
          </PersistGate>
        </Provider>
      )}
    </React.Fragment>
  );
};

export default wrapper.withRedux(App);
