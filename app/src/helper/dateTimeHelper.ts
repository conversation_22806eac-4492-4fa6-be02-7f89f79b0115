export function ConvertDurationToMinutes(duration: string): number {
  let totalMinutes = 0;
  const hourMatch = duration.match(/(\d+)\s*hour/);
  const minuteMatch = duration.match(/(\d+)\s*minute/);

  if (hourMatch) {
    totalMinutes += parseInt(hourMatch[1], 10) * 60;
  }
  if (minuteMatch) {
    totalMinutes += parseInt(minuteMatch[1], 10);
  }
  return totalMinutes;
}

export function ConvertToISOString(interviewAt: string): string {
  const dateTimeStr = interviewAt.split("-")[0].trim();
  const localDate = new Date(dateTimeStr);
  return localDate.toISOString();
}
