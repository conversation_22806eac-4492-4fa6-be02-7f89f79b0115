export const YesNoOptions: Array<{
  value: string | number | boolean;
  label: string;
}> = [
  { label: "Yes", value: true },
  { label: "No", value: false },
];

export const YesNoStringOptions: Array<{
  value: string | number | boolean;
  label: string;
}> = [
  { label: "Yes", value: "true" },
  { label: "No", value: "false" },
];

export const RatingOptions: Array<{
  value: string | number | boolean;
  label: string;
}> = [
  { label: "1", value: 1 },
  { label: "2", value: 2 },
  { label: "3", value: 3 },
  { label: "4", value: 4 },
  { label: "5", value: 5 },
];

export const InterviewRoundOptions: Array<{
  value: string | number;
  label: string;
}> = Array.from({ length: 20 }, (_, index) => ({
  label: `${index + 1}`,
  value: index + 1,
}));

export const InterviewModeOptions: Array<{
  value: string | number | boolean;
  label: string;
}> = [
  { label: "Video Call", value: 0 },
  { label: "Face to Face", value: 1 },
  { label: "Telephonic", value: 2 },
  { label: "AI Screening Round", value: 3 },
];

export const PercentageOptions: Array<{
  value: string | number;
  label: string;
}> = Array.from({ length: 20 }, (_, index) => ({
  label: `${(index + 1) * 5}`,
  value: (index + 1) * 5,
}));

// export const TimeDurationOptions: Array<{
//   value: string | number;
//   label: string;
// }> = [
//   { value: 5, label: "5 minutes" },
//   { value: 10, label: "10 minutes" },
//   { value: 15, label: "15 minutes" },
//   { value: 20, label: "20 minutes" },
//   { value: 25, label: "25 minutes" },
//   { value: 30, label: "30 minutes" },
//   { value: 35, label: "35 minutes" },
//   { value: 40, label: "40 minutes" },
//   { value: 45, label: "45 minutes" },
//   { value: 50, label: "50 minutes" },
//   { value: 55, label: "55 minutes" },
//   { value: 60, label: "1 hour" },
//   { value: 70, label: "1 hour 10 minutes" },
//   { value: 80, label: "1 hour 20 minutes" },
//   { value: 90, label: "1 hour 30 minutes" },
//   { value: 100, label: "1 hour 40 minutes" },
//   { value: 110, label: "1 hour 50 minutes" },
//   { value: 120, label: "2 hours" },
// ];

export const TimeDurationOptions: Array<{
  value: number;
  label: string;
}> = Array.from({ length: 106 }, (_, i) => {
  const minutes = i + 15; // starts at 15
  const hours = Math.floor(minutes / 60);
  const remainingMinutes = minutes % 60;

  let label = "";
  if (hours === 0) {
    label = `${minutes} minutes`;
  } else if (remainingMinutes === 0) {
    label = `${hours} hour${hours > 1 ? "s" : ""}`;
  } else {
    label = `${hours} hour${hours > 1 ? "s" : ""} ${remainingMinutes} minute${remainingMinutes > 9 ? "s" : ""}`;
  }

  return {
    value: minutes,
    label,
  };
});

export const ResumeSourceOptions: Array<{
  value: string | number;
  label: string;
}> = [
  { value: "Naukri.com", label: "Naukri.com" },
  { value: "Monster.com", label: "Monster.com" },
  { value: "Indeed", label: "Indeed" },
  { value: "LinkedIn", label: "LinkedIn" },
  { value: "Glassdoor", label: "Glassdoor" },
  { value: "CareerBuilder", label: "CareerBuilder" },
  { value: "Employee Referrals", label: "Employee Referrals" },
  { value: "Walk-In Applicants", label: "Walk-In Applicants" },
  { value: "Sovren", label: "Sovren" },
  { value: "Rchilli", label: "Rchilli" },
  { value: "Hireability", label: "Hireability" },
  { value: "Google Drive/Docs", label: "Google Drive/Docs" },
  { value: "Dropbox", label: "Dropbox" },
  { value: "Microsoft OneDrive", label: "Microsoft OneDrive" },
  { value: "Twitter/X", label: "Twitter/X" },
  { value: "GitHub", label: "GitHub" },
  { value: "Behance", label: "Behance" },
  { value: "Dribbble", label: "Dribbble" },
  { value: "Zoho Recruit", label: "Zoho Recruit" },
  { value: "Workable", label: "Workable" },
  { value: "Greenhouse", label: "Greenhouse" },
  { value: "Bullhorn", label: "Bullhorn" },
  { value: "Upwork", label: "Upwork" },
  { value: "Fiverr", label: "Fiverr" },
  { value: "Toptal", label: "Toptal" },
  { value: "Freelancer.com", label: "Freelancer.com" },
  { value: "Workday", label: "Workday" },
  { value: "SAP SuccessFactors", label: "SAP SuccessFactors" },
  { value: "BambooHR", label: "BambooHR" },
  { value: "ADP", label: "ADP" },
  { value: "Dice", label: "Dice" },
  {
    value: "Industry-Specific Platforms",
    label: "Industry-Specific Platforms",
  },
  {
    value: "University and College Placement Cells",
    label: "University and College Placement Cells",
  },
  { value: "Recruitment Agencies", label: "Recruitment Agencies" },
  { value: "Other", label: "Other" },
];

export const Timezones: Array<{
  label: string;
  value: string;
}> = [
  { label: "Africa/Abidjan +00:00 GMT", value: "Africa/Abidjan" },
  { label: "Africa/Accra +00:00 GMT", value: "Africa/Accra" },
  { label: "Africa/Addis_Ababa +03:00 GMT", value: "Africa/Addis_Ababa" },
  { label: "Africa/Algiers +01:00 GMT", value: "Africa/Algiers" },
  { label: "Africa/Asmara +03:00 GMT", value: "Africa/Asmara" },
  { label: "Africa/Bamako +00:00 GMT", value: "Africa/Bamako" },
  { label: "Africa/Bangui +01:00 GMT", value: "Africa/Bangui" },
  { label: "Africa/Banjul +00:00 GMT", value: "Africa/Banjul" },
  { label: "Africa/Bissau +00:00 GMT", value: "Africa/Bissau" },
  { label: "Africa/Blantyre +02:00 GMT", value: "Africa/Blantyre" },
  { label: "Africa/Brazzaville +01:00 GMT", value: "Africa/Brazzaville" },
  { label: "Africa/Bujumbura +02:00 GMT", value: "Africa/Bujumbura" },
  { label: "Africa/Cairo +03:00 GMT", value: "Africa/Cairo" },
  { label: "Africa/Casablanca +01:00 GMT", value: "Africa/Casablanca" },
  { label: "Africa/Ceuta +02:00 GMT", value: "Africa/Ceuta" },
  { label: "Africa/Conakry +00:00 GMT", value: "Africa/Conakry" },
  { label: "Africa/Dakar +00:00 GMT", value: "Africa/Dakar" },
  { label: "Africa/Dar_es_Salaam +03:00 GMT", value: "Africa/Dar_es_Salaam" },
  { label: "Africa/Djibouti +03:00 GMT", value: "Africa/Djibouti" },
  { label: "Africa/Douala +01:00 GMT", value: "Africa/Douala" },
  { label: "Africa/El_Aaiun +01:00 GMT", value: "Africa/El_Aaiun" },
  { label: "Africa/Freetown +00:00 GMT", value: "Africa/Freetown" },
  { label: "Africa/Gaborone +02:00 GMT", value: "Africa/Gaborone" },
  { label: "Africa/Harare +02:00 GMT", value: "Africa/Harare" },
  { label: "Africa/Johannesburg +02:00 GMT", value: "Africa/Johannesburg" },
  { label: "Africa/Juba +02:00 GMT", value: "Africa/Juba" },
  { label: "Africa/Kampala +03:00 GMT", value: "Africa/Kampala" },
  { label: "Africa/Khartoum +02:00 GMT", value: "Africa/Khartoum" },
  { label: "Africa/Kigali +02:00 GMT", value: "Africa/Kigali" },
  { label: "Africa/Kinshasa +01:00 GMT", value: "Africa/Kinshasa" },
  { label: "Africa/Lagos +01:00 GMT", value: "Africa/Lagos" },
  { label: "Africa/Libreville +01:00 GMT", value: "Africa/Libreville" },
  { label: "Africa/Lome +00:00 GMT", value: "Africa/Lome" },
  { label: "Africa/Luanda +01:00 GMT", value: "Africa/Luanda" },
  { label: "Africa/Lubumbashi +02:00 GMT", value: "Africa/Lubumbashi" },
  { label: "Africa/Lusaka +02:00 GMT", value: "Africa/Lusaka" },
  { label: "Africa/Malabo +01:00 GMT", value: "Africa/Malabo" },
  { label: "Africa/Maputo +02:00 GMT", value: "Africa/Maputo" },
  { label: "Africa/Maseru +02:00 GMT", value: "Africa/Maseru" },
  { label: "Africa/Mbabane +02:00 GMT", value: "Africa/Mbabane" },
  { label: "Africa/Mogadishu +03:00 GMT", value: "Africa/Mogadishu" },
  { label: "Africa/Monrovia +00:00 GMT", value: "Africa/Monrovia" },
  { label: "Africa/Nairobi +03:00 GMT", value: "Africa/Nairobi" },
  { label: "Africa/Ndjamena +01:00 GMT", value: "Africa/Ndjamena" },
  { label: "Africa/Niamey +01:00 GMT", value: "Africa/Niamey" },
  { label: "Africa/Nouakchott +00:00 GMT", value: "Africa/Nouakchott" },
  { label: "Africa/Ouagadougou +00:00 GMT", value: "Africa/Ouagadougou" },
  { label: "Africa/Porto-Novo +01:00 GMT", value: "Africa/Porto-Novo" },
  { label: "Africa/Sao_Tome +00:00 GMT", value: "Africa/Sao_Tome" },
  { label: "Africa/Tripoli +02:00 GMT", value: "Africa/Tripoli" },
  { label: "Africa/Tunis +01:00 GMT", value: "Africa/Tunis" },
  { label: "Africa/Windhoek +02:00 GMT", value: "Africa/Windhoek" },
  { label: "America/Adak -09:00 GMT", value: "America/Adak" },
  { label: "America/Anchorage -08:00 GMT", value: "America/Anchorage" },
  { label: "America/Anguilla -04:00 GMT", value: "America/Anguilla" },
  { label: "America/Antigua -04:00 GMT", value: "America/Antigua" },
  { label: "America/Araguaina -03:00 GMT", value: "America/Araguaina" },
  {
    label: "America/Argentina/Buenos_Aires -03:00 GMT",
    value: "America/Argentina/Buenos_Aires",
  },
  {
    label: "America/Argentina/Catamarca -03:00 GMT",
    value: "America/Argentina/Catamarca",
  },
  {
    label: "America/Argentina/Cordoba -03:00 GMT",
    value: "America/Argentina/Cordoba",
  },
  {
    label: "America/Argentina/Jujuy -03:00 GMT",
    value: "America/Argentina/Jujuy",
  },
  {
    label: "America/Argentina/La_Rioja -03:00 GMT",
    value: "America/Argentina/La_Rioja",
  },
  {
    label: "America/Argentina/Mendoza -03:00 GMT",
    value: "America/Argentina/Mendoza",
  },
  {
    label: "America/Argentina/Rio_Gallegos -03:00 GMT",
    value: "America/Argentina/Rio_Gallegos",
  },
  {
    label: "America/Argentina/Salta -03:00 GMT",
    value: "America/Argentina/Salta",
  },
  {
    label: "America/Argentina/San_Juan -03:00 GMT",
    value: "America/Argentina/San_Juan",
  },
  {
    label: "America/Argentina/San_Luis -03:00 GMT",
    value: "America/Argentina/San_Luis",
  },
  {
    label: "America/Argentina/Tucuman -03:00 GMT",
    value: "America/Argentina/Tucuman",
  },
  {
    label: "America/Argentina/Ushuaia -03:00 GMT",
    value: "America/Argentina/Ushuaia",
  },
  { label: "America/Aruba -04:00 GMT", value: "America/Aruba" },
  { label: "America/Asuncion -04:00 GMT", value: "America/Asuncion" },
  { label: "America/Atikokan -05:00 GMT", value: "America/Atikokan" },
  { label: "America/Bahia -03:00 GMT", value: "America/Bahia" },
  {
    label: "America/Bahia_Banderas -06:00 GMT",
    value: "America/Bahia_Banderas",
  },
  { label: "America/Barbados -04:00 GMT", value: "America/Barbados" },
  { label: "America/Belem -03:00 GMT", value: "America/Belem" },
  { label: "America/Belize -06:00 GMT", value: "America/Belize" },
  { label: "America/Blanc-Sablon -04:00 GMT", value: "America/Blanc-Sablon" },
  { label: "America/Boa_Vista -04:00 GMT", value: "America/Boa_Vista" },
  { label: "America/Bogota -05:00 GMT", value: "America/Bogota" },
  { label: "America/Boise -06:00 GMT", value: "America/Boise" },
  { label: "America/Cambridge_Bay -06:00 GMT", value: "America/Cambridge_Bay" },
  { label: "America/Campo_Grande -04:00 GMT", value: "America/Campo_Grande" },
  { label: "America/Cancun -05:00 GMT", value: "America/Cancun" },
  { label: "America/Caracas -04:00 GMT", value: "America/Caracas" },
  { label: "America/Cayenne -03:00 GMT", value: "America/Cayenne" },
  { label: "America/Cayman -05:00 GMT", value: "America/Cayman" },
  { label: "America/Chicago -05:00 GMT", value: "America/Chicago" },
  { label: "America/Chihuahua -06:00 GMT", value: "America/Chihuahua" },
  { label: "America/Ciudad_Juarez -06:00 GMT", value: "America/Ciudad_Juarez" },
  { label: "America/Costa_Rica -06:00 GMT", value: "America/Costa_Rica" },
  { label: "America/Creston -07:00 GMT", value: "America/Creston" },
  { label: "America/Cuiaba -04:00 GMT", value: "America/Cuiaba" },
  { label: "America/Curacao -04:00 GMT", value: "America/Curacao" },
  { label: "America/Danmarkshavn +00:00 GMT", value: "America/Danmarkshavn" },
  { label: "America/Dawson -07:00 GMT", value: "America/Dawson" },
  { label: "America/Dawson_Creek -07:00 GMT", value: "America/Dawson_Creek" },
  { label: "America/Denver -06:00 GMT", value: "America/Denver" },
  { label: "America/Detroit -04:00 GMT", value: "America/Detroit" },
  { label: "America/Dominica -04:00 GMT", value: "America/Dominica" },
  { label: "America/Edmonton -06:00 GMT", value: "America/Edmonton" },
  { label: "America/Eirunepe -05:00 GMT", value: "America/Eirunepe" },
  { label: "America/El_Salvador -06:00 GMT", value: "America/El_Salvador" },
  { label: "America/Fort_Nelson -07:00 GMT", value: "America/Fort_Nelson" },
  { label: "America/Fortaleza -03:00 GMT", value: "America/Fortaleza" },
  { label: "America/Glace_Bay -03:00 GMT", value: "America/Glace_Bay" },
  { label: "America/Goose_Bay -03:00 GMT", value: "America/Goose_Bay" },
  { label: "America/Grand_Turk -04:00 GMT", value: "America/Grand_Turk" },
  { label: "America/Grenada -04:00 GMT", value: "America/Grenada" },
  { label: "America/Guadeloupe -04:00 GMT", value: "America/Guadeloupe" },
  { label: "America/Guatemala -06:00 GMT", value: "America/Guatemala" },
  { label: "America/Guayaquil -05:00 GMT", value: "America/Guayaquil" },
  { label: "America/Guyana -04:00 GMT", value: "America/Guyana" },
  { label: "America/Halifax -03:00 GMT", value: "America/Halifax" },
  { label: "America/Havana -04:00 GMT", value: "America/Havana" },
  { label: "America/Hermosillo -07:00 GMT", value: "America/Hermosillo" },
  {
    label: "America/Indiana/Indianapolis -04:00 GMT",
    value: "America/Indiana/Indianapolis",
  },
  { label: "America/Indiana/Knox -05:00 GMT", value: "America/Indiana/Knox" },
  {
    label: "America/Indiana/Marengo -04:00 GMT",
    value: "America/Indiana/Marengo",
  },
  {
    label: "America/Indiana/Petersburg -04:00 GMT",
    value: "America/Indiana/Petersburg",
  },
  {
    label: "America/Indiana/Tell_City -05:00 GMT",
    value: "America/Indiana/Tell_City",
  },
  { label: "America/Indiana/Vevay -04:00 GMT", value: "America/Indiana/Vevay" },
  {
    label: "America/Indiana/Vincennes -04:00 GMT",
    value: "America/Indiana/Vincennes",
  },
  {
    label: "America/Indiana/Winamac -04:00 GMT",
    value: "America/Indiana/Winamac",
  },
  { label: "America/Inuvik -06:00 GMT", value: "America/Inuvik" },
  { label: "America/Iqaluit -04:00 GMT", value: "America/Iqaluit" },
  { label: "America/Jamaica -05:00 GMT", value: "America/Jamaica" },
  { label: "America/Juneau -08:00 GMT", value: "America/Juneau" },
  {
    label: "America/Kentucky/Louisville -04:00 GMT",
    value: "America/Kentucky/Louisville",
  },
  {
    label: "America/Kentucky/Monticello -04:00 GMT",
    value: "America/Kentucky/Monticello",
  },
  { label: "America/Kralendijk -04:00 GMT", value: "America/Kralendijk" },
  { label: "America/La_Paz -04:00 GMT", value: "America/La_Paz" },
  { label: "America/Lima -05:00 GMT", value: "America/Lima" },
  { label: "America/Los_Angeles -07:00 GMT", value: "America/Los_Angeles" },
  { label: "America/Lower_Princes -04:00 GMT", value: "America/Lower_Princes" },
  { label: "America/Maceio -03:00 GMT", value: "America/Maceio" },
  { label: "America/Managua -06:00 GMT", value: "America/Managua" },
  { label: "America/Manaus -04:00 GMT", value: "America/Manaus" },
  { label: "America/Marigot -04:00 GMT", value: "America/Marigot" },
  { label: "America/Martinique -04:00 GMT", value: "America/Martinique" },
  { label: "America/Matamoros -05:00 GMT", value: "America/Matamoros" },
  { label: "America/Mazatlan -07:00 GMT", value: "America/Mazatlan" },
  { label: "America/Menominee -05:00 GMT", value: "America/Menominee" },
  { label: "America/Merida -06:00 GMT", value: "America/Merida" },
  { label: "America/Metlakatla -08:00 GMT", value: "America/Metlakatla" },
  { label: "America/Mexico_City -06:00 GMT", value: "America/Mexico_City" },
  { label: "America/Miquelon -02:00 GMT", value: "America/Miquelon" },
  { label: "America/Moncton -03:00 GMT", value: "America/Moncton" },
  { label: "America/Monterrey -06:00 GMT", value: "America/Monterrey" },
  { label: "America/Montevideo -03:00 GMT", value: "America/Montevideo" },
  { label: "America/Montserrat -04:00 GMT", value: "America/Montserrat" },
  { label: "America/Nassau -04:00 GMT", value: "America/Nassau" },
  { label: "America/New_York -04:00 GMT", value: "America/New_York" },
  { label: "America/Nome -08:00 GMT", value: "America/Nome" },
  { label: "America/Noronha -02:00 GMT", value: "America/Noronha" },
  {
    label: "America/North_Dakota/Beulah -05:00 GMT",
    value: "America/North_Dakota/Beulah",
  },
  {
    label: "America/North_Dakota/Center -05:00 GMT",
    value: "America/North_Dakota/Center",
  },
  {
    label: "America/North_Dakota/New_Salem -05:00 GMT",
    value: "America/North_Dakota/New_Salem",
  },
  { label: "America/Nuuk -01:00 GMT", value: "America/Nuuk" },
  { label: "America/Ojinaga -05:00 GMT", value: "America/Ojinaga" },
  { label: "America/Panama -05:00 GMT", value: "America/Panama" },
  { label: "America/Paramaribo -03:00 GMT", value: "America/Paramaribo" },
  { label: "America/Phoenix -07:00 GMT", value: "America/Phoenix" },
  {
    label: "America/Port-au-Prince -04:00 GMT",
    value: "America/Port-au-Prince",
  },
  { label: "America/Port_of_Spain -04:00 GMT", value: "America/Port_of_Spain" },
  { label: "America/Porto_Velho -04:00 GMT", value: "America/Porto_Velho" },
  { label: "America/Puerto_Rico -04:00 GMT", value: "America/Puerto_Rico" },
  { label: "America/Punta_Arenas -03:00 GMT", value: "America/Punta_Arenas" },
  { label: "America/Rankin_Inlet -05:00 GMT", value: "America/Rankin_Inlet" },
  { label: "America/Recife -03:00 GMT", value: "America/Recife" },
  { label: "America/Regina -06:00 GMT", value: "America/Regina" },
  { label: "America/Resolute -05:00 GMT", value: "America/Resolute" },
  { label: "America/Rio_Branco -05:00 GMT", value: "America/Rio_Branco" },
  { label: "America/Santarem -03:00 GMT", value: "America/Santarem" },
  { label: "America/Santiago -04:00 GMT", value: "America/Santiago" },
  { label: "America/Santo_Domingo -04:00 GMT", value: "America/Santo_Domingo" },
  { label: "America/Sao_Paulo -03:00 GMT", value: "America/Sao_Paulo" },
  { label: "America/Scoresbysund +00:00 GMT", value: "America/Scoresbysund" },
  { label: "America/Sitka -08:00 GMT", value: "America/Sitka" },
  { label: "America/St_Barthelemy -04:00 GMT", value: "America/St_Barthelemy" },
  { label: "America/St_Johns -03:30 GMT", value: "America/St_Johns" },
  { label: "America/St_Kitts -04:00 GMT", value: "America/St_Kitts" },
  { label: "America/St_Lucia -04:00 GMT", value: "America/St_Lucia" },
  { label: "America/St_Thomas -04:00 GMT", value: "America/St_Thomas" },
  { label: "America/St_Vincent -04:00 GMT", value: "America/St_Vincent" },
  { label: "America/Swift_Current -06:00 GMT", value: "America/Swift_Current" },
  { label: "America/Tegucigalpa -06:00 GMT", value: "America/Tegucigalpa" },
  { label: "America/Thule -03:00 GMT", value: "America/Thule" },
  { label: "America/Tijuana -07:00 GMT", value: "America/Tijuana" },
  { label: "America/Toronto -04:00 GMT", value: "America/Toronto" },
  { label: "America/Tortola -04:00 GMT", value: "America/Tortola" },
  { label: "America/Vancouver -07:00 GMT", value: "America/Vancouver" },
  { label: "America/Whitehorse -07:00 GMT", value: "America/Whitehorse" },
  { label: "America/Winnipeg -05:00 GMT", value: "America/Winnipeg" },
  { label: "America/Yakutat -08:00 GMT", value: "America/Yakutat" },
  { label: "Antarctica/Casey +11:00 GMT", value: "Antarctica/Casey" },
  { label: "Antarctica/Davis +07:00 GMT", value: "Antarctica/Davis" },
  {
    label: "Antarctica/DumontDUrville +10:00 GMT",
    value: "Antarctica/DumontDUrville",
  },
  { label: "Antarctica/Macquarie +10:00 GMT", value: "Antarctica/Macquarie" },
  { label: "Antarctica/Mawson +05:00 GMT", value: "Antarctica/Mawson" },
  { label: "Antarctica/McMurdo +12:00 GMT", value: "Antarctica/McMurdo" },
  { label: "Antarctica/Palmer -03:00 GMT", value: "Antarctica/Palmer" },
  { label: "Antarctica/Rothera -03:00 GMT", value: "Antarctica/Rothera" },
  { label: "Antarctica/Syowa +03:00 GMT", value: "Antarctica/Syowa" },
  { label: "Antarctica/Troll +02:00 GMT", value: "Antarctica/Troll" },
  { label: "Antarctica/Vostok +06:00 GMT", value: "Antarctica/Vostok" },
  { label: "Arctic/Longyearbyen +02:00 GMT", value: "Arctic/Longyearbyen" },
  { label: "Asia/Aden +03:00 GMT", value: "Asia/Aden" },
  { label: "Asia/Almaty +06:00 GMT", value: "Asia/Almaty" },
  { label: "Asia/Amman +03:00 GMT", value: "Asia/Amman" },
  { label: "Asia/Anadyr +12:00 GMT", value: "Asia/Anadyr" },
  { label: "Asia/Aqtau +05:00 GMT", value: "Asia/Aqtau" },
  { label: "Asia/Aqtobe +05:00 GMT", value: "Asia/Aqtobe" },
  { label: "Asia/Ashgabat +05:00 GMT", value: "Asia/Ashgabat" },
  { label: "Asia/Atyrau +05:00 GMT", value: "Asia/Atyrau" },
  { label: "Asia/Baghdad +03:00 GMT", value: "Asia/Baghdad" },
  { label: "Asia/Bahrain +03:00 GMT", value: "Asia/Bahrain" },
  { label: "Asia/Baku +04:00 GMT", value: "Asia/Baku" },
  { label: "Asia/Bangkok +07:00 GMT", value: "Asia/Bangkok" },
  { label: "Asia/Barnaul +07:00 GMT", value: "Asia/Barnaul" },
  { label: "Asia/Beirut +03:00 GMT", value: "Asia/Beirut" },
  { label: "Asia/Bishkek +06:00 GMT", value: "Asia/Bishkek" },
  { label: "Asia/Brunei +08:00 GMT", value: "Asia/Brunei" },
  { label: "Asia/Chita +09:00 GMT", value: "Asia/Chita" },
  { label: "Asia/Choibalsan +08:00 GMT", value: "Asia/Choibalsan" },
  { label: "Asia/Colombo +05:30 GMT", value: "Asia/Colombo" },
  { label: "Asia/Damascus +03:00 GMT", value: "Asia/Damascus" },
  { label: "Asia/Dhaka +06:00 GMT", value: "Asia/Dhaka" },
  { label: "Asia/Dili +09:00 GMT", value: "Asia/Dili" },
  { label: "Asia/Dubai +04:00 GMT", value: "Asia/Dubai" },
  { label: "Asia/Dushanbe +05:00 GMT", value: "Asia/Dushanbe" },
  { label: "Asia/Famagusta +03:00 GMT", value: "Asia/Famagusta" },
  { label: "Asia/Gaza +03:00 GMT", value: "Asia/Gaza" },
  { label: "Asia/Hebron +03:00 GMT", value: "Asia/Hebron" },
  { label: "Asia/Ho_Chi_Minh +07:00 GMT", value: "Asia/Ho_Chi_Minh" },
  { label: "Asia/Hong_Kong +08:00 GMT", value: "Asia/Hong_Kong" },
  { label: "Asia/Hovd +07:00 GMT", value: "Asia/Hovd" },
  { label: "Asia/Irkutsk +08:00 GMT", value: "Asia/Irkutsk" },
  { label: "Asia/Jakarta +07:00 GMT", value: "Asia/Jakarta" },
  { label: "Asia/Jayapura +09:00 GMT", value: "Asia/Jayapura" },
  { label: "Asia/Jerusalem +03:00 GMT", value: "Asia/Jerusalem" },
  { label: "Asia/Kabul +04:30 GMT", value: "Asia/Kabul" },
  { label: "Asia/Kamchatka +12:00 GMT", value: "Asia/Kamchatka" },
  { label: "Asia/Karachi +05:00 GMT", value: "Asia/Karachi" },
  { label: "Asia/Kathmandu +05:45 GMT", value: "Asia/Kathmandu" },
  { label: "Asia/Khandyga +09:00 GMT", value: "Asia/Khandyga" },
  { label: "Asia/Kolkata +05:30 GMT", value: "Asia/Kolkata" },
  { label: "Asia/Krasnoyarsk +07:00 GMT", value: "Asia/Krasnoyarsk" },
  { label: "Asia/Kuala_Lumpur +08:00 GMT", value: "Asia/Kuala_Lumpur" },
  { label: "Asia/Kuching +08:00 GMT", value: "Asia/Kuching" },
  { label: "Asia/Kuwait +03:00 GMT", value: "Asia/Kuwait" },
  { label: "Asia/Macau +08:00 GMT", value: "Asia/Macau" },
  { label: "Asia/Magadan +11:00 GMT", value: "Asia/Magadan" },
  { label: "Asia/Makassar +08:00 GMT", value: "Asia/Makassar" },
  { label: "Asia/Manila +08:00 GMT", value: "Asia/Manila" },
  { label: "Asia/Muscat +04:00 GMT", value: "Asia/Muscat" },
  { label: "Asia/Nicosia +03:00 GMT", value: "Asia/Nicosia" },
  { label: "Asia/Novokuznetsk +07:00 GMT", value: "Asia/Novokuznetsk" },
  { label: "Asia/Novosibirsk +07:00 GMT", value: "Asia/Novosibirsk" },
  { label: "Asia/Omsk +06:00 GMT", value: "Asia/Omsk" },
  { label: "Asia/Oral +05:00 GMT", value: "Asia/Oral" },
  { label: "Asia/Phnom_Penh +07:00 GMT", value: "Asia/Phnom_Penh" },
  { label: "Asia/Pontianak +07:00 GMT", value: "Asia/Pontianak" },
  { label: "Asia/Pyongyang +09:00 GMT", value: "Asia/Pyongyang" },
  { label: "Asia/Qatar +03:00 GMT", value: "Asia/Qatar" },
  { label: "Asia/Qostanay +06:00 GMT", value: "Asia/Qostanay" },
  { label: "Asia/Qyzylorda +05:00 GMT", value: "Asia/Qyzylorda" },
  { label: "Asia/Riyadh +03:00 GMT", value: "Asia/Riyadh" },
  { label: "Asia/Sakhalin +11:00 GMT", value: "Asia/Sakhalin" },
  { label: "Asia/Samarkand +05:00 GMT", value: "Asia/Samarkand" },
  { label: "Asia/Seoul +09:00 GMT", value: "Asia/Seoul" },
  { label: "Asia/Shanghai +08:00 GMT", value: "Asia/Shanghai" },
  { label: "Asia/Singapore +08:00 GMT", value: "Asia/Singapore" },
  { label: "Asia/Srednekolymsk +11:00 GMT", value: "Asia/Srednekolymsk" },
  { label: "Asia/Taipei +08:00 GMT", value: "Asia/Taipei" },
  { label: "Asia/Tashkent +05:00 GMT", value: "Asia/Tashkent" },
  { label: "Asia/Tbilisi +04:00 GMT", value: "Asia/Tbilisi" },
  { label: "Asia/Tehran +03:30 GMT", value: "Asia/Tehran" },
  { label: "Asia/Thimphu +06:00 GMT", value: "Asia/Thimphu" },
  { label: "Asia/Tokyo +09:00 GMT", value: "Asia/Tokyo" },
  { label: "Asia/Tomsk +07:00 GMT", value: "Asia/Tomsk" },
  { label: "Asia/Ulaanbaatar +08:00 GMT", value: "Asia/Ulaanbaatar" },
  { label: "Asia/Urumqi +06:00 GMT", value: "Asia/Urumqi" },
  { label: "Asia/Ust-Nera +10:00 GMT", value: "Asia/Ust-Nera" },
  { label: "Asia/Vientiane +07:00 GMT", value: "Asia/Vientiane" },
  { label: "Asia/Vladivostok +10:00 GMT", value: "Asia/Vladivostok" },
  { label: "Asia/Yakutsk +09:00 GMT", value: "Asia/Yakutsk" },
  { label: "Asia/Yangon +06:30 GMT", value: "Asia/Yangon" },
  { label: "Asia/Yekaterinburg +05:00 GMT", value: "Asia/Yekaterinburg" },
  { label: "Asia/Yerevan +04:00 GMT", value: "Asia/Yerevan" },
  { label: "Atlantic/Azores +00:00 GMT", value: "Atlantic/Azores" },
  { label: "Atlantic/Bermuda -03:00 GMT", value: "Atlantic/Bermuda" },
  { label: "Atlantic/Canary +01:00 GMT", value: "Atlantic/Canary" },
  { label: "Atlantic/Cape_Verde -01:00 GMT", value: "Atlantic/Cape_Verde" },
  { label: "Atlantic/Faroe +01:00 GMT", value: "Atlantic/Faroe" },
  { label: "Atlantic/Madeira +01:00 GMT", value: "Atlantic/Madeira" },
  { label: "Atlantic/Reykjavik +00:00 GMT", value: "Atlantic/Reykjavik" },
  {
    label: "Atlantic/South_Georgia -02:00 GMT",
    value: "Atlantic/South_Georgia",
  },
  { label: "Atlantic/St_Helena +00:00 GMT", value: "Atlantic/St_Helena" },
  { label: "Atlantic/Stanley -03:00 GMT", value: "Atlantic/Stanley" },
  { label: "Australia/Adelaide +09:30 GMT", value: "Australia/Adelaide" },
  { label: "Australia/Brisbane +10:00 GMT", value: "Australia/Brisbane" },
  { label: "Australia/Broken_Hill +09:30 GMT", value: "Australia/Broken_Hill" },
  { label: "Australia/Darwin +09:30 GMT", value: "Australia/Darwin" },
  { label: "Australia/Eucla +08:45 GMT", value: "Australia/Eucla" },
  { label: "Australia/Hobart +10:00 GMT", value: "Australia/Hobart" },
  { label: "Australia/Lindeman +10:00 GMT", value: "Australia/Lindeman" },
  { label: "Australia/Lord_Howe +10:30 GMT", value: "Australia/Lord_Howe" },
  { label: "Australia/Melbourne +10:00 GMT", value: "Australia/Melbourne" },
  { label: "Australia/Perth +08:00 GMT", value: "Australia/Perth" },
  { label: "Australia/Sydney +10:00 GMT", value: "Australia/Sydney" },
  { label: "Canada/Atlantic -03:00 GMT", value: "Canada/Atlantic" },
  { label: "Canada/Central -05:00 GMT", value: "Canada/Central" },
  { label: "Canada/Eastern -04:00 GMT", value: "Canada/Eastern" },
  { label: "Canada/Mountain -06:00 GMT", value: "Canada/Mountain" },
  { label: "Canada/Newfoundland -03:30 GMT", value: "Canada/Newfoundland" },
  { label: "Canada/Pacific -07:00 GMT", value: "Canada/Pacific" },
  { label: "Europe/Amsterdam +02:00 GMT", value: "Europe/Amsterdam" },
  { label: "Europe/Andorra +02:00 GMT", value: "Europe/Andorra" },
  { label: "Europe/Astrakhan +04:00 GMT", value: "Europe/Astrakhan" },
  { label: "Europe/Athens +03:00 GMT", value: "Europe/Athens" },
  { label: "Europe/Belgrade +02:00 GMT", value: "Europe/Belgrade" },
  { label: "Europe/Berlin +02:00 GMT", value: "Europe/Berlin" },
  { label: "Europe/Bratislava +02:00 GMT", value: "Europe/Bratislava" },
  { label: "Europe/Brussels +02:00 GMT", value: "Europe/Brussels" },
  { label: "Europe/Bucharest +03:00 GMT", value: "Europe/Bucharest" },
  { label: "Europe/Budapest +02:00 GMT", value: "Europe/Budapest" },
  { label: "Europe/Busingen +02:00 GMT", value: "Europe/Busingen" },
  { label: "Europe/Chisinau +03:00 GMT", value: "Europe/Chisinau" },
  { label: "Europe/Copenhagen +02:00 GMT", value: "Europe/Copenhagen" },
  { label: "Europe/Dublin +01:00 GMT", value: "Europe/Dublin" },
  { label: "Europe/Gibraltar +02:00 GMT", value: "Europe/Gibraltar" },
  { label: "Europe/Guernsey +01:00 GMT", value: "Europe/Guernsey" },
  { label: "Europe/Helsinki +03:00 GMT", value: "Europe/Helsinki" },
  { label: "Europe/Isle_of_Man +01:00 GMT", value: "Europe/Isle_of_Man" },
  { label: "Europe/Istanbul +03:00 GMT", value: "Europe/Istanbul" },
  { label: "Europe/Jersey +01:00 GMT", value: "Europe/Jersey" },
  { label: "Europe/Kaliningrad +02:00 GMT", value: "Europe/Kaliningrad" },
  { label: "Europe/Kirov +03:00 GMT", value: "Europe/Kirov" },
  { label: "Europe/Kyiv +03:00 GMT", value: "Europe/Kyiv" },
  { label: "Europe/Lisbon +01:00 GMT", value: "Europe/Lisbon" },
  { label: "Europe/Ljubljana +02:00 GMT", value: "Europe/Ljubljana" },
  { label: "Europe/London +01:00 GMT", value: "Europe/London" },
  { label: "Europe/Luxembourg +02:00 GMT", value: "Europe/Luxembourg" },
  { label: "Europe/Madrid +02:00 GMT", value: "Europe/Madrid" },
  { label: "Europe/Malta +02:00 GMT", value: "Europe/Malta" },
  { label: "Europe/Mariehamn +03:00 GMT", value: "Europe/Mariehamn" },
  { label: "Europe/Minsk +03:00 GMT", value: "Europe/Minsk" },
  { label: "Europe/Monaco +02:00 GMT", value: "Europe/Monaco" },
  { label: "Europe/Moscow +03:00 GMT", value: "Europe/Moscow" },
  { label: "Europe/Oslo +02:00 GMT", value: "Europe/Oslo" },
  { label: "Europe/Paris +02:00 GMT", value: "Europe/Paris" },
  { label: "Europe/Podgorica +02:00 GMT", value: "Europe/Podgorica" },
  { label: "Europe/Prague +02:00 GMT", value: "Europe/Prague" },
  { label: "Europe/Riga +03:00 GMT", value: "Europe/Riga" },
  { label: "Europe/Rome +02:00 GMT", value: "Europe/Rome" },
  { label: "Europe/Samara +04:00 GMT", value: "Europe/Samara" },
  { label: "Europe/San_Marino +02:00 GMT", value: "Europe/San_Marino" },
  { label: "Europe/Sarajevo +02:00 GMT", value: "Europe/Sarajevo" },
  { label: "Europe/Saratov +04:00 GMT", value: "Europe/Saratov" },
  { label: "Europe/Simferopol +03:00 GMT", value: "Europe/Simferopol" },
  { label: "Europe/Skopje +02:00 GMT", value: "Europe/Skopje" },
  { label: "Europe/Sofia +03:00 GMT", value: "Europe/Sofia" },
  { label: "Europe/Stockholm +02:00 GMT", value: "Europe/Stockholm" },
  { label: "Europe/Tallinn +03:00 GMT", value: "Europe/Tallinn" },
  { label: "Europe/Tirane +02:00 GMT", value: "Europe/Tirane" },
  { label: "Europe/Ulyanovsk +04:00 GMT", value: "Europe/Ulyanovsk" },
  { label: "Europe/Vaduz +02:00 GMT", value: "Europe/Vaduz" },
  { label: "Europe/Vatican +02:00 GMT", value: "Europe/Vatican" },
  { label: "Europe/Vienna +02:00 GMT", value: "Europe/Vienna" },
  { label: "Europe/Vilnius +03:00 GMT", value: "Europe/Vilnius" },
  { label: "Europe/Volgograd +03:00 GMT", value: "Europe/Volgograd" },
  { label: "Europe/Warsaw +02:00 GMT", value: "Europe/Warsaw" },
  { label: "Europe/Zagreb +02:00 GMT", value: "Europe/Zagreb" },
  { label: "Europe/Zurich +02:00 GMT", value: "Europe/Zurich" },
  { label: "GMT +00:00 GMT", value: "GMT" },
  { label: "Indian/Antananarivo +03:00 GMT", value: "Indian/Antananarivo" },
  { label: "Indian/Chagos +06:00 GMT", value: "Indian/Chagos" },
  { label: "Indian/Christmas +07:00 GMT", value: "Indian/Christmas" },
  { label: "Indian/Cocos +06:30 GMT", value: "Indian/Cocos" },
  { label: "Indian/Comoro +03:00 GMT", value: "Indian/Comoro" },
  { label: "Indian/Kerguelen +05:00 GMT", value: "Indian/Kerguelen" },
  { label: "Indian/Mahe +04:00 GMT", value: "Indian/Mahe" },
  { label: "Indian/Maldives +05:00 GMT", value: "Indian/Maldives" },
  { label: "Indian/Mauritius +04:00 GMT", value: "Indian/Mauritius" },
  { label: "Indian/Mayotte +03:00 GMT", value: "Indian/Mayotte" },
  { label: "Indian/Reunion +04:00 GMT", value: "Indian/Reunion" },
  { label: "Pacific/Apia +13:00 GMT", value: "Pacific/Apia" },
  { label: "Pacific/Auckland +12:00 GMT", value: "Pacific/Auckland" },
  { label: "Pacific/Bougainville +11:00 GMT", value: "Pacific/Bougainville" },
  { label: "Pacific/Chatham +12:45 GMT", value: "Pacific/Chatham" },
  { label: "Pacific/Chuuk +10:00 GMT", value: "Pacific/Chuuk" },
  { label: "Pacific/Easter -06:00 GMT", value: "Pacific/Easter" },
  { label: "Pacific/Efate +11:00 GMT", value: "Pacific/Efate" },
  { label: "Pacific/Fakaofo +13:00 GMT", value: "Pacific/Fakaofo" },
  { label: "Pacific/Fiji +12:00 GMT", value: "Pacific/Fiji" },
  { label: "Pacific/Funafuti +12:00 GMT", value: "Pacific/Funafuti" },
  { label: "Pacific/Galapagos -06:00 GMT", value: "Pacific/Galapagos" },
  { label: "Pacific/Gambier -09:00 GMT", value: "Pacific/Gambier" },
  { label: "Pacific/Guadalcanal +11:00 GMT", value: "Pacific/Guadalcanal" },
  { label: "Pacific/Guam +10:00 GMT", value: "Pacific/Guam" },
  { label: "Pacific/Honolulu -10:00 GMT", value: "Pacific/Honolulu" },
  { label: "Pacific/Kanton +13:00 GMT", value: "Pacific/Kanton" },
  { label: "Pacific/Kiritimati +14:00 GMT", value: "Pacific/Kiritimati" },
  { label: "Pacific/Kosrae +11:00 GMT", value: "Pacific/Kosrae" },
  { label: "Pacific/Kwajalein +12:00 GMT", value: "Pacific/Kwajalein" },
  { label: "Pacific/Majuro +12:00 GMT", value: "Pacific/Majuro" },
  { label: "Pacific/Marquesas -10:30 GMT", value: "Pacific/Marquesas" },
  { label: "Pacific/Midway -11:00 GMT", value: "Pacific/Midway" },
  { label: "Pacific/Nauru +12:00 GMT", value: "Pacific/Nauru" },
  { label: "Pacific/Niue -11:00 GMT", value: "Pacific/Niue" },
  { label: "Pacific/Norfolk +11:00 GMT", value: "Pacific/Norfolk" },
  { label: "Pacific/Noumea +11:00 GMT", value: "Pacific/Noumea" },
  { label: "Pacific/Pago_Pago -11:00 GMT", value: "Pacific/Pago_Pago" },
  { label: "Pacific/Palau +09:00 GMT", value: "Pacific/Palau" },
  { label: "Pacific/Pitcairn -08:00 GMT", value: "Pacific/Pitcairn" },
  { label: "Pacific/Pohnpei +11:00 GMT", value: "Pacific/Pohnpei" },
  { label: "Pacific/Port_Moresby +10:00 GMT", value: "Pacific/Port_Moresby" },
  { label: "Pacific/Rarotonga -10:00 GMT", value: "Pacific/Rarotonga" },
  { label: "Pacific/Saipan +10:00 GMT", value: "Pacific/Saipan" },
  { label: "Pacific/Tahiti -10:00 GMT", value: "Pacific/Tahiti" },
  { label: "Pacific/Tarawa +12:00 GMT", value: "Pacific/Tarawa" },
  { label: "Pacific/Tongatapu +13:00 GMT", value: "Pacific/Tongatapu" },
  { label: "Pacific/Wake +12:00 GMT", value: "Pacific/Wake" },
  { label: "Pacific/Wallis +12:00 GMT", value: "Pacific/Wallis" },
  { label: "US/Alaska -08:00 GMT", value: "US/Alaska" },
  { label: "US/Arizona -07:00 GMT", value: "US/Arizona" },
  { label: "US/Central -05:00 GMT", value: "US/Central" },
  { label: "US/Eastern -04:00 GMT", value: "US/Eastern" },
  { label: "US/Hawaii -10:00 GMT", value: "US/Hawaii" },
  { label: "US/Mountain -06:00 GMT", value: "US/Mountain" },
  { label: "US/Pacific -07:00 GMT", value: "US/Pacific" },
  { label: "UTC +00:00 GMT", value: "UTC" },
];
