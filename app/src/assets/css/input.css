.group-relative {
    position: relative;
}

.group-relative .password-eye-icon {
    position: absolute;
    z-index: 1;
    right: 10px;
    top: 17px;
    cursor: pointer;
}

section.private-layout .group-relative .password-eye-icon {
    position: absolute;
    z-index: 1;
    right: 20px;
    top: 36px;
    cursor: pointer;
}
section.candidate-layout .group-relative .password-eye-icon {
    position: absolute;
    z-index: 1;
    right: 20px;
    top: 36px;
    cursor: pointer;
}

.group-relative span.required,
.group-relative p.error {
    color: red !important;
    margin: 0;
    /* margin: 0; */
}

/* .group-relative span.required {
    margin-left: 5px;
} */

.form-control.custom-height-100 {
    height: auto !important;
}

.form-control:disabled,
.form-control.disabled {
    background-color: #e9ecef !important;
}

.group-relative .ant-select {
    height: auto;
    width: 100%;
}

.group-relative .ant-select .ant-select-selector {
    min-height: 38px;
    background-color: #fafafa;
    border-color: #ececec;
    border-radius: 4px;
}

.form-control::placeholder {
    color: var(--placeholder-text-color);
    font-size: 14px;
}

.form-control:focus {
    box-shadow: none !important;
}

input.form-check-input {
    cursor: pointer;
}

.query-40-chars {
    max-width: 40ch; /* Set maximum width to 80 characters (ch) */
    text-overflow: ellipsis;
    overflow: hidden; /* Hide overflowing text */
    white-space: nowrap; /* Prevent wrapping of text */
}

.query-30-chars {
    max-width: 30ch; /* Set maximum width to 80 characters (ch) */
    text-overflow: ellipsis;
    overflow: hidden; /* Hide overflowing text */
    white-space: nowrap; /* Prevent wrapping of text */
}

.group-section .form-group-name {
    margin-top: 5px;
    margin-bottom: 10px;
    font-weight: 600;
}

label.form-label {
    display: flex;
    align-items: center;
    gap: 5px;
}

/* label.form-label svg path {
    fill: #3c6ade;
} */
label.form-label svg path {
    fill: #cfcfcf;
}

.form-control .PhoneInputInput {
    margin: 0;
    border: none;
    outline: none;
    color: var(--theme-heading-color);
    background-color: #fafafa !important;
    background: #fafafa;
    text-align: left;
}

.form-control.PhoneInput {
    background-color: #fafafa !important;
}
textarea {
    min-height: 50px;
    max-height: 400px;
}
.rating-error {
    color: red !important;
}

.mb-3.group-relative.approve-reject-checkbox {
    display: flex;
    gap: 10px;
}
