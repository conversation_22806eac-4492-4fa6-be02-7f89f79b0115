.antd-modal-right-side {
    top: 0;
    right: 0 !important;
    margin: 0 !important;
    padding: 0;
    position: fixed;
    width: 500px !important;
}

.antd-modal-right-side .ant-modal-content {
    padding: 0;
    border-radius: 0;
}

.antd-modal-right-side .ant-form {
    overflow-x: hidden;
    height: calc(100vh - 220px);
    font-family: "Poppins", sans-serif;
    overflow-y: auto;
}

.antd-modal-right-side .ant-modal-footer {
    position: relative;
}

.ant-modal-footer button {
    font-size: 14px;
}

.ant-modal-content .btn-theme,
.ant-modal-content .btn-theme:focus {
    background-color: var(--primary) !important;
    border: 1px solid var(--primary) !important;
    color: #ffffff !important;
}

.ant-modal-content .btn-theme:hover {
    background-color: var(--primary-hover) !important;
    border: 1px solid var(--primary-hover) !important;
    color: #ffffff !important;
}

.ant-modal-content .ant-btn.btn {
    padding: 0px 10px;
    border-radius: 4px;
    box-shadow: none !important;
    min-height: 38px;
    min-width: 70px;
}

.ant-modal-content button.ant-btn-default.btn,
.ant-modal-content button.ant-btn-default.btn:hover {
    color: #fff;
    background: #1e1e1e;
    border-color: #1e1e1e;
}

.ant-modal-content .resend-otp-div,
section.auth .resend-otp-div {
    justify-content: space-between;
    display: flex;
    margin-bottom: 15px;
}

.ant-modal-content .resend-otp-div span,
section.auth .resend-otp-div span {
    color: var(--primary);
    cursor: pointer;
}

.ant-modal-content .resend-otp-div span.disabled,
section.auth .resend-otp-div span.disabled {
    color: #525252;
    cursor: default;
}
@media screen and (max-width: 767px) {
    .ant-modal-content .resend-otp-div,
    .ant-modal-content .resend-otp-div span,
    section.auth .resend-otp-div,
    section.auth .resend-otp-div span {
        color: #fff;
    }

    .ant-modal-content .resend-otp-div span.disabled,
    section.auth .resend-otp-div span.disabled {
        color: #948787;
    }

    .antd-modal-right-side {
        width: 400px !important;
    }
}

.icon-primary {
    border-radius: 5px;
    padding: 5px;
    background-color: #eaeeff;
    color: #4864e2;
}

.edit-icon {
    border-radius: 5px;
    padding: 5px;
    background-color: #eaeeff;
    color: #4864e2;
}

.delete-icon {
    border-radius: 5px;
    padding: 5px;
    background-color: #fee7e7;
    color: #e5242a;
}

.round-icon {
    border-radius: 50%;
}

button.btn-upload-resume,
button.btn-add-manually {
    min-height: 40px;
}

.ant-modal-content {
    padding: 0 !important;
}

.ant-modal-body {
    padding: 0 20px !important;
}

.ant-modal-header,
.ant-modal-footer {
    padding: 15px 20px !important;
}
