* {
    box-sizing: border-box;
}

section.auth img {
    max-width: 100%;
}

section.auth {
    background: #fff;
    height: 100vh;
}

section.auth .content {
    height: 100%;
    padding: 58px 52px;
    background: #fff;
    border-radius: 0;
}

section.auth .row {
    height: 100%;
    margin: 0;
}

section.auth .col {
    padding: 0;
}

section.auth .btn-theme,
section.auth .btn-theme:focus {
    background-color: var(--primary);
    border: 1px solid var(--primary);
}

section.auth .btn-theme:hover {
    background-color: var(--primary-hover);
    border: 1px solid var(--primary-hover);
}

section.auth .content .box h4 {
    font-size: 24px;
    font-weight: 700;
    margin: 0 0 40px;
}

section.auth .content.register .box {
    margin-top: -100px;
}

section.auth .content .box form label {
    margin: 0 0 4px;
    color: #525252;
    font-weight: 500;
}

section.auth .content .box form .form-control {
    border: 1px solid #dadada;
    background: #f5f5f5;
    height: 48px;
    border-radius: 5px;
    outline: none;
    box-shadow: none;
}

section.auth .content .box form button.btn {
    border-radius: 5px;
    height: 48px;
    font-size: 18px;
    width: 100%;
    margin: 20px 0 0;
    background: linear-gradient(90deg, var(--primary) 0%, #d75a33 100%);
    border: none;
}

section.auth .banner-img {
    width: 100%;
    height: 100%;
}

section.auth .error-message {
    color: red;
    font-size: 15px;
}

section.auth .auth-banner {
    background: var(--primary);
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
    padding: 30px !important;
}

section.auth .auth-banner h2 {
    font-size: 32px;
    font-weight: 700;
    line-height: 44px;
    color: #fff;
    text-align: center;
    margin-bottom: 30px;
    max-width: 535px;
}

section.auth .auth-banner h2 span {
    color: var(--text);
}

section.auth .box {
    text-align: center;
}

section.auth .main-logo {
    margin-bottom: 20px;
    height: auto;
}

section.auth .content-box {
    padding: 25px 20px;
    max-width: 400px;
    width: 100%;
}

section.auth .content-box h4 {
    font-size: 28px;
    font-weight: 600;
    line-height: 38.64px;
    text-align: center;
}

section.auth .content-box p {
    font-size: 16px;
    color: #4a5b67;
    font-weight: 400;
    line-height: 20.64px;
    text-align: center;
}

section.auth .content-box form {
    margin-top: 30px;
    text-align: left;
}

section.auth .content-box form p {
    text-align: left;
    /* font-size: 14px; */
}

section.auth .content-box form input.form-control {
    background: #f7f7f7;
    border: 1px solid #f7f7f7;
    /* font-size: 14px; */
    padding: 20px 10px;
}

section.auth .content-box form input.form-control::placeholder {
    color: grey !important;
    opacity: 1; /* Firefox */
}

section.auth .content-box form input.form-control::-ms-input-placeholder {
    color: grey !important;
}

section.auth .content-box form select {
    background: #f7f7f7;
    border: none;
    /* font-size: 13px; */
    padding: 0.7rem 2.25rem 0.7rem 0.75rem;
}

section.auth .content-box form select:focus {
    border: none !important;
    box-shadow: none !important;
}

section.auth .content-box form select:required:invalid {
    color: gray;
}
section.auth .content-box form option[value=""][disabled] {
    display: none;
}
section.auth .content-box form option {
    color: black;
}
section.auth .content-box .btn-primary {
    width: 100%;
    min-height: 42px;
    box-shadow: 0px 4px 10px 0px #00000012;
}

section.auth .bottom-text {
    margin-top: 20px;
}

section.auth .bottom-text span {
    font-size: 16px;
    font-weight: 400;
    line-height: 22.08px;
    text-align: left;
    color: #4a5b67;
}

section.auth .same-line-privacy a,
section.auth .bottom-text span a {
    color: var(--primary);
    text-decoration: none;
}

section.auth .same-line-privacy a:hover,
section.auth .bottom-text span a:hover {
    color: var(--primary-hover);
}

section.auth .same-line-privacy label {
    line-height: normal;
}

section.auth .content-box form label.form-label {
    display: none;
}

section.auth .mobile-logo {
    display: none;
}

section.auth .group-relative input {
    height: 40px;
}

section.auth .same-line-privacy {
    display: flex;
    align-items: center;
}

section.auth .same-line-privacy input {
    outline: none !important;
    border: none !important;
    box-shadow: none !important;
}

.react-datepicker-wrapper {
    width: 100%;
}

@media screen and (max-width: 768px) {
    section.auth .content {
        padding: 58px 35px;
    }

    section.auth .content img {
        max-width: 200px;
    }
}

@media screen and (max-width: 767px) {
    section.auth .row {
        margin: 0;
    }

    section.auth .mobile-bg {
        background-color: var(--primary-hover);
    }

    section.auth .same-line-privacy {
        color: var(--lightbg);
    }

    section.auth .content-box h4,
    section.auth .content-box p,
    section.auth .bottom-text span,
    section.auth .mobile-logo {
        display: inline-block;
    }

    section.auth .bottom-text span,
    section.auth .content-box p {
        font-size: small;
    }
    section.auth .content-box h4 {
        font-size: larger;
    }
    section.auth .desktop-logo {
        display: none;
    }

    section.auth .main-logo {
        margin-bottom: 10px;
    }

    section.auth h4,
    section.auth p,
    section.auth span {
        color: white !important;
    }
}

section.auth img.img-responsive {
    max-width: 100% !important;
    height: auto;
}
