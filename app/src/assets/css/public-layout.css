@import url("https://fonts.googleapis.com/css2?family=Noto+Sans:ital,wght@0,100..900;1,100..900&family=Poppins:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&display=swap");

html {
    scroll-behavior: smooth;
}

* {
    padding: 0;
    margin: 0;
    box-sizing: border-box;
}

body {
    font-family: "Poppins", sans-serif;
    font-style: normal;
    font-size: 16px;
    font-weight: 400;
}

.ant-modal .ant-modal-body,
.ant-btn {
    font-size: 16px;
}

@media screen and (max-width: 1366px) {
    body,
    .ant-modal .ant-modal-body,
    .ant-btn {
        font-size: 14px;
    }
}

/* .btn {
    font-size: 14px;
} */

section.public-layout img {
    max-width: 100%;
}

section.public-layout ul {
    padding: 0;
    margin: 0px 0 0;
    list-style: none;
}

section.public-layout p {
    color: var(--text);
}

section.public-layout a {
    text-decoration: none;
}

section.public-layout .primary {
    color: var(--primary) !important;
}

section.public-layout .secondary {
    color: var(--secondary) !important;
}

section.public-layout .text {
    color: var(--text) !important;
}

section.public-layout .heading {
    color: var(--heading);
}

/*background-color*/
section.public-layout .lightbg {
    background-color: var(--lightbg);
}

section.public-layout .primarybg {
    background-color: var(--primary);
}

section.public-layout .secondarybg {
    background-color: var(--secondary);
}

/*fonts*/
section.public-layout .ft38 {
    font-size: 38px;
}

section.public-layout .ft32 {
    font-size: 32px;
}

section.public-layout .ft20 {
    font-size: 20px;
}

section.public-layout .ft16 {
    font-size: 16px;
}

section.public-layout .ft14 {
    font-size: 14px;
}

/*font-weight*/
section.public-layout .medium {
    font-weight: 600;
}

section.public-layout .semibold {
    font-weight: 700;
}

section.public-layout .bold {
    font-weight: 900;
}

/*button*/
section.public-layout .btn {
    height: 42px !important;
    border-radius: 50px;
    padding: 0 22px;
    font-weight: 500 !important;
    border: none;
    background: white;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    color: var(--theme-primary-color);
    transition: all 0.3s ease;
}

.main-banner .banner-content .btn:hover {
    color: #fff;
    background: var(--primary);
}
.main-banner .banner-content {
    max-width: 818px;
    margin: 0 auto 50px;
}
.main-banner .banner-content p {
    max-width: 580px;
    margin: 0 auto 30px;
}
section.public-layout .btn-primary {
    position: relative;
    z-index: 1;
    background-color: var(--primary) !important;
    color: #fff !important;
    overflow: hidden;
    transition: color 0.3s ease-in-out;
}

section.public-layout .btn-primary::before {
    content: "";
    position: absolute;
    inset: 0;
    background: linear-gradient(
        95.19deg,
        #2ed1a5 9.3%,
        #2ed1a5 43.98%,
        #f2d014 94.17%
    );
    z-index: -1;
    opacity: 0;
    transition: opacity 0.4s ease-in-out;
}
section.public-layout .btn-light {
    background-color: #fff;
    color: var(--primary);
    border: 1px solid #fff;
}
section.public-layout .btn-light:hover {
    background-color: #fff;
    border-color: var(--primary) !important;
    color: var(--primary) !important;
}
section.public-layout .cta-schedule .btn-light:hover {
    background-color: var(--secondary) !important;
    border-color: var(--secondary) !important;
    color: #fff !important;
}
section.public-layout .btn-primary:hover::before {
    opacity: 1;
}

section.public-layout .btn-primary:hover {
    color: #fff !important;
}

section.public-layout .btn-border {
    background-color: #fff !important;
    background: #fff !important;
}

/***** Home Page ******/

/*header*/
section.public-layout .py-80 {
    padding-top: 80px;
    padding-bottom: 80px;
}
section.public-layout .pt-120 {
    padding-top: 170px;
}

section.public-layout header .navbar ul.navbar-nav {
    gap: 32px;
}

section.public-layout .main-navbar li a.btn,
section.public-layout .main-navbar li a.btn:hover {
    color: #000;
    display: flex;
    align-items: center;
    padding: 0 20px;
    font-weight: 500;
}
section.public-layout .main-navbar li a {
    padding-left: 0 !important;
    padding-right: 0 !important ;
}
section.public-layout .main-navbar li a,
section.public-layout .main-navbar li a:hover,
section.public-layout .main-navbar li a.active {
    color: #fff !important;
    font-weight: 400;
    position: relative;
    transition: all 0.3s ease;
}
section.public-layout .main-navbar li a.btn {
    padding: 0 20px !important;
}
/* section.public-layout .main-navbar li a.active::after {
    content: "";
    width: 100%;
    left: 0;
    right: 0;
    bottom: -21px;
    position: absolute;
    height: 3px;
    background: #ffffff;
} */

section.public-layout .main-navbar li a.active {
    font-weight: 500 !important;
}

/*banner*/
section.public-layout .main-banner {
    padding: 160px 0 0 0;
    /* overflow: hidden; */
}

section.public-layout .banner-content h1 {
    font-weight: 600;
    font-size: 40px;
    margin-bottom: 24px;
}
section.public-layout .banner-content a {
    background-color: #fff;
    border-color: #fff;
    color: var(--primary);
}
section.public-layout .banner-content a:hover {
    background-color: #fff;
    border-color: #fff;
    color: var(--primary);
}

/* .banner-vector img:nth-child(2) {
    bottom: -30px;
    right: 60px;
    z-index: 1;
    content: '';
    -webkit-box-shadow: 11px 12px 13px 0px rgb(2 77 100);
    -moz-box-shadow: 11px 12px 13px 0px rgb(2 77 100);
    box-shadow: 11px 12px 13px 0px rgb(2 77 100);
} */
section.public-layout .banner-vector img.img-responsive {
    max-width: 900px !important;
    margin-bottom: -85px;
}
/* section.public-layout .banner-vector img:last-child {
    z-index: 0;
    position: absolute;
    top: 30px;
    left: -200px;
    opacity: 0.8;
} */

/*introduction-section*/
section.public-layout .intro-sec .container h3 {
    font-size: 38px;
}
section.public-layout .intro-sec h4 {
    font-size: 38px;
    color: #101917;
    margin-bottom: 16px;
    font-weight: 600;
}
section.public-layout .intro-sec h4 span {
    color: var(--primary);
}
section.public-layout .pointer-wrapper {
    background: white;
    padding: 20px;
    border-radius: 8px;
    transition: all 0.3s ease;
    box-shadow: 0px 7px 13px 0px rgb(228 234 237);
    -webkit-box-shadow: 0px 7px 13px 0px rgb(228 234 237);
    -moz-box-shadow: 0px 7px 13px 0px rgb(228 234 237);
}

section.public-layout .pointer-wrapper:hover {
    box-shadow: 0px 7px 13px 0px rgb(228 234 237);
    -webkit-box-shadow: 0px 7px 13px 0px rgb(228 234 237);
    -moz-box-shadow: 0px 7px 13px 0px rgb(228 234 237);
}

section.public-layout .pointer-wrapper:hover > .pointer-img {
    background: var(--primary);
    transition: all 0.3s ease;
}

section.public-layout .pointer-wrapper:hover > .pointer-img img {
    filter: invert(1) brightness(9);
}

section.public-layout .pointer-content h4 {
    font-size: 20px;
}

section.public-layout .partners img {
    height: 40px;
    object-fit: contain;
}

section.public-layout .intro-sec .container .row {
    row-gap: 24px;
    margin: 0 -32px;
}
section.public-layout .intro-sec .container .row > div {
    padding: 0 32px;
}

section.public-layout .pointer-img {
    min-width: 48px;
    width: 48px;
    height: 48px;
    border-radius: 30px;
    background: #fff;
    box-shadow: 0px 7px 13px 0px rgb(228 234 237);
    -webkit-box-shadow: 0px 7px 13px 0px rgb(228 234 237);
    -moz-box-shadow: 0px 7px 13px 0px rgb(228 234 237);
}

section.public-layout .pointer-img img {
    width: 32px;
    height: 32px;
}

section.public-layout .section-head h3 {
    font-size: 38px;
    font-weight: 700;
}

/*key-features*/
section.public-layout .feature-card {
    padding: 40px 20px 60px;
    transition: all 0.2s ease-in-out;
    height: 100%;
}

section.public-layout .feature-card .feature-content p {
    margin: 0;
}

section.public-layout .feature-card:hover {
    background: var(--primary);
    transition: all 0.2s ease-in-out;
    box-shadow: 1px 1px 12px rgba(0, 0, 0, 0.24);
}

section.public-layout .feature-card:hover > .feature-content h4,
section.public-layout .feature-card:hover > .feature-content p {
    color: #fff;
}

section.public-layout .features .container .row {
    row-gap: 20px;
}

section.public-layout .feature-card:hover > .feature-img {
    box-shadow: 0px 7px 13px 0px rgb(43 66 167);
    -webkit-box-shadow: 0px 7px 13px 0px rgb(43 66 167);
    -moz-box-shadow: 0px 7px 13px 0px rgb(43 66 167);
}

section.public-layout .feature-img {
    min-width: 96px;
    width: 96px;
    height: 96px;
    border-radius: 50px;
    background-color: white;
    box-shadow: 0px 7px 13px 0px rgb(228 234 237);
    -webkit-box-shadow: 0px 7px 13px 0px rgb(228 234 237);
    -moz-box-shadow: 0px 7px 13px 0px rgb(228 234 237);
}

section.public-layout .feature-img img {
    width: 48px;
    height: 48px;
}

section.public-layout .feature-content h4 {
    font-size: 20px;
    font-weight: 600;
    margin: 0 0 15px;
}

section.public-layout .common-heading h5 {
    font-size: 20px;
    font-weight: 500;
}

section.public-layout .common-heading {
    margin: 0 0 50px;
}

section.public-layout .common-heading p {
    margin: 0;
}

section.public-layout .common-heading h4 {
    font-size: 38px;
    margin: 0 0 16px;
}

/*Work-process*/
section.public-layout .process-icon {
    width: 40px;
    height: 40px;
}

section.public-layout .process-icon img {
    z-index: 1;
    transition: all 0.3s ease;
}

section.public-layout .process-icon::before {
    content: "";
    position: absolute;
    width: 32px;
    height: 32px;
    background: #e7ebff;
    opacity: 0.7;
    border-radius: 30px;
    z-index: 0;
    right: -10px;
    bottom: -10px;
    transition: all 0.3s ease;
}

section.public-layout .work-process .container .row {
    row-gap: 24px;
}

section.public-layout .work-process-card {
    padding: 20px;
    border: 1px solid #e3f3ee;
    background-color: #fff;
    transition: all 0.3s ease;
    height: 100%;
}

section.public-layout .work-process-card:hover {
    background: var(--primary);
}

section.public-layout .work-process-card:hover > .process-icon img {
    filter: invert(1);
}

section.public-layout .work-process-card:hover > .process-icon::before {
    background: #637be5;
}

section.public-layout .work-process-card:hover > .process-content h4,
section.public-layout .work-process-card:hover > .process-content p {
    color: #fff;
}

section.public-layout .work-process-card p.steps {
    font-size: 80px;
    color: #f5f5f5;
    position: absolute;
    top: -10px;
    right: 25px;
    width: 85px;
    height: 40px;
    font-weight: 500;
    transition: all 0.3s ease;
}
section.public-layout .pt-80 {
    padding-top: 80px;
}

section.public-layout .work-process-card:hover > p.steps {
    color: #637be5;
}

section.public-layout .process-vector-wrap-one {
    position: relative;
    border-radius: 0 40px 40px 40px;
    border-bottom: 10px solid var(--primary);
    padding: 40px 0;
}

section.public-layout .process-vector-wrap-two {
    position: relative;
    padding: 40px 0;
}

section.public-layout .process-vector-wrap-one {
    background: #f9fffd;
    border-radius: 0 40px 40px 40px;
    border-bottom: 10px solid var(--primary);
}

section.public-layout .process-vector-wrap-two {
    border-radius: 0 40px 40px 40px;
    background: #fff;
    border-bottom: 10px solid var(--primary);
}

section.public-layout .process-vector-wrap-one span,
section.public-layout .process-vector-wrap-two span {
    width: 70px;
    height: 70px;
    border-radius: 40px;
    position: absolute;
    background: #000;
    color: #fff;
    font-size: 24px;
    font-weight: 500;
    content: "";
}

section.public-layout .process-vector-wrap-one span {
    right: -30px;
    top: 35%;
}

section.public-layout .process-bg {
    background: #f5f7ff;
    padding: 50px 0;
}

section.public-layout .process-vector-wrap-two span {
    left: -30px;
    top: 35%;
}

section.public-layout .process-content-wrap h4 {
    font-size: 28px;
    font-weight: 600;
    color: var(--heading);
}

section.public-layout .process-content-wrap {
    padding: 0 40px;
}

/*FAQ*/
section.public-layout
    .faq-accordian.accordion
    .accordion-item
    .accordion-header
    .accordion-button,
section.public-layout .accordion-body {
    background: #f4f4f4;
    border: none;
    box-shadow: none;
}

section.public-layout .faq-accordian .accordion-button::after {
    width: 25px;
    height: 25px;
    background-size: 25px;
}

section.public-layout
    .faq-accordian.accordion
    .accordion-item
    .accordion-header
    .accordion-button {
    font-size: 18px;
    font-weight: 500;
    border-radius: 10px;
    color: var(--heading);
}
section.public-layout
    .faq-accordian.accordion
    .accordion-item
    .accordion-header
    .accordion-button[aria-expanded="true"] {
    border-radius: 10px 10px 0 0;
}
section.public-layout .show .accordion-body {
    border-radius: 0 0 10px 10px;
}
section.public-layout
    .faq-accordian.accordion
    .accordion-item
    .accordion-header
    .accordion-button:not(.collapsed)::after {
    background-image: url("/images/accordian/accordian-close.svg");
}

section.public-layout
    .faq-accordian.accordion
    .accordion-item
    .accordion-header
    .accordion-button::after {
    background-image: url("/images/accordian/accrodian-open.svg");
}

/*Call to action*/
section.public-layout .cta-section {
    margin-bottom: -145px;
    position: relative;
}

section.public-layout .cta-wrapper {
    padding: 60px 40px;
    background-size: cover;
    position: relative;
    z-index: 9;
    background-image: url("/images/home/<USER>/cta-bg.png");
}

section.public-layout .cta-wrapper h4 {
    font-size: 38px;
    font-weight: 600;
}

/*footer*/
section.public-layout .footer-menu li a {
    padding: 0 20px;
    color: var(--heading);
}

section.public-layout .footer-menu li a:hover {
    color: var(--primary);
}

section.public-layout .social-media li a {
    width: 30px;
    height: 30px;
    background: var(--primary);
    display: flex;
    justify-content: center;
    border-radius: 4px;
    align-items: center;
    transition: all 0.2s ease-in-out;
}

section.public-layout .social-media li a:hover {
    background: var(--heading);
    transition: all 0.2s ease-in-out;
}

section.public-layout .copyright-content p {
    color: #949c9b;
}

section.public-layout .footer {
    padding: 140px 0 0;
}

section.public-layout .partners .row {
    row-gap: 40px;
}

/***************About us page*************/

section.public-layout .beief-intro {
    background: #edf0ff;
}

section.public-layout .beief-intro .container p {
    font-size: 20px;
    font-weight: 500;
}

section.public-layout .about-pointers li {
    padding-left: 30px;
    margin-bottom: 15px;
}

section.public-layout .about-pointers li::before {
    position: absolute;
    content: "";
    width: 24px;
    height: 24px;
    top: 0;
    left: 0;
    background: url("/images/checkicon.svg");
}

section.public-layout .workprocess-content {
    border: 1px solid var(--primary);
    padding: 36px 20px;
    transition: all 0.3s ease;
}

section.public-layout .pointers-list ul li {
    padding-left: 30px;
    margin-bottom: 15px;
}
section.public-layout .pointers-list ul li::before {
    position: absolute;
    content: "";
    width: 24px;
    height: 24px;
    top: 0;
    left: 0;
    background: url("/images/home/<USER>") no-repeat;
    background-size: contain;
    background-position: center;
}
section.public-layout .dashboard-features-wrap .pointers-list ul {
    grid-template-columns: repeat(2, 1fr);
    gap: 6px;
}

/*About Us*/

section.public-layout section.inner-banner {
    min-height: 315px;
    display: flex;
    align-items: center;
    /* background: url("/images/inner-page-pattern.png") var(--secondary); */
    /* background: var(--primary); */
    background-size: cover;
    background-position: bottom;
    position: relative;
}

section.public-layout header.darkHeader {
    background-color: var(--primary);
}
section.public-layout header.darkHeader .btn {
    background-color: #fff !important;
    color: var(--primary) !important;
}
section.public-layout header {
    transition: all 0.3s ease;
}

section.public-layout .public-layout header::after {
    border-bottom: 1px solid #ffffff1f;
    content: "";
    position: absolute;
    width: 100%;
    max-width: 1300px;
    margin: auto;
    left: 0;
    right: 0;
    bottom: 0;
}

/*Privacy Policy*/
section.public-layout .privacy-section .content h5 {
    color: #1c1616;
    font-size: 20px;
    font-weight: 700;
    margin: 0 0 5px;
}

section.public-layout .privacy-section .content p,
section.public-layout .privacy-section .content li {
    color: #4a5b67;
}

section.public-layout .privacy-section .content ul {
    margin: 15px 0 0;
    padding: 0 0 0 20px;
    list-style: disc;
}

section.public-layout .privacy-section .content ul li {
    margin: 0 0 15px;
}

section.public-layout .privacy-section .content ul li:last-child {
    margin: 0;
}

section.public-layout .privacy-section .content ul li strong {
    font-weight: 700;
    color: #4a5b67;
}

section.public-layout .z-9 {
    z-index: 99;
}

/* Contact Us */

section.public-layout .contact-wrap ul.contact-details .box {
    display: flex;
    gap: 25px;
    margin: 0 0 20px;
}

section.public-layout .contact-wrap ul.contact-details .box .content {
    padding: 0 !important;
}

section.public-layout .contact-wrap ul.contact-details .box .content h5 {
    font-weight: 600;
}

section.public-layout .contact-wrap ul.contact-details .box .content p {
    margin: 0;
    color: #949c9b;
}

section.public-layout .contact-wrap ul.contact-details .box:last-child {
    margin: 0;
}

section.public-layout .contact-wrap .card {
    padding: 40px;
    border-radius: 20px;
    border-color: #cddde3;
}

section.public-layout .contact-wrap .card h3 {
    font-size: 22px;
    font-weight: 600;
}

section.public-layout .contact-wrap .card p {
    margin: 0;
}

section.public-layout .contact-wrap .card form {
    margin: 25px 0 0;
}

section.public-layout .contact-wrap .card form .form-control {
    background: #f5fdff;
    border-color: #e3f1f4;
    min-height: 42px;
    box-shadow: none;
}

section.public-layout .contact-wrap .card form textarea.form-control {
    min-height: 100px;
}

section.public-layout .map {
    margin-bottom: -260px;
}
section.public-layout header .navbar {
    transition: all 0.3s ease-in-out 0s;
    padding: 10px 0 !important;
}
section.public-layout header .navbar ul.navbar-nav li {
    display: flex;
    align-items: center;
}

section.public-layout .mx-w60 {
    max-width: 60px !important;
}

section.public-layout img.img-responsive {
    max-width: 100% !important;
    height: auto;
}

section.public-layout .privacy-section strong {
    font-weight: 600;
}

section.public-layout .privacy-section ul {
    list-style: disc !important;
    padding-left: 18px !important;
}
section.public-layout .shape1 {
    left: 0;
    right: 0;
    top: 50px;
}
section.public-layout .shape2 {
    bottom: 30%;
}
section.public-layout .ats-boxes {
    gap: 20px;
}
section.public-layout .boxes-inner {
    padding: 1px;
    border-radius: 10px;
    background-image: linear-gradient(
        180deg,
        var(--primary) 0%,
        rgba(46, 209, 165, 0) 100%
    );
}
section.public-layout .contactIcon {
    width: 56px;
    height: 56px;
    background: var(--primary);
    border-radius: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
}
section.public-layout .box-Items {
    background-color: #fff;
    padding: 24px;
    display: flex;
    gap: 24px;
    border-radius: 10px;
    position: relative;
}

section.public-layout .box-Items::before {
    content: "";
    position: absolute;
    inset: 0;
    background-image: linear-gradient(
        180deg,
        var(--primary) 0%,
        rgba(46, 209, 165, 0) 100%
    );
    opacity: 0;
    transition: opacity 0.4s ease-in-out;
    z-index: 0;
    border-radius: 10px;
}

section.public-layout .box-Items:hover::before {
    opacity: 1;
}

section.public-layout .box-Items > * {
    position: relative;
    z-index: 1; /* Ensure content is above gradient */
}
section.public-layout .boxicon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 48px;
    height: 48px;
    border-radius: 100%;
    min-width: 48px;
    background: #fff;
    box-shadow: 0px 10px 24px 0px #0000000f;
}
section.public-layout .cta-schedule-inner {
    border-radius: 16px;
    overflow: hidden;
    /* background-color: #26B18B; */
    background: linear-gradient(180deg, #2ed1a5 0%, #26b18b 100%);
    display: flex;
    align-items: center;
}
section.public-layout .CtaImg {
    max-width: 550px;
    height: 350px;
    width: 100%;
    position: relative;
    z-index: 1;
}
section.public-layout .CtaImg::before {
    max-width: 490px;
    height: 350px;
    background-color: #19735b;
    width: 100%;
    position: absolute;
    left: -25px;
    border-radius: 0 0 0 372px;
    z-index: -1;
    content: "";
}
section.public-layout .CtaImg img {
    object-fit: cover;
    width: 100%;
    border-radius: 0 0 0 372px;
    height: 100%;
}
section.public-layout .CtaContent {
    padding: 60px;
}
section.public-layout .Feature-Section {
    padding: 100px 0;
}
section.public-layout .Feature-Section h4 {
    max-width: 1020px;
    margin: 0 auto 60px;
}
section.public-layout .feature-box {
    display: flex;
    flex-direction: column;
    gap: 100px;
}

section.public-layout .feature-box ul {
    display: flex;
    flex-direction: column;
    gap: 16px;
    padding-left: 35px !important;
}
section.public-layout .feature-box ul li {
    position: relative;
    font-weight: 600;
}
section.public-layout .feature-box ul li::before {
    content: "";
    position: absolute;
    left: -35px;
    top: -1px;
    width: 24px;
    height: 24px;
    background: url("../images/checkcircle.svg") no-repeat center center;
    background-size: contain;
}
section.public-layout .feature-box-inner:nth-child(odd) {
    flex-direction: row-reverse;
}
section.public-layout .img-box {
    width: 100%;
    overflow: hidden;
    background-color: gray;
    position: relative;
    border-radius: 10px 100px 10px 10px;
    box-shadow: 0px 4px 12px 0px #00000014;
}

section.public-layout .overlay-link-icon-outer {
    display: inline-flex;
    position: absolute;
    width: 80px;
    height: 80px;
    align-items: center;
    justify-content: center;
    right: 0;
    top: 0;
    background-color: #fff;
    z-index: 1;
    border-radius: 0 0 0 30px;
}
section.public-layout .overlay-link-icon {
    display: inline-flex;
    width: 65px;
    height: 65px;
    border-radius: 100%;
    top: -16px;
    right: -16px;
    align-items: center;
    justify-content: center;
    background: #02263b;
}
section.public-layout .overlay-link-icon img {
    transition: all 0.8s ease;
}
section.public-layout .ft-box:hover .overlay-link-icon img {
    transform: rotate(-180deg);
}
section.public-layout .shape-top {
    top: 0;
    left: -30px;
    background-color: #fff;
    position: absolute;
    transform: rotate(180deg);
    width: 30px;
    height: 30px;
    clip-path: path("M0 0 Q0,30 30,30 L 0 30 Z");
}
section.public-layout .shape-bottom {
    background-color: #fff;
    width: 30px;
    height: 30px;
    clip-path: path("M0 0 Q0,30 30,30 L 0 30 Z");
    bottom: -30px;
    transform: rotate(180deg);
    position: absolute;
    right: 0;
}
section.public-layout .img-box::before {
    content: "";
    width: 40px;
    height: 40px;
    border-radius: 100%;
    z-index: 0;
    position: absolute;
    background: var(--secondary);
    top: 0;
    left: calc(100% - 40px);
    transform: scale3d(1, 1, 1);
    transform-style: preserve-3d;
    transition: all 0.8s ease;
}
section.public-layout .ft-box:hover .img-box:before {
    transform: scale3d(45, 45, 1);
    will-change: transform;
}
section.public-layout .overlay-feature-text {
    height: 100%;
    display: flex;
    flex-direction: column;
    position: absolute;
    left: 0;
    right: 0;
    top: 100%;
    align-items: center;
    justify-content: center;
    transition: all 0.5s ease-in-out 0s;
}
section.public-layout .overlay-feature-inner {
    max-width: 435px;
}
section.public-layout .ft-box:hover .overlay-feature-text {
    top: 0;
    transition: all 1s ease-in-out 0s;
}
section.public-layout .feature-box-inner.row {
    margin: 0 -32px;
}
section.public-layout .feature-box-inner.row > div {
    padding: 0 32px;
}
section.public-layout header.darkHeader nav.navbar {
    padding: 0 !important;
}
section.public-layout .py-100 {
    padding: 100px 0;
}
section.public-layout .mt-50 {
    margin-top: 50px;
}
section.public-layout .textOverlay {
    position: absolute;
    top: 0;
    right: 0;
    left: 0;
    bottom: 0;
    font-size: 24px;
    display: flex;
    align-items: center;
    padding-bottom: 10px;
    justify-content: center;
    font-weight: 500;
}
section.public-layout .shape-box img {
    transition: all 0.3s ease-in-out 0s;
}
section.public-layout .work-box:hover .shape-box img {
    transform: rotate(20deg);
}
section.public-layout .work-box {
    position: relative;
}
section.public-layout .work-box::after {
    content: url("../images/arrow-shape.svg");
    position: absolute;
    right: -27px;
    top: 100px;
}
section.public-layout .work-box:last-child:after {
    display: none;
}
section.public-layout .work-box:nth-child(even)::after {
    content: url("../images/arrow-shape-rotate.svg");
    top: 95px;
}
section.public-layout .plan-tabs-btn {
    background: #f4f4f4;
    border-radius: 10px;
    margin-bottom: 60px;
}
section.public-layout .plan-tabs-btn button {
    background: transparent;
    border: none;
    color: #242d2b;
    padding: 0 35px;
    height: 42px;
    border-radius: 10px;
}
section.public-layout .plan-tabs-btn button.active {
    box-shadow: 0px 4px 12px 0px #0000001f;
    font-weight: 500;
    font-size: 18px;
    background-color: #fff;
}
section.public-layout .fixed-plan {
    padding: 40px 20px;
    border-radius: 8px;
    display: flex;
    flex-direction: column;
    height: 100%;
    align-items: flex-start;
    position: relative;
    overflow: hidden;
    justify-content: center;
}
section.public-layout .fixed-plan p {
    color: #ffffffb2;
}
section.public-layout .ft24 {
    font-size: 24px;
}
section.public-layout .fixed-plan h4 {
    max-width: 260px;
}
section.public-layout .plan-card {
    padding: 32px 20px;
    border: 1px solid #d3d3d3;
    border-radius: 8px;
    height: 100%;
    position: relative;
}
section.public-layout .plan-card:hover {
    background: #eeeeee47;
}
section.public-layout .plan-card h6 {
    font-size: 28px;
    margin-bottom: 8px;
    font-weight: 600;
}
section.public-layout .plan-card h2 {
    margin-bottom: 20px;
    display: inline-flex;
    width: 100%;
    align-items: center;
    gap: 10px;
    margin-top: 5px;
}
section.public-layout .plan-card h2 span {
    font-size: 16px;
    color: #8d8d8d;
    font-weight: 400;
}
section.public-layout .plan-card ul {
    display: flex;
    flex-direction: column;
    gap: 16px;
    margin-top: 24px !important;
}
section.public-layout .plan-card ul li {
    padding-left: 40px;
    position: relative;
}
section.public-layout .plan-card ul li::before {
    content: "";
    position: absolute;
    left: 0;
    top: 0;
    width: 24px;
    height: 24px;
    background: url("../images/star.svg") no-repeat center center;
    background-size: contain;
}
section.public-layout .plan-popular {
    border-color: #f8da30;
}
section.public-layout .popular-tag {
    background: linear-gradient(135deg, #f8da30 14.64%, #fdab22 85.36%);
    padding: 0 24px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    height: 34px;
    border-radius: 35px;
    gap: 10px;
    color: #000102;
    font-weight: 500;
    position: absolute;
    left: 0;
    right: 0;
    margin: auto;
    max-width: 182px;
    top: -16px;
}
section.public-layout .popular-tag img {
    filter: invert(0) brightness(00);
}
section.public-layout .planShape1 {
    top: -45px;
    left: -46px;
    transform: rotate(180deg);
    pointer-events: none;
}
section.public-layout img.img-responsive.planShape2 {
    bottom: 30px;
    right: 0;
    max-width: 354px !important;
    pointer-events: none;
}
section.public-layout .faq-accordian {
    max-width: 880px;
    margin: 0 auto;
}
section.public-layout .footer {
    box-shadow: none;
    background-color: #f4f4f4;
}
section.public-layout .max-550 {
    max-width: 550px;
    margin: 0 auto 20px;
}
section.public-layout .copyright-content {
    border-top: 1px solid rgba(0, 103, 135, 0.3);
}

section.public-layout .footer {
    box-shadow: none;
    background-color: #f4f4f4;
    padding-top: 210px;
}
section.public-layout .bottomlayer {
    object-fit: cover;
    position: relative;
    top: -1px;
    height: auto;
}
section.public-layout .pt-140 {
    padding-top: 140px;
}
section.public-layout .how-it-work {
    padding-bottom: 30px;
}
section.public-layout .bottomlayer1 {
    object-fit: cover;
    position: absolute;
    bottom: 0;
    height: auto;
    background-color: #fff;
    z-index: 1;
}
section.public-layout .banner-vector {
    position: relative;
    z-index: 2;
}
section.public-layout .feature-box-inner {
    row-gap: 30px;
}
section.public-layout .plan-section .row {
    row-gap: 30px;
}

@media screen and (max-width: 1280px) {
    section.public-layout .banner-content h1 {
        font-size: 36px;
    }

    section.public-layout header .navbar ul.navbar-nav {
        gap: 20px;
    }
}

@media screen and (max-width: 1024px) {
    section.public-layout .features .container .row {
        row-gap: 20px;
    }
}

@media screen and (max-width: 991px) {
    section.public-layout .section.inner-banner {
        min-height: 280px !important;
    }
    section.public-layout .navbar-toggler {
        background-color: #fff !important;
    }
    section.public-layout .key-feature-one .container .row,
    section.public-layout .key-feature-two .container .row,
    section.public-layout .contact-wrap .container .row {
        row-gap: 24px;
    }
    section.public-layout .key-feature-one .container .row {
        flex-direction: column-reverse;
    }

    section.public-layout .navbar-collapse.collapse.show {
        background: var(--primary);
        border-radius: 10px;
    }
    section.public-layout .main-navbar.navbar-nav .nav-item .nav-link {
        padding: 8px 20px !important;
    }
    section.public-layout .main-navbar li a.btn {
        justify-content: center;
        margin: 0 20px 10px;
    }
    section.public-layout .process-vector-wrap-one span,
    section.public-layout .process-vector-wrap-two span {
        width: 50px;
        height: 50px;
        font-size: 20px;
    }
    section.public-layout .process-vector-wrap-one span,
    section.public-layout .process-vector-wrap-two span {
        top: 40%;
    }
    section.public-layout .process-content-wrap {
        padding: 0 20px;
    }
    section.public-layout .process-vector-wrap-one img,
    section.public-layout .process-vector-wrap-two img {
        width: 80%;
    }
    section.public-layout .process-content-wrap h4 {
        font-size: 24px;
    }
    section.public-layout .common-heading h4,
    section.public-layout .cta-wrapper h4 {
        font-size: 32px;
    }
    section.public-layout .footer-menu li a {
        padding: 0 6px;
    }
    section.public-layout .working-process .common-heading {
        margin: 0 60px 50px;
    }
    section.public-layout .cta-wrapper {
        padding: 40px;
    }
    section.public-layout .intro-sec .container .row {
        margin: 0;
    }
    section.public-layout .intro-sec .container .row > div {
        padding: 0 15px;
    }
    section.public-layout .work-box::after {
        display: none;
    }
    section.public-layout .ft38 {
        font-size: 28px;
    }
    section.public-layout .CtaContent {
        padding: 30px;
    }
    section.public-layout .pt-140 {
        padding-top: 60px;
    }
    section.public-layout .py-100 {
        padding: 60px 0;
    }
    section.public-layout .how-it-work {
        padding-bottom: 30px;
    }
    section.public-layout .box-Items {
        padding: 10px;
        gap: 12px;
    }
    section.public-layout .feature-box {
        gap: 50px;
    }
    section.public-layout .feature-box-inner.row {
        margin: 0;
    }
    section.public-layout .feature-box-inner.row > div {
        padding: 0 15px;
    }
    section.public-layout .plan-section .row .col-md-4 {
        width: 50%;
    }
}

@media screen and (max-width: 820px) {
    section.public-layout section.intro-sec .row {
        row-gap: 40px;
    }

    section.public-layout .banner-vector img:first-child {
        position: relative;
    }

    section.public-layout .features .container .row {
        row-gap: 20px;
    }

    section.public-layout .feature-card {
        padding: 20px 20px 40px;
    }

    section.public-layout .key-feature-one-img,
    section.public-layout .key-feature-two-img {
        text-align: center;
    }

    section.public-layout .key-feature-one-img {
        margin-bottom: 40px;
    }

    section.public-layout .footer-menu li a {
        padding: 0 6px;
    }

    section.public-layout .key-feature-one .container .row {
        flex-direction: column-reverse;
    }

    section.public-layout .key-feature-one .container .row {
        row-gap: 20px;
    }
}

@media screen and (max-width: 767px) {
    section.public-layout .plan-section .row .col-md-4 {
        width: 100%;
    }
    section.public-layout .plan-tabs-btn {
        margin-bottom: 30px;
    }
    section.public-layout .cta-wrapper {
        padding: 20px;
    }
    section.public-layout .cta-schedule-inner {
        flex-direction: column-reverse;
        flex-wrap: wrap;
    }
    section.public-layout .CtaImg::before {
        display: none;
    }
    section.public-layout .CtaImg img {
        border-radius: 0;
    }
    section.public-layout .CtaImg {
        height: auto;
    }
}
@media screen and (max-width: 567px) {
    section.public-layout .overlay-feature-text {
        display: none;
    }
    section.public-layout .img-box::before {
        display: none;
    }

    section.public-layout .overlay-link-icon-outer {
        height: 60px;
        width: 60px;
    }
    section.public-layout .overlay-link-icon {
        width: 45px;
        height: 45px;
    }
    section.public-layout .btn {
        font-size: 14px;
        padding: 0 15px;
    }
    section.public-layout .Feature-Section {
        padding: 50px 0;
    }
    section.public-layout .ft20 {
        font-size: 16px;
    }
    section.public-layout {
        font-size: 14px;
    }

    section.public-layout .main-banner {
        padding: 100px 0 0;
    }

    section.public-layout .banner-content h1 {
        font-size: 30px;
    }
    section.public-layout .main-banner .banner-content p {
        margin: 0 auto 25px !important;
    }

    section.public-layout .py-80.intro-sec {
        padding-top: 110px;
    }
    section.public-layout .intro-sec h4 {
        font-size: 28px;
    }
    section.public-layout .features .container .row {
        row-gap: 20px;
    }

    section.public-layout .banner-vector img:first-child {
        position: relative;
    }

    section.public-layout .common-heading h4 {
        font-size: 30px;
        font-weight: 600;
    }

    section.public-layout .feature-card {
        padding: 20px 20px 40px;
    }

    section.public-layout .section-head h3 {
        font-size: 30px;
        font-weight: 700;
    }

    section.public-layout .about-content {
        margin-bottom: 30px;
    }

    section.public-layout .pointer-wrapper {
        flex-wrap: wrap;
    }

    section.public-layout .feature-img {
        min-width: 80px;
        width: 80px;
        height: 80px;
    }

    section.public-layout .feature-img img {
        width: 40px;
        height: 40px;
    }

    section.public-layout .partners-wrap .row .col-sm-12 {
        margin-bottom: 40px;
    }

    section.public-layout .py-80 {
        padding-top: 50px;
        padding-bottom: 50px;
    }

    section.public-layout .cta-wrapper ul li:first-child {
        margin-bottom: 20px;
    }

    section.public-layout .cta-wrapper ul {
        margin-bottom: 0;
    }
    section.public-layout .flex-box {
        grid-template-columns: repeat(1, 1fr);
    }
    section.public-layout .flex-box .box::after {
        display: none;
    }
    section.public-layout section.inner-banner {
        min-height: 200px;
    }
    section.public-layout .ft32 {
        font-size: 26px;
    }
    section.public-layout .cta-wrapper h4 {
        font-size: 28px;
    }

    section.public-layout
        .faq-accordian.accordion
        .accordion-item
        .accordion-header
        .accordion-button,
    section.public-layout .beief-intro .container p {
        font-size: 16px;
    }
    section.public-layout .workprocess-content {
        z-index: 1;
    }

    section.public-layout .flex-box {
        margin-bottom: 50px;
    }

    section.public-layout .flex-box {
        gap: 90px;
    }
    section.public-layout .dashboard-features-wrap .pointers-list ul {
        grid-template-columns: repeat(1, 1fr);
    }
    section.public-layout .working-process .common-heading {
        margin: 10px;
    }
    section.public-layout .process-vector-wrap-one,
    section.public-layout .process-vector-wrap-two {
        margin: 0 20px 40px;
    }
    section.public-layout .process-vector-wrap-one span,
    section.public-layout .process-vector-wrap-two span {
        top: -25px;
        left: 20px;
    }
    section.public-layout .process-bg .container .row {
        flex-direction: column-reverse;
    }
    section.public-layout .pt-80 {
        padding-top: 40px;
    }

    section.public-layout .process-bg {
        padding: 30px 0;
    }
}

@media screen and (max-width: 320px) {
    section.public-layout .navbar-brand img {
        max-width: 220px;
        width: 100%;
    }

    section.public-layout .banner-content h1,
    section.public-layout .common-heading h4 {
        font-size: 28px;
    }

    section.public-layout
        .faq-accordian.accordion
        .accordion-item
        .accordion-header
        .accordion-button {
        font-size: 16px;
    }

    section.public-layout .cta-wrapper h4 {
        font-size: 28px;
    }

    section.public-layout .cta-wrapper {
        padding: 20px;
    }

    section.public-layout .cta-wrapper p.mb-5 {
        margin-bottom: 20px !important;
    }

    section.public-layout .py-80 {
        padding-top: 40px;
        padding-bottom: 40px;
    }

    section.public-layout .feature-content h4 {
        font-size: 18px;
        line-height: 26px;
    }
    section.public-layout .flex-box {
        margin-bottom: 50px;
    }

    section.public-layout .flex-box {
        gap: 90px;
    }
    section.public-layout .workprocess-icon {
        width: 90px;
        height: 90px;
    }

    section.public-layout .workprocess-icon::before {
        width: 140px;
        height: 140px;
    }

    section.public-layout .workprocess-icon::after {
        width: 200px;
        height: 200px;
    }
    section.public-layout .banner-content h1,
    section.public-layout .common-heading h4 {
        font-size: 24px;
    }
    section.public-layout .process-vector-wrap-one,
    section.public-layout .process-vector-wrap-two {
        margin: 0 5px 30px;
    }
    section.public-layout .process-content-wrap h4 {
        font-size: 20px;
    }
    section.public-layout .process-vector-wrap-one span,
    section.public-layout .process-vector-wrap-two span {
        width: 40px;
        height: 40px;
    }
    section.public-layout .process-vector-wrap-one span,
    section.public-layout .process-vector-wrap-two span {
        top: -20px;
    }

    section.public-layout .common-heading h5 {
        font-size: 18px;
    }

    section.public-layout .common-heading h4 {
        font-size: 26px;
        line-height: 36px;
    }
}
