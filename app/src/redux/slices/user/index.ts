// slices/userSlice.js

import { createSlice } from "@reduxjs/toolkit";
import { UserInterface } from "@src/redux/interfaces";
import { AppDispatch } from "@src/redux/store";
import { adminUserApi } from "@src/apis/adminApis";
import flashMessage from "@src/components/FlashMessage";
interface UserState {
  rows: UserInterface[]; // Adjust the type according to your user data structure
  limit: number;
  count: number;
  currentPage: number;
}

const initialState: UserState = {
  rows: [],
  limit: 10,
  count: 0,
  currentPage: 1,
};

const userSlice = createSlice({
  name: "user",
  initialState,
  reducers: {
    setUserData: (state, action) => {
      state = { ...state, ...action.payload };
      return state;
    },
    updateUserDetail: (state, action) => {
      state.rows = UpdateDetailInRow(state.rows, action.payload);
    },
  },
});

const { setUserData } = userSlice.actions;

export const { updateUserDetail } = userSlice.actions;

/**
 * Updates the detail of a user in the user data array.
 * @param userData Array of user data
 * @param payload Payload containing the updated data and the ID of the user
 * @returns Updated array of user data
 */
const UpdateDetailInRow = (userData: UserInterface[], payload: any) => {
  const detail = payload.data;
  const newUserData = userData.map((user: UserInterface) => {
    if (user.id === payload.id) {
      return { ...user, ...detail };
    }
    return user;
  });
  return newUserData;
};

/**
 * Retrieves all user list from the server and dispatches an action to update the user data in the Redux store.
 * @param params Parameters for fetching user list
 */
export const getAllUserList =
  (params: any) => async (dispatch: AppDispatch) => {
    const { success, ...response } = await adminUserApi.getUsers(params);
    if (success) {
      const { page, rows, count, limit } = response.data;
      await dispatch(
        setUserData({
          rows: rows,
          limit: limit,
          count: count,
          currentPage: page,
        }),
      );
    } else {
      await dispatch(
        setUserData({
          rows: [],
          limit: 10,
          count: 0,
          currentPage: 1,
        }),
      );
    }
  };

/**
 * Updates the status of an existing signature on the server and dispatches an action to update the signature status in the Redux store.
 * Optionally, executes a callback function with the response.
 * @param id The ID of the signature to update.
 * @param status The updated status for the signature.
 * @param callback Optional callback function to execute after updating the signature status.
 */
export const updateUserStatus =
  (
    id: number,
    status: number,
    callback?: (success: boolean, response: any) => void,
  ) =>
  async (dispatch: AppDispatch) => {
    const { success, ...response } = await adminUserApi.updateUserStatus(id, {
      status,
    });
    if (success) {
      await dispatch(updateUserDetail(response.data));
    }
    flashMessage(response.message, success ? "success" : "error");
    callback && callback(success, response);
  };

export default userSlice.reducer;
