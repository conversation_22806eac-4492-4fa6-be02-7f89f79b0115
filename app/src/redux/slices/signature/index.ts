import { PayloadAction, createSlice } from "@reduxjs/toolkit";
import { EmployeeSignatureInterface } from "@src/redux/interfaces";
import { AppDispatch } from "@src/redux/store";
import { eSignatureApi } from "@src/apis/wildcardApis";
import flashMessage from "@src/components/FlashMessage";

interface EmployeeSignatureState {
  rows: EmployeeSignatureInterface[]; // Adjust the type according to your signature data structure
  limit: number;
  count: number;
  currentPage: number;
  signature: null | EmployeeSignatureInterface;
  options: Array<{ value: string | number; label: string }>;
}

const initialState: EmployeeSignatureState = {
  rows: [],
  limit: 10,
  count: 0,
  currentPage: 1,
  signature: null,
  options: [],
};

const signatureSlice = createSlice({
  name: "signature",
  initialState,
  reducers: {
    setEmployeeSignatureData: (state, action) => {
      state = { ...state, ...action.payload };
      return state;
    },
    updateEmployeeSignatureDetail: (state, action) => {
      state.rows = UpdateDetailInRow(state.rows, action.payload);
    },
    setEmployeeSignatureState: (
      state,
      action: PayloadAction<null | EmployeeSignatureInterface>,
    ) => {
      state.signature = action.payload;
    },
    setEmployeeSignatureOptionsState: (
      state,
      action: PayloadAction<Array<{ value: string | number; label: string }>>,
    ) => {
      state.options = action.payload ?? [];
    },
    updateEmployeeSignatureState: (
      state,
      action: PayloadAction<EmployeeSignatureInterface>,
    ) => {
      const index = state.rows.findIndex(
        (signature) => signature.id === action.payload.id,
      );
      if (index !== -1) {
        state.rows[index] = { ...state.rows[index], ...action.payload };
      }
    },
  },
});

const {
  setEmployeeSignatureData,
  setEmployeeSignatureState,
  setEmployeeSignatureOptionsState,
  updateEmployeeSignatureState,
} = signatureSlice.actions;

export const { updateEmployeeSignatureDetail } = signatureSlice.actions;

/**
 * Updates the detail of a signature in the signature data array.
 * @param signatureData Array of signature data
 * @param payload Payload containing the updated data and the ID of the signature
 * @returns Updated array of signature data
 */
const UpdateDetailInRow = (
  signatureData: EmployeeSignatureInterface[],
  payload: any,
) => {
  const detail = payload.data;
  const newEmployeeSignatureData = signatureData.map(
    (signature: EmployeeSignatureInterface) => {
      if (signature.id === payload.id) {
        return { ...signature, ...detail };
      }
      return signature;
    },
  );
  return newEmployeeSignatureData;
};

/**
 * Retrieves all signature list from the server and dispatches an action to update the signature data in the Redux store.
 * @param params Parameters for fetching signature list
 */
export const getAllEmployeeSignatureList =
  (params: any) => async (dispatch: AppDispatch) => {
    const { success, ...response } =
      await eSignatureApi.getEmployeeSignatures(params);
    if (success) {
      const { page, rows, count, limit } = response.data;
      await dispatch(
        setEmployeeSignatureData({
          rows: rows,
          limit: limit,
          count: count,
          currentPage: page,
        }),
      );
    } else {
      await dispatch(
        setEmployeeSignatureData({
          rows: [],
          limit: 10,
          count: 0,
          currentPage: 1,
        }),
      );
    }
  };

/**
 * Retrieves signature detail from the server and dispatches an action to update the signature data in the Redux store.
 * Optionally, executes a callback function with the response.
 * @param id The ID of the signature to fetch.
 * @param callback Optional callback function to execute after fetching the signature detail.
 */
export const getEmployeeSignatureDetail =
  (id: number, callback?: (success: boolean, response: any) => void) =>
  async (dispatch: AppDispatch) => {
    const { success, ...response } =
      await eSignatureApi.getEmployeeSignaturesDetails(id);
    if (success) {
      await dispatch(setEmployeeSignatureState(response.data));
    } else {
      await dispatch(setEmployeeSignatureState(null));
    }
    callback && callback(success, response);
  };

/**
 * Retrieves all signature options from the server and dispatches an action to update the signature options data in the Redux store.
 * @param params Parameters for fetching signature list
 */
export const getEmployeeSignatureOptions =
  (params: any) => async (dispatch: AppDispatch) => {
    const { success, ...response } =
      await eSignatureApi.getEmployeeSignatureOption(params);
    if (success) {
      await dispatch(setEmployeeSignatureOptionsState(response.data));
    } else {
      await dispatch(setEmployeeSignatureOptionsState([]));
    }
  };

/**
 * Updates the status of an existing signature on the server and dispatches an action to update the signature status in the Redux store.
 * Optionally, executes a callback function with the response.
 * @param id The ID of the signature to update.
 * @param status The updated status for the signature.
 * @param callback Optional callback function to execute after updating the signature status.
 */
export const updateEmployeeSignatureStatus =
  (
    id: number,
    status: number,
    callback?: (success: boolean, response: any) => void,
  ) =>
  async (dispatch: AppDispatch) => {
    const { success, ...response } =
      await eSignatureApi.updateEmployeeSignatureStatus(id, { status });
    if (success) {
      await dispatch(updateEmployeeSignatureState(response.data));
    }
    flashMessage(response.message, success ? "success" : "error");
    callback && callback(success, response);
  };

export default signatureSlice.reducer;
